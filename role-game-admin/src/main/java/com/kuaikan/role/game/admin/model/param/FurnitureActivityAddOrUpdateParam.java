package com.kuaikan.role.game.admin.model.param;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity;
import com.kuaikan.role.game.api.bean.FurnitureActivityConfig;
import com.kuaikan.role.game.api.bo.ImageInfo;
import com.kuaikan.role.game.api.enums.FurnitureActivityType;

/**
 * <AUTHOR>
 * @date 2025/4/17 18:12
 */

@Data
@Accessors(chain = true)
public class FurnitureActivityAddOrUpdateParam implements Serializable {

    private static final long serialVersionUID = -8930257144891118432L;

    private Integer id;
    private Integer orderId;
    private String name;
    private Long startAt;
    private Long endAt;
    private Integer type;
    private List<Integer> furnitureGroupIds;
    // 只在常驻活动使用,为true关联当前所有家具套组
    private boolean pickAllFurnitureGroup;
    private ImageInfo navigationImage;
    private ImageInfo backgroundImage;
    private boolean showGuarantee;
    private Integer ruleId;
    private String ruleDescription;
    private List<String> exchangeMall;
    private String reminderBubble;
    private RoleAnimation roleAnimation;
    private boolean firstFree;
    private FirstPrice firstPrice;
    private CumulativeDrawReward cumulativeDrawReward;

    @Data
    @Accessors(chain = true)
    public static class RoleAnimation implements Serializable {

        private static final long serialVersionUID = 2033028036124854980L;
        private Integer furnitureGroupId;
        private Double zoomRatio;
        private List<CostumeBlindBoxActivity.RoleConfig> roleConfigs;
    }

    @Data
    @Accessors(chain = true)
    public static class FirstPrice implements Serializable {

        private static final long serialVersionUID = 6783908275310285483L;
        private Integer kkbPrice;
        private String cornerText;
    }

    @Data
    @Accessors(chain = true)
    public static class CumulativeDrawReward implements Serializable {

        private static final long serialVersionUID = -6316744188401341446L;
        private ImageInfo rewardImage;
        private String rewardText;
        private List<CostumeBlindBoxActivity.Reward> rewards;
    }

    public FurnitureActivityConfig.Config toFurnitureActivityConfig() {
        FurnitureActivityConfig.Config config = new FurnitureActivityConfig.Config();
        config.setNavigationImage(navigationImage);
        config.setBackgroundImage(backgroundImage);
        config.setShowGuarantee(showGuarantee);
        config.setRuleId(ruleId);
        config.setRuleDescription(ruleDescription);
        config.setExchangeMall(exchangeMall);
        if (type == FurnitureActivityType.FURNITURE_ACTIVITY_LIMIT.getCode()) {
            config.setReminderBubble(reminderBubble);
            config.setRoleAnimation(toRoleAnimationConfig(roleAnimation));
            config.setFirstFree(firstFree);
            config.setFirstPrice(toFirstPriceConfig(firstPrice));
            config.setCumulativeDrawReward(toCumulativeDrawRewardConfig(cumulativeDrawReward));
        }
        return config;
    }

    public FurnitureActivityConfig.RoleAnimation toRoleAnimationConfig(RoleAnimation roleAnimation) {
        if (roleAnimation == null) {
            return null;
        }
        FurnitureActivityConfig.RoleAnimation roleAnimationConfig = new FurnitureActivityConfig.RoleAnimation();
        roleAnimationConfig.setFurnitureGroupId(roleAnimation.getFurnitureGroupId());
        roleAnimationConfig.setRoleConfigs(roleAnimation.getRoleConfigs());
        roleAnimationConfig.setZoomRatio(roleAnimation.getZoomRatio());
        return roleAnimationConfig;
    }

    public FurnitureActivityConfig.FirstPrice toFirstPriceConfig(FirstPrice firstPrice) {
        if (firstPrice == null) {
            return null;
        }
        FurnitureActivityConfig.FirstPrice firstPriceConfig = new FurnitureActivityConfig.FirstPrice();
        firstPriceConfig.setKkbPrice(firstPrice.getKkbPrice());
        firstPriceConfig.setCornerText(firstPrice.getCornerText());
        return firstPriceConfig;
    }

    public FurnitureActivityConfig.CumulativeDrawReward toCumulativeDrawRewardConfig(CumulativeDrawReward cumulativeDrawReward) {
        if (cumulativeDrawReward == null) {
            return null;
        }
        FurnitureActivityConfig.CumulativeDrawReward cumulativeDrawRewardConfig = new FurnitureActivityConfig.CumulativeDrawReward();
        cumulativeDrawRewardConfig.setRewardImage(cumulativeDrawReward.getRewardImage());
        cumulativeDrawRewardConfig.setRewardText(cumulativeDrawReward.getRewardText());
        cumulativeDrawRewardConfig.setRewards(cumulativeDrawReward.getRewards());
        return cumulativeDrawRewardConfig;
    }

    public String validateError() {
        if (StringUtils.isBlank(name)) {
            return "活动名称不能为空";
        }
        if (FurnitureActivityType.UNKNOWN.getCode() == FurnitureActivityType.getByCode(type).getCode()) {
            return "活动类型错误";
        }
        if (null == startAt || endAt == null) {
            return "活动时间不能为空";
        }
        if (null == navigationImage) {
            return "活动导航图不能为空";
        }
        if (null == backgroundImage) {
            return "活动背景图不能为空";
        }
        if (null == ruleId) {
            return "活动规则不能为空";
        }
        if (StringUtils.isBlank(ruleDescription)) {
            return "规则说明不能为空";
        }
        if (CollectionUtils.isEmpty(exchangeMall)) {
            return "兑换商城不能为空";
        }
        return null;
    }

}

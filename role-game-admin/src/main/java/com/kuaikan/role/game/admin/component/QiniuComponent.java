package com.kuaikan.role.game.admin.component;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.Map;
import java.util.Optional;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.qiniu.common.Zone;
import com.qiniu.http.Response;
import com.qiniu.processing.OperationStatus;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.UploadManager;
import com.qiniu.util.StringMap;
import com.qiniu.util.UrlSafeBase64;

import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.config.Settings;
import com.kuaikan.third.qiniu.exception.KuaikanQiniuException;
import com.kuaikan.common.exception.KException;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.third.qiniu.exception.DoubleQiniuException;
import com.kuaikan.third.qiniu.exception.KuaikanQiniuException;
import com.kuaikan.third.qiniu.helper.AuthHelper;
import com.kuaikan.third.qiniu.helper.OperationManagerHelper;
import com.kuaikan.third.qiniu.model.RequestParamModel;
import com.kuaikan.third.qiniu.service.QiniuAuth;
import com.kuaikan.third.qiniu.service.QiniuOperationManager;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Slf4j
@Component
public class QiniuComponent {

    @Resource
    private QiniuAuth qiniuAuth;
    @Resource
    private QiniuOperationManager qiniuOperationManager;
    private OperationManagerHelper operationManager;
    private RequestParamModel requestParamModel;
    private AuthHelper authHelper;
    private UploadManager uploadManager;

    public static final String FREE_BUCKET_NAME = Settings.getEnvironment().isProd() ? "kuaikan-data" : "kk-data-stag";
    public static final String PAY_BUCKET_NAME = Settings.getEnvironment().isProd() ? "kuaikan-payment" : "kk-payment-stag";
    /**
     * pfop操作状态查询的时间间隔，毫秒
     */
    private static final int PFOP_CHECK_DURATION = 1000;
    /**
     * pfop操作处理成功
     */
    private static final int PFOP_SUCCESS = 0;
    /**
     * pfop操作等待处理
     */
    private static final int PFOP_WAIT_PROCESS = 1;
    /**
     * pfop操作正在处理
     */
    private static final int PFOP_IN_PROCESSING = 2;

    private static final String BLACK_MIME_TYPE = "!text/css;text/html;text/javascript;text/xml;application/javascript;image/svg+xml";
    private static final long EXPIRE_TIMES = 3600;

    @PostConstruct
    private void init() {
        requestParamModel = new RequestParamModel("role-game", "AyFgZCqhjqqPwfmghspOAYfUitgjQxHt");
        operationManager = new OperationManagerHelper(requestParamModel, qiniuOperationManager);
        authHelper = new AuthHelper(requestParamModel, qiniuAuth);
        Configuration configuration = new Configuration(Zone.autoZone());
        uploadManager = new UploadManager(configuration);
    }

    public String uploadWithStream(String bucket, File file, String key) {
        try {
            InputStream inputStream = Files.newInputStream(file.toPath());
            String token = authHelper.uploadToken(bucket);
            byte[] data = IOUtils.toByteArray(inputStream);
            Response res = uploadManager.put(data, key, token, null, null, false);
            String jsonValue = res.bodyString();
            Map<String, Object> jsonMap = JSON.parseObject(jsonValue, Maps.newHashMap().getClass());
            return (String) jsonMap.get("key");
        } catch (IOException e) {
            log.error("uploadWithStream error", e);
            return "";
        }
    }

    public String uploadWithXmlStream(String bucket, File file, String key) {
        try {
            InputStream inputStream = Files.newInputStream(file.toPath());
            StringMap stringMap = new StringMap();
            stringMap.put("mimeLimit", BLACK_MIME_TYPE);
            String token = authHelper.uploadToken(bucket, key, EXPIRE_TIMES, stringMap);
            byte[] data = IOUtils.toByteArray(inputStream);
            Response res = uploadManager.put(data, key, token, null, null, false);
            String jsonValue = res.bodyString();
            Map<String, Object> jsonMap = JSON.parseObject(jsonValue, Maps.newHashMap().getClass());
            return (String) jsonMap.get("key");
        } catch (IOException e) {
            log.error("uploadWithStream error", e);
            return "";
        }
    }

    public String uploadWithStream(String bucket, String filePath, String key) {
        try {
            InputStream inputStream = new FileInputStream(filePath);
            String token = authHelper.uploadToken(bucket);
            byte[] data = IOUtils.toByteArray(inputStream);
            Response res = uploadManager.put(data, key, token, null, null, false);
            String jsonValue = res.bodyString();
            Map<String, Object> jsonMap = JSON.parseObject(jsonValue, Maps.newHashMap().getClass());
            return (String) jsonMap.get("key");
        } catch (IOException e) {
            log.error("uploadWithStream error", e);
            return "";
        }
    }

    public String uploadWithStream(String bucket, byte[] data, String key) {
        try {
            String token = authHelper.uploadToken(bucket);
            Response res = uploadManager.put(data, key, token, null, null, false);
            String jsonValue = res.bodyString();
            Map<String, Object> jsonMap = JSON.parseObject(jsonValue, Maps.newHashMap().getClass());
            return (String) jsonMap.get("key");
        } catch (IOException e) {
            log.error("uploadWithStream error", e);
            return "";
        }
    }

    public String getPrivateDownloadUrl(String key, long expires) {
        try {
            return authHelper.privateDownloadUrl(key, expires);
        } catch (KuaikanQiniuException e) {
            log.error("privateDownloadUrl error", e);
            return "";
        }
    }

    /**
     * 获取任务的处理状态
     * @param persistId 异步任务唯一标识
     * @return 任务的处理状态
     * @throws DoubleQiniuException 检查过程中失败时抛出
     */
    public OperationStatus getPersistStatus(String persistId) throws DoubleQiniuException {
        return operationManager.prefop(persistId);
    }

    /**
     * 请求后循环检查处理状态
     * @param bucket 源/目的图片所在空间
     * @param sourceKey 原图片key
     * @param targetKey 处理后得到的图片的key
     * @param fops 具体的操作
     * @throws DoubleQiniuException 请求过程中失败时抛出
     * @throws InterruptedException 线程被打断时抛出
     */
    public void syncPfop(String bucket, String sourceKey, String targetKey, String fops) throws DoubleQiniuException, InterruptedException {

        String id = pfop(bucket, sourceKey, targetKey, fops);
        log.info("Send pfop. source={}, target={}, fops={}, persistId={}", sourceKey, targetKey, fops, id);
        while (true) {
            Thread.sleep(PFOP_CHECK_DURATION);
            OperationStatus persistStatus = getPersistStatus(id);
            int code = Optional.ofNullable(persistStatus).map(status -> status.code).orElse(PFOP_WAIT_PROCESS);
            log.info("Pfop operation status. id={}, status={}", id, JsonUtils.writeValueAsString(persistStatus));
            if (code == PFOP_SUCCESS) {
                break;
            } else if (code != PFOP_WAIT_PROCESS && code != PFOP_IN_PROCESSING) {
                throw new KException(ResponseCodeMsg.SYSTEM_ERROR);
            }
        }
    }

    /**
     * 处理图片，原图片和目标图片在同一空间的情况下适用
     * @param bucket 源/目的图片所在空间
     * @param sourceKey 原图片key
     * @param targetKey 处理后得到的图片的key
     * @param fops 具体的操作
     * @return 任务提交后的任务唯一标识
     * @throws DoubleQiniuException 请求过程中失败时抛出
     */
    public String pfop(String bucket, String sourceKey, String targetKey, String fops) throws DoubleQiniuException {
        String encodeTargetInfo = UrlSafeBase64.encodeToString(StringUtils.join(bucket, ":", targetKey));
        String pfops = StringUtils.join(fops, "|saveas/", encodeTargetInfo);
        StringMap params = new StringMap().putNotEmpty("pipeline", "kkpri01");
        return operationManager.pfop(bucket, sourceKey, pfops, params);
    }

}

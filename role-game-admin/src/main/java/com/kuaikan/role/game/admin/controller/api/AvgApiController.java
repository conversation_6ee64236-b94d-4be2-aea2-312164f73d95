package com.kuaikan.role.game.admin.controller.api;

import static com.kuaikan.role.game.admin.biz.AvgBiz.SPINE_TYPE_SET;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Maps;

import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.admin.biz.AvgBiz;
import com.kuaikan.role.game.admin.component.AvgChapterComponent;
import com.kuaikan.role.game.admin.model.dto.DigitalAssetBaseRespDTO;
import com.kuaikan.role.game.admin.converter.AvgGyroscopeMapper;
import com.kuaikan.role.game.admin.converter.AvgHotZoneMapper;
import com.kuaikan.role.game.admin.converter.AvgUIStyleZipFileConverter;
import com.kuaikan.role.game.admin.enums.AvgChapterOrderTypeEnum;
import com.kuaikan.role.game.admin.model.param.AnchorUpdateParam;
import com.kuaikan.role.game.admin.model.param.AvgChapterCreateParam;
import com.kuaikan.role.game.admin.model.param.AvgChapterDeleteParam;
import com.kuaikan.role.game.admin.model.param.AvgChapterUpdateParam;
import com.kuaikan.role.game.admin.model.param.AvgDirSaveOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.AvgFileQueryParam;
import com.kuaikan.role.game.admin.model.param.AvgGyroscopeSaveParam;
import com.kuaikan.role.game.admin.model.param.AvgHotZoneAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.AvgOriginFileSaveOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.AvgUIStyleZipFileCreateParam;
import com.kuaikan.role.game.admin.model.view.AvgDirView;
import com.kuaikan.role.game.admin.model.view.AvgGyroscopeView;
import com.kuaikan.role.game.admin.model.view.AvgHotZoneView;
import com.kuaikan.role.game.admin.model.view.AvgOriginFileView;
import com.kuaikan.role.game.admin.model.view.PageResult;
import com.kuaikan.role.game.admin.repository.AvgRepository;
import com.kuaikan.role.game.admin.utils.ResponseUtils;
import com.kuaikan.role.game.api.apis.AvgApi;
import com.kuaikan.role.game.api.enums.AvgChapterSyncTypeEnum;
import com.kuaikan.role.game.api.bean.AvgChapter;
import com.kuaikan.role.game.api.bean.AvgDir;
import com.kuaikan.role.game.api.bean.AvgGyroscope;
import com.kuaikan.role.game.api.bean.AvgHotZone;
import com.kuaikan.role.game.api.bean.AvgOriginFile;
import com.kuaikan.role.game.api.bean.AvgProject;
import com.kuaikan.role.game.api.enums.AvgFileResourceType;
import com.kuaikan.role.game.api.enums.AvgFileType;
import com.kuaikan.role.game.api.enums.HiddenTypeEnum;
import com.kuaikan.role.game.api.rpc.param.AnchorUpdateRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgChapterCreateRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgChapterDeleteRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgChapterUpdateRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgChapterUploadRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgDirSaveOrUpdateRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgFileQueryRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgGyroscopeSaveRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgHotZoneSaveOrUpdateRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgOriginFileSaveOrUpdateRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgResourceUpdateRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgUIStyleZipFileCreateRpcParam;
import com.kuaikan.role.game.api.rpc.param.BatchQueryAvgParam;
import com.kuaikan.role.game.api.rpc.param.PushDigitalAssetParam;
import com.kuaikan.role.game.api.rpc.param.CheckTopicAvgParam;
import com.kuaikan.role.game.api.rpc.param.CheckTopicAvgRpcParam;
import com.kuaikan.role.game.api.rpc.param.UpdateResourceAvgParam;
import com.kuaikan.role.game.api.rpc.result.AvgChapterModel;
import com.kuaikan.role.game.api.rpc.result.AvgChapterSimpleModel;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/roleGame/avg")
@Slf4j
public class AvgApiController implements AvgApi {

    @Resource
    private AvgBiz avgBiz;

    @Resource
    private AvgChapterComponent avgChapterComponent;

    @Resource
    private AvgRepository avgRepository;

    @Override
    public RpcResult<Map<Integer, AvgChapterModel>> queryByIds(BatchQueryAvgParam batchQueryAvgParam) {
        return RpcResult.success(avgBiz.queryByIds(batchQueryAvgParam));
    }

    @Override
    public RpcResult<Object> pushDigitalAsset(PushDigitalAssetParam pushDigitalAssetParam) {
        if (pushDigitalAssetParam == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "参数不能为空");
        }
        int chapterId = pushDigitalAssetParam.getChapterId();
        AvgChapterSyncTypeEnum typeEnum = AvgChapterSyncTypeEnum.getByCode(pushDigitalAssetParam.getSyncType());
        if (chapterId <= 0 || typeEnum == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "参数错误");
        }
        DigitalAssetBaseRespDTO<Object> pushRes = avgChapterComponent.pushDigitalAsset(chapterId, typeEnum);
        if (pushRes == null || !"200".equals(pushRes.getCode())) {
            return RpcResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "推送数字资产失败");
        }
        return RpcResult.success(pushRes.getData());
    }

    @Override
    public RpcResult<List<AvgChapterSimpleModel>> queryTopicRelatedChapter(int topicId) {
        return RpcResult.success(avgBiz.queryTopicRelatedChapter(topicId));
    }

    // 段落列表
    @Override
    public RpcResult<Map<String, Object>> chapterList(Integer orderTypeId, String segmentName, Integer segmentId, Integer type,
                                                      Integer topicId, int page, int pageSize) {
        AvgChapterOrderTypeEnum orderType = AvgChapterOrderTypeEnum.getByCode(orderTypeId);
        if (orderType == null || topicId <= 0) {
            log.warn("chapterList param error, orderTypeId: {}, topicId: {}", orderTypeId, topicId);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "参数错误");
        }
        return RpcResult.success(ResponseUtils.valueOf(
                avgBiz.queryAvgChapterList(orderType, segmentName, segmentId, topicId, type, page, pageSize)));
    }

    @Override
    public RpcResult<Map<String, Object>> chapterUpload(AvgChapterUploadRpcParam rpcParam) {
        AvgChapter avgChapter = avgRepository.queryAvgChapterByChapterId(rpcParam.getChapterId());
        if (avgChapter == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "章节不存在");
        }
        AvgProject avgProject = avgRepository.queryAvgProjectByObjectId(avgChapter.getProjectId());
        if (avgProject == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "项目不存在");
        }
        if (!Objects.equals(avgProject.getTopicId(), rpcParam.getTopicId())) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        return RpcResult.success(ResponseUtils.valueOf(
                avgBiz.chapterUpload(rpcParam.getAvgChapterTextList(), rpcParam.getChapterId(), rpcParam.getFileName(),
                        rpcParam.getChapterFileKey(), true)));
    }

    @Override
    public RpcResult<Map<String, Object>> updateChapter(AvgChapterUpdateRpcParam rpcParam) {
        AvgChapter avgChapter = avgRepository.queryAvgChapterByChapterId(rpcParam.getChapterId());
        if (avgChapter == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "章节不存在");
        }
        AvgProject avgProject = avgRepository.queryAvgProjectByObjectId(avgChapter.getProjectId());
        if (avgProject == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "项目不存在");
        }
        if (!Objects.equals(avgProject.getTopicId(), rpcParam.getTopicId())) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        AvgChapterUpdateParam avgChapterUpdateParam = new AvgChapterUpdateParam();
        avgChapterUpdateParam.setChapterId(rpcParam.getChapterId());
        avgChapterUpdateParam.setStyleId(rpcParam.getStyleId());
        avgChapterUpdateParam.setName(rpcParam.getName());
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.updateChapter(avgChapterUpdateParam, true)));
    }

    @Override
    public RpcResult<Map<String, Object>> deleteChapter(AvgChapterDeleteRpcParam rpcParam) {
        AvgChapter avgChapter = avgRepository.queryAvgChapterByChapterId(rpcParam.getChapterId());
        if (avgChapter == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "章节不存在");
        }
        AvgProject avgProject = avgRepository.queryAvgProjectByObjectId(avgChapter.getProjectId());
        if (avgProject == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "项目不存在");
        }
        if (!Objects.equals(avgProject.getTopicId(), rpcParam.getTopicId())) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        AvgChapterDeleteParam avgChapterDeleteParam = new AvgChapterDeleteParam();
        avgChapterDeleteParam.setChapterId(rpcParam.getChapterId());
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.deleteChapter(avgChapterDeleteParam, true)));
    }

    @Override
    public RpcResult<Map<String, Object>> createChapter(AvgChapterCreateRpcParam rpcParam) {
        AvgProject avgProject = avgRepository.queryAvgProjectByTopicId(rpcParam.getTopicId());
        if (Objects.isNull(avgProject)) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此专题");
        }
        AvgChapterCreateParam avgChapterCreateParam = new AvgChapterCreateParam();
        avgChapterCreateParam.setProjectId(avgProject.getObjectId());
        avgChapterCreateParam.setType(rpcParam.getType());
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.createChapter(avgChapterCreateParam, true)));
    }

    @Override
    public RpcResult<Map<String, Object>> createFile(AvgOriginFileSaveOrUpdateRpcParam param) {
        AvgOriginFileSaveOrUpdateParam param1 = new AvgOriginFileSaveOrUpdateParam();
        BeanUtils.copyProperties(param, param1);
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.createFile(param1, true)));
    }

    @Override
    public RpcResult<Map<String, Object>> online(int id, int type, int topicId) {
        RpcResult<Void> validation = validateResourcePermission(id, type, topicId);
        if (!validation.isSuccess()) {
            return RpcResult.result(validation.getCode(), validation.getMessage());
        }

        return RpcResult.success(ResponseUtils.valueOf(avgBiz.online(id, type, true)));
    }

    @Override
    public RpcResult<Map<String, Object>> offline(int id, int type, int topicId) {
        RpcResult<Void> validation = validateResourcePermission(id, type, topicId);
        if (!validation.isSuccess()) {
            return RpcResult.result(validation.getCode(), validation.getMessage());
        }

        return RpcResult.success(ResponseUtils.valueOf(avgBiz.offline(id, type, true)));
    }

    @Override
    public RpcResult<Map<String, Object>> avgZipFileDownload(int id, int topicId) {
        AvgDir avgDir = avgRepository.queryAvgDirById(id);
        if (avgDir == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "dir没找到");
        }
        if (avgDir.getResource() == AvgFileResourceType.UNKNOW.getCode()
                || avgDir.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && !Objects.equals(
                avgDir.getConfig().getTopicId(), topicId)) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.avgZipFileDownload(id)));
    }

    @Override
    public RpcResult<Map<String, Object>> checkTopic(CheckTopicAvgRpcParam rpcParam) {
        CheckTopicAvgParam checkTopicAvgParam = new CheckTopicAvgParam();
        checkTopicAvgParam.setType(rpcParam.getType());
        checkTopicAvgParam.setNames(rpcParam.getNames());
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.checkTopic(checkTopicAvgParam)));
    }

    @Override
    public RpcResult<Map<String, Object>> updateAnchor(AnchorUpdateRpcParam rpcParam) {
        AvgOriginFile avgOriginFile = avgRepository.queryAvgFileById(rpcParam.getId());
        if (avgOriginFile == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件不存在");
        }
        if (avgOriginFile.getResource() == AvgFileResourceType.UNKNOW.getCode()
                || avgOriginFile.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && !Objects.equals(
                avgOriginFile.getConfig().getTopicId(), rpcParam.getTopicId())) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        AnchorUpdateParam param = new AnchorUpdateParam();
        param.setAnchor(rpcParam.getAnchor());
        param.setId(rpcParam.getId());
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.updateAnchor(param)));
    }

    @Override
    public RpcResult<Map<String, Object>> createZipFile(AvgUIStyleZipFileCreateRpcParam rpcParam) {
        AvgUIStyleZipFileCreateParam param = AvgUIStyleZipFileConverter.INSTANCE.toDTO(rpcParam);
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.createZipFile(param, false)));
    }

    @Override
    public RpcResult<Map<String, Object>> dirListAllByType(int type, String orderByAndSort) {
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.dirListAllByType(type, orderByAndSort)));
    }

    @Override
    public RpcResult<Map<String, Object>> fileListByType(AvgFileQueryRpcParam param) {
        AvgFileQueryParam queryParam = new AvgFileQueryParam();
        BeanUtils.copyProperties(param, queryParam);
        queryParam.setTopicId(null);
        BizResult<PageView<AvgOriginFileView>> pageViewBizResult = avgBiz.fileListByTypes(queryParam);
        pageViewBizResult.getData().getList().forEach(file -> {
            if (file.getResource() == AvgFileResourceType.COMMON_RESOURCE.getCode()) {
                file.setHiddenType(HiddenTypeEnum.COMMON_HiEEN.getCode());
            } else if (file.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && Objects.equals(file.getTopicId(),
                    param.getTopicId())) {
                file.setHiddenType(HiddenTypeEnum.SPECIAL_NO_HIDDEN.getCode());
            } else if (file.getResource() == AvgFileResourceType.UNKNOW.getCode()
                    || file.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && !Objects.equals(file.getTopicId(),
                    param.getTopicId())) {
                file.setHiddenType(HiddenTypeEnum.SPECIAL_HIDDEN.getCode());
            }
        });
        return RpcResult.success(ResponseUtils.valueOf(pageViewBizResult));
    }

    @Override
    public RpcResult<Map<String, Object>> checkExit(String fileName, Integer dirId) {
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.checkExit(fileName, dirId)));
    }

    @Override
    public RpcResult<Map<String, Object>> deleteFile(int id, int topicId) {
        AvgOriginFile avgOriginFile = avgRepository.queryAvgFileById(id);
        if (Objects.isNull(avgOriginFile)) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST);
        }
        if (avgOriginFile.getResource() == AvgFileResourceType.COMMON_RESOURCE.getCode()) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        if (avgOriginFile.getResource() == AvgFileResourceType.UNKNOW.getCode()
                || avgOriginFile.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && !avgOriginFile.getConfig()
                .getTopicId()
                .equals(topicId)) {
            //对topicId校验权限
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        avgBiz.deleteFile(id, true);
        return RpcResult.success();
    }

    @Override
    public RpcResult<Map<String, Object>> avgFileUpload(MultipartFile file, String key, Integer type, Integer dirId) {
        BizResult<String> bizResult = avgBiz.avgFileUpload(file, key, type, dirId);
        return RpcResult.success(ResponseUtils.valueOf(bizResult));
    }

    @Override
    public RpcResult<Map<String, Object>> updateFile(AvgOriginFileSaveOrUpdateRpcParam rpcParam) {
        AvgOriginFile avgOriginFile = avgRepository.queryAvgFileById(rpcParam.getId());
        if (Objects.isNull(avgOriginFile)) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST);
        }
        if (avgOriginFile.getResource() == AvgFileResourceType.UNKNOW.getCode()
                || avgOriginFile.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && !Objects.equals(
                avgOriginFile.getConfig().getTopicId(), rpcParam.getTopicId())) {
            //对topicId校验权限
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST);
        }
        AvgOriginFileSaveOrUpdateParam param = new AvgOriginFileSaveOrUpdateParam();
        BeanUtils.copyProperties(rpcParam, param);
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.updateFile(param)));
    }

    @Override
    public RpcResult<Map<String, Object>> dirListWithFilesByType(AvgFileQueryRpcParam rpcParam) {
        AvgFileQueryParam param = new AvgFileQueryParam();
        BeanUtils.copyProperties(rpcParam, param);
        param.setTopicId(null);
        BizResult<PageResult<AvgDirView>> pageResultBizResult = avgBiz.dirListWithFilesByType(param);
        pageResultBizResult.getData().getList().forEach(file -> {
            if (file.getResource() == AvgFileResourceType.COMMON_RESOURCE.getCode()) {
                file.setHiddenType(HiddenTypeEnum.COMMON_HiEEN.getCode());
            } else if (file.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && Objects.equals(file.getTopicId(),
                    rpcParam.getTopicId())) {
                file.setHiddenType(HiddenTypeEnum.SPECIAL_NO_HIDDEN.getCode());
            } else if (file.getResource() == AvgFileResourceType.UNKNOW.getCode()
                    || file.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && !Objects.equals(file.getTopicId(),
                    rpcParam.getTopicId())) {
                file.setHiddenType(HiddenTypeEnum.SPECIAL_HIDDEN.getCode());
                file.getChildren().stream().findFirst().ifPresent(avgDirOrFile -> avgDirOrFile.setDisplayUrl(""));
            }
        });
        return RpcResult.success(ResponseUtils.valueOf(pageResultBizResult));
    }

    @Override
    public RpcResult<Map<String, Object>> avgZipFileUpload(MultipartFile file, Integer type, Integer dirId, Integer topicId) {
        AvgDir avgDir = avgRepository.queryAvgDirById(dirId);
        if (avgDir == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "dir目录没找到");
        }
        if (avgDir.getResource() == AvgFileResourceType.UNKNOW.getCode()
                || avgDir.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && !Objects.equals(
                avgDir.getConfig().getTopicId(), topicId)) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.avgZipFileUpload(file, type, dirId)));
    }

    @Override
    public RpcResult<Map<String, Object>> updateDir(AvgDirSaveOrUpdateRpcParam rpcParam) {
        AvgDir avgDir = avgRepository.queryAvgDirById(rpcParam.getId());
        if (Objects.isNull(avgDir)) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST);
        }
        if (avgDir.getResource() == AvgFileResourceType.UNKNOW.getCode()
                || avgDir.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && !Objects.equals(
                avgDir.getConfig().getTopicId(), rpcParam.getTopicId())) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        AvgDirSaveOrUpdateParam param = new AvgDirSaveOrUpdateParam();
        BeanUtils.copyProperties(rpcParam, param);
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.updateDir(param, true)));
    }

    @Override
    public RpcResult<Map<String, Object>> createDir(AvgDirSaveOrUpdateRpcParam rpcParam) {
        AvgDir avgDir = avgRepository.queryAvgDirById(rpcParam.getId());
        if (Objects.isNull(avgDir)) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "dir目录没找到");
        }
        if (avgDir.getResource() == AvgFileResourceType.UNKNOW.getCode()
                || avgDir.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && !Objects.equals(
                avgDir.getConfig().getTopicId(), rpcParam.getTopicId())) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        AvgDirSaveOrUpdateParam param = new AvgDirSaveOrUpdateParam();
        BeanUtils.copyProperties(rpcParam, param);
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.createDir(param, true)));
    }

    @Override
    public RpcResult<Map<String, Object>> dirListByType(int type, String orderByAndSort, int page, int pageSize) {
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.dirListByType(type, page, pageSize, orderByAndSort)));
    }

    @Override
    public RpcResult<Map<String, Object>> deleteDir(int id, int topicId) {
        AvgDir avgDir = avgRepository.queryAvgDirById(id);
        if (Objects.isNull(avgDir)) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "dir目录没找到");
        }
        if(avgDir.getResource() == AvgFileResourceType.COMMON_RESOURCE.getCode()){
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        if (avgDir.getResource() == AvgFileResourceType.UNKNOW.getCode()
                || avgDir.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && !Objects.equals(
                avgDir.getConfig().getTopicId(), topicId)) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        avgBiz.deleteDir(id);
        return RpcResult.success();
    }

    @Override
    public RpcResult<Map<String, Object>> avgHotZoneList(AvgFileQueryRpcParam rpcParam) {
        AvgFileQueryParam param = new AvgFileQueryParam();
        BeanUtils.copyProperties(rpcParam, param);
        param.setTopicId(null);
        BizResult<PageResult<AvgHotZoneView>> pageResultBizResult = avgBiz.avgHotZoneList(param);
        pageResultBizResult.getData().getList().forEach(file -> {
            if (file.getResource() == AvgFileResourceType.COMMON_RESOURCE.getCode()) {
                file.setHiddenType(HiddenTypeEnum.COMMON_HiEEN.getCode());
            } else if (file.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && Objects.equals(file.getTopicId(),
                    rpcParam.getTopicId())) {
                file.setHiddenType(HiddenTypeEnum.SPECIAL_NO_HIDDEN.getCode());
            } else if (file.getResource() == AvgFileResourceType.UNKNOW.getCode()
                    || file.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && !Objects.equals(file.getTopicId(),
                    rpcParam.getTopicId())) {
                file.setHiddenType(HiddenTypeEnum.SPECIAL_HIDDEN.getCode());
            }
        });
        return RpcResult.success(ResponseUtils.valueOf(pageResultBizResult));
    }

    @Override
    public RpcResult<Map<String, Object>> avgHotZoneCreate(AvgHotZoneSaveOrUpdateRpcParam rpcParam) {
        AvgHotZoneAddOrUpdateParam param = AvgHotZoneMapper.INSTANCE.toDTO(rpcParam);
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.avgHotZoneCreate(param)));
    }

    @Override
    public RpcResult<Map<String, Object>> avgHotZoneUpdate(AvgHotZoneSaveOrUpdateRpcParam rpcParam) {
        AvgHotZone hotZone = avgRepository.queryAvgHotZoneById(rpcParam.getId());
        if (hotZone == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "点触热区不存在");
        }
        if (hotZone.getResource() == AvgFileResourceType.UNKNOW.getCode()
                || hotZone.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && !Objects.equals(
                rpcParam.getTopicId(), hotZone.getConfig().getTopicId())) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        AvgHotZoneAddOrUpdateParam param = AvgHotZoneMapper.INSTANCE.toDTO(rpcParam);
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.avgHotZoneUpdate(param)));
    }

    @Override
    public RpcResult<Map<String, Object>> avgHotZoneDelete(Integer id, Integer topicId) {
        AvgHotZone hotZone = avgRepository.queryAvgHotZoneById(id);
        if (hotZone == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "点触热区不存在");
        }
        if(hotZone.getResource() == AvgFileResourceType.COMMON_RESOURCE.getCode()){
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        if (hotZone.getResource() == AvgFileResourceType.UNKNOW.getCode()
                || hotZone.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && !Objects.equals(topicId,
                hotZone.getConfig().getTopicId())) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.avgHotZoneDelete(id)));
    }

    @Override
    public RpcResult<Map<String, Object>> avgHotZoneUpload(MultipartFile file, String key, Integer hotZoneId, Integer topicId) {
        AvgHotZone hotZone = avgRepository.queryAvgHotZoneById(hotZoneId);
        if (hotZone == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "点触热区不存在");
        }
        if (hotZone.getResource() == AvgFileResourceType.UNKNOW.getCode()
                || hotZone.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && !Objects.equals(topicId,
                hotZone.getConfig().getTopicId())) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.avgHotZoneUpload(file, key, hotZoneId)));
    }

    @Override
    public RpcResult<Map<String, Object>> avgTagNameList() {
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.avgTagNameList()));
    }

    @Override
    public RpcResult<Map<String, Object>> updateResource(AvgResourceUpdateRpcParam rpcParam) {
        UpdateResourceAvgParam param = new UpdateResourceAvgParam();
        BeanUtils.copyProperties(rpcParam, param);
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.updateResource(param)));
    }

    @Override
    public RpcResult<Map<String, Object>> gyroscopeList(AvgFileQueryRpcParam rpcParam) {
        AvgFileQueryParam param = new AvgFileQueryParam();
        BeanUtils.copyProperties(rpcParam, param);
        param.setTopicId(null);
        BizResult<PageResult<AvgGyroscopeView>> pageViewBizResult = avgBiz.avgGyroscopeViewList(param);
        pageViewBizResult.getData().getList().forEach(file -> {
            if (file.getResource() == AvgFileResourceType.COMMON_RESOURCE.getCode()) {
                file.setHiddenType(HiddenTypeEnum.COMMON_HiEEN.getCode());
            } else if (file.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && Objects.equals(file.getTopicId(),
                    rpcParam.getTopicId())) {
                file.setHiddenType(HiddenTypeEnum.SPECIAL_NO_HIDDEN.getCode());
            } else if (file.getResource() == AvgFileResourceType.UNKNOW.getCode()
                    || file.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && !Objects.equals(file.getTopicId(),
                    rpcParam.getTopicId())) {
                file.setHiddenType(HiddenTypeEnum.SPECIAL_HIDDEN.getCode());
            }
        });
        return RpcResult.success(ResponseUtils.valueOf(pageViewBizResult));
    }

    @Override
    public RpcResult<Map<String, Object>> avgGyroscopeCreate(AvgGyroscopeSaveRpcParam rpcParam) {
        AvgGyroscopeSaveParam param = AvgGyroscopeMapper.INSTANCE.toSaveParam(rpcParam);
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.avgGyroscopeSave(param)));
    }

    @Override
    public RpcResult<Map<String, Object>> avgGyroscopeUpload(MultipartFile file, int gyroscopeId, Integer type, Integer topicId) {
        AvgGyroscope avgGyroscope = avgRepository.queryAvgGyroscopeById(gyroscopeId);
        if (avgGyroscope == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "陀螺仪不存在");
        }
        if ( avgGyroscope.getResource() == AvgFileResourceType.UNKNOW.getCode()
                || avgGyroscope.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && !Objects.equals(topicId,
                avgGyroscope.getConfig().getTopicId())) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.avgGyroscopeUpload(file, gyroscopeId, type)));
    }

    @Override
    public RpcResult<Map<String, Object>> avgGyroscopeDelete(Integer id, Integer topicId) {
        AvgGyroscope avgGyroscope = avgRepository.queryAvgGyroscopeById(id);
        if (avgGyroscope == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "陀螺仪不存在");
        }
        if(avgGyroscope.getResource() == AvgFileResourceType.COMMON_RESOURCE.getCode()){
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        if (avgGyroscope.getResource() == AvgFileResourceType.UNKNOW.getCode()
                || avgGyroscope.getResource() == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && !Objects.equals(topicId,
                avgGyroscope.getConfig().getTopicId())) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }
        return RpcResult.success(ResponseUtils.valueOf(avgBiz.avgGyroscopeDelete(id)));
    }

    @Override
    public RpcResult<Map<Integer, String>> getTopicRelatedProject(Collection<Integer> topicIds) {
        if (CollectionUtils.isEmpty(topicIds)) {
            return RpcResult.success(Maps.newHashMap());
        }
        List<AvgProject> avgProjects = avgRepository.queryAvgProjectByTopicIds(topicIds);
        Map<Integer, String> topicId2ProjectId = avgProjects.stream()
                .collect(Collectors.toMap(AvgProject::getTopicId, AvgProject::getObjectId));
        return RpcResult.success(topicId2ProjectId);
    }

    /**
     * 验证资源权限
     *
     * @param id  资源ID
     * @param type 资源类型
     * @param topicId 专题ID
     * @return 校验结果
     */
    private RpcResult<Void> validateResourcePermission(int id, int type, Integer topicId) {
        Integer resourceType = 0;
        Integer avgTopicId = 0;

        if (SPINE_TYPE_SET.contains(type)) {
            AvgDir avgDir = avgRepository.queryAvgDirById(id);
            if (!Objects.isNull(avgDir)) {
                resourceType = avgDir.getResource();
                avgTopicId = avgDir.getConfig().getTopicId();
            }
        } else if (type == AvgFileType.HOT_ZONE.getCode()) {
            AvgHotZone zone = avgRepository.queryAvgHotZoneById(id);
            if (!Objects.isNull(zone)) {
                resourceType = zone.getResource();
                avgTopicId = zone.getConfig().getTopicId();
            }
        } else if (type == AvgFileType.GYROSCOPE.getCode()) {
            AvgGyroscope gyroscope = avgRepository.queryAvgGyroscopeById(id);
            if (!Objects.isNull(gyroscope)) {
                resourceType = gyroscope.getResource();
                avgTopicId = gyroscope.getConfig().getTopicId();
            }
        } else {
            AvgOriginFile avgOriginFile = avgRepository.queryAvgFileById(id);
            if (!Objects.isNull(avgOriginFile)) {
                resourceType = avgOriginFile.getResource();
                avgTopicId = avgOriginFile.getConfig().getTopicId();
            }
        }

        if (Objects.equals(resourceType, AvgFileResourceType.COMMON_RESOURCE.getCode())) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }

        if (Objects.equals(resourceType, AvgFileResourceType.UNKNOW.getCode())
                || Objects.equals(resourceType, AvgFileResourceType.SPECIAL_RESOURCE.getCode()) && !Objects.equals(avgTopicId,
                topicId)) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无此资源操作权限");
        }

        return RpcResult.success();
    }
}

package com.kuaikan.role.game.admin.model.view;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity;

/**
 * <AUTHOR>
 * @date 2025/4/21 16:36
 * @description 活动档位奖励视图类
 */

@Data
@Accessors(chain = true)
public class ActivityRewardView implements Serializable {

    private static final long serialVersionUID = -3308217205650687840L;
    private Integer needNum;
    private String name;
    private Integer type;
    private Integer rewardId;
    private Integer rewardNum;
    private ImageInfoView rewardImage;
    private String awardName;
    private String activityName;
    private Long prizeBagId;

    public static ActivityRewardView valueOf(CostumeBlindBoxActivity.Reward reward) {
        ActivityRewardView activityRewardView = new ActivityRewardView();
        activityRewardView.setNeedNum(reward.getNeedNum());
        activityRewardView.setName(reward.getName());
        activityRewardView.setType(reward.getType());
        activityRewardView.setRewardId(reward.getRewardId());
        activityRewardView.setRewardNum(reward.getRewardNum());
        activityRewardView.setRewardImage(ImageInfoView.valueOf(reward.getRewardImage()));
        activityRewardView.setAwardName(reward.getAwardName());
        activityRewardView.setActivityName(reward.getActivityName());
        activityRewardView.setPrizeBagId(reward.getPrizeBagId());
        return activityRewardView;
    }
}

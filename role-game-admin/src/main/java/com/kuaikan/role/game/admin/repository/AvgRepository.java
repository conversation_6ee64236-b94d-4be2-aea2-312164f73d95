package com.kuaikan.role.game.admin.repository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Collation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.mongodb.client.result.UpdateResult;

import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.role.game.admin.dao.rolegame.AvgDirMapper;
import com.kuaikan.role.game.admin.dao.rolegame.AvgGyroscopeMapper;
import com.kuaikan.role.game.admin.dao.rolegame.AvgHotZoneMapper;
import com.kuaikan.role.game.admin.dao.rolegame.AvgOriginFileMapper;
import com.kuaikan.role.game.admin.dao.rolegame.AvgResourceTagMapper;
import com.kuaikan.role.game.admin.enums.AvgChapterOrderTypeEnum;
import com.kuaikan.role.game.admin.enums.AvgProjectOrderTypeEnum;
import com.kuaikan.role.game.api.bean.AvgChapter;
import com.kuaikan.role.game.api.bean.AvgDir;
import com.kuaikan.role.game.api.bean.AvgGyroscope;
import com.kuaikan.role.game.api.bean.AvgHotZone;
import com.kuaikan.role.game.api.bean.AvgOriginFile;
import com.kuaikan.role.game.api.bean.AvgProject;
import com.kuaikan.role.game.api.bean.AvgResourceTag;
import com.kuaikan.role.game.api.bean.CommonAudio;
import com.kuaikan.role.game.api.bean.CommonVideo;
import com.kuaikan.role.game.api.bean.UserAvgChapterRecord;
import com.kuaikan.role.game.api.enums.AvgChapterType;
import com.kuaikan.role.game.api.enums.CommonAudioStatus;
import com.kuaikan.role.game.api.enums.CommonStatus;
import com.kuaikan.role.game.common.enums.CacheConfig;

/**
 * <AUTHOR>
 * @date 2024/5/23
 */
@Repository
@Slf4j
public class AvgRepository {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private AvgOriginFileMapper avgOriginFileMapper;

    @Resource
    private AvgDirMapper avgDirMapper;

    @Resource
    private AvgHotZoneMapper avgHotZoneMapper;

    @Resource
    private AvgGyroscopeMapper avgGyroscopeMapper;

    @Resource
    private AvgResourceTagMapper avgResourceTagMapper;

    public AvgProject queryAvgProjectByObjectId(String objectId) {
        Query query = new Query(Criteria.where("_id").is(new ObjectId(objectId)));
        return mongoTemplate.findOne(query, AvgProject.class);
    }

    public List<AvgProject> queryAvgProjectByTopicIds(Collection<Integer> topicIds) {
        Query query = new Query(Criteria.where("topicId").in(topicIds));
        return mongoTemplate.find(query, AvgProject.class);
    }

    public AvgProject queryAvgProjectByTopicId(Integer topicId) {
        Query query = new Query(Criteria.where("topicId").is(topicId));
        return mongoTemplate.findOne(query, AvgProject.class);
    }

    public void insertAvgProject(AvgProject avgProject) {
        Date now = new Date();
        avgProject.setCreatedAt(now);
        avgProject.setUpdatedAt(now);
        mongoTemplate.insert(avgProject);
    }

    public void updateAvgProject(AvgProject avgProject) {
        Update update = new Update();
        if (StringUtils.isNotBlank(avgProject.getName())) {
            update.set("name", avgProject.getName());
        }
        if (avgProject.getChapterCount() != null) {
            update.set("chapterCount", avgProject.getChapterCount());
        }
        update.set("styleId", avgProject.getStyleId());
        update.set("topicId", avgProject.getTopicId());
        update.set("updatedAt", new Date());
        Query query = new Query(Criteria.where("_id").is(avgProject.getObjectId()));
        mongoTemplate.updateFirst(query, update, AvgProject.class);
    }

    public int countAllAvgProject() {
        Query query = new Query();
        query.addCriteria(Criteria.where("status").is(CommonStatus.ONLINE.getCode()));
        return (int) mongoTemplate.count(query, AvgProject.class);
    }

    public List<AvgProject> queryAvgProjectList(AvgProjectOrderTypeEnum orderTypeEnum, int page, int pageSize) {
        Query query = new Query();
        query.addCriteria(Criteria.where("status").is(CommonStatus.ONLINE.getCode()));
        if (orderTypeEnum.isAsc()) {
            query.with(Sort.by(Sort.Order.asc(orderTypeEnum.getField())));
        } else {
            query.with(Sort.by(Sort.Order.desc(orderTypeEnum.getField())));
        }
        query.collation(Collation.of(Collation.CollationLocale.of("zh")));
        query.skip((long) (page - 1) * pageSize).limit(pageSize);
        return mongoTemplate.find(query, AvgProject.class);
    }

    public AvgChapter queryAvgChapterByChapterId(int chapterId) {
        Query query = new Query(Criteria.where("chapterId").is(chapterId));
        return mongoTemplate.findOne(query, AvgChapter.class);
    }

    public List<AvgChapter> queryAvgChapterByChapterIds(List<Integer> chapterIds, int type) {
        Query query = new Query(Criteria.where("chapterId").in(chapterIds).and("type").is(type));
        return mongoTemplate.find(query, AvgChapter.class);
    }

    public Integer queryMaxChapterId() {
        Query query = new Query();
        query.with(Sort.by(Sort.Order.desc("chapterId")));
        AvgChapter maxChapter = mongoTemplate.findOne(query, AvgChapter.class);
        if (maxChapter == null) {
            return AvgChapter.CHAPTER_MIN;
        }
        return maxChapter.getChapterId();
    }

    public void updateAvgChapter(AvgChapter avgChapter) {
        Update update = new Update();

        if (StringUtils.isNotBlank(avgChapter.getChapterFileName())) {
            update.set("chapterFileName", avgChapter.getChapterFileName());
        }
        if (StringUtils.isNotBlank(avgChapter.getChapterFileKey())) {
            update.set("chapterFileKey", avgChapter.getChapterFileKey());
        }
        if (StringUtils.isNotBlank(avgChapter.getChapterName())) {
            update.set("chapterName", avgChapter.getChapterName());
        }
        if (avgChapter.getStyleId() != null) {
            update.set("styleId", avgChapter.getStyleId());
        }
        if (CollectionUtils.isNotEmpty(avgChapter.getTextList())) {
            update.set("textList", avgChapter.getTextList());
        }
        if (avgChapter.getStatus() != null) {
            update.set("status", avgChapter.getStatus());
        }
        if (StringUtils.isNotBlank(avgChapter.getTextContentMd5())) {
            update.set("textContentMd5", avgChapter.getTextContentMd5());
        }
        if (avgChapter.getType() != null) {
            update.set("type", avgChapter.getType());
        }
        if (avgChapter.getRefreshFile() != null) {
            update.set("refreshFile", avgChapter.getRefreshFile());
        }

        update.set("updatedAt", new Date());
        Query query = new Query(Criteria.where("chapterId").is(avgChapter.getChapterId()));
        mongoTemplate.updateFirst(query, update, AvgChapter.class);
        deleteCache(avgChapter.getChapterId());
    }

    /**
     * 可空的styleId
     */
    public void updateAvgChapterEmptyStyleId(AvgChapter avgChapter) {
        Update update = new Update();

        if (StringUtils.isNotBlank(avgChapter.getChapterName())) {
            update.set("chapterName", avgChapter.getChapterName());
        }
        update.set("styleId", avgChapter.getStyleId());

        update.set("updatedAt", new Date());
        Query query = new Query(Criteria.where("chapterId").is(avgChapter.getChapterId()));
        mongoTemplate.updateFirst(query, update, AvgChapter.class);
        deleteCache(avgChapter.getChapterId());
    }

    public void insert(AvgChapter avgChapter) {
        Date now = new Date();
        avgChapter.setCreatedAt(now);
        avgChapter.setUpdatedAt(now);
        mongoTemplate.insert(avgChapter);
        deleteCache(avgChapter.getChapterId());
    }

    public void insertAvgOriginFile(AvgOriginFile avgOriginFile) {
        avgOriginFileMapper.insertSelective(avgOriginFile);
        deleteFileCache(avgOriginFile);
        deleteFileParentIdCache(avgOriginFile.getParentId());
    }

    public void batchInsertAvgOriginFile(List<AvgOriginFile> avgOriginFiles) {
        avgOriginFileMapper.batchInsert(avgOriginFiles);
        for (AvgOriginFile avgOriginFile : avgOriginFiles) {
            deleteFileCache(avgOriginFile);
            deleteFileParentIdCache(avgOriginFile.getParentId());
        }
    }

    public void insertCommonVideo(CommonVideo commonVideo) {
        Date now = new Date();
        commonVideo.setCreatedAt(now);
        commonVideo.setUpdatedAt(now);
        mongoTemplate.insert(commonVideo);
        deleteCommonVideoCache(commonVideo.getVideoId());
    }

    public CommonVideo queryCommonVideoByVideoId(String videoId) {
        Query query = new Query(Criteria.where("video_id").is(videoId));
        return mongoTemplate.findOne(query, CommonVideo.class);
    }

    public CommonAudio queryCommonAudioByAudioId(String audioId) {
        Query query = new Query(Criteria.where("audio_id").is(audioId));
        return mongoTemplate.findOne(query, CommonAudio.class);
    }

    public List<CommonVideo> batchQueryAvgCommonVideo(Collection<String> videoIds) {
        Criteria criteria = Criteria.where("video_id").in(videoIds);
        Query query = new Query(criteria);
        return mongoTemplate.find(query, CommonVideo.class);
    }

    public void updateCommonAudio(CommonAudio commonAudio) {
        Update update = new Update();
        if (Objects.nonNull(commonAudio.getOriginAudio())) {
            update.set("origin_audio", commonAudio.getOriginAudio());
        }
        update.set("status", commonAudio.getStatus());
        update.set("updated_at", new Date());
        Query query = new Query(Criteria.where("_id").is(commonAudio.getId()));
        mongoTemplate.updateFirst(query, update, CommonAudio.class);
    }

    public void updateCommonVideo(CommonVideo commonVideo) {
        Update update = new Update();
        if (Objects.nonNull(commonVideo.getOriginVideo())) {
            update.set("origin_video", commonVideo.getOriginVideo());
        }
        update.set("status", commonVideo.getStatus());
        update.set("updated_at", new Date());
        Query query = new Query(Criteria.where("_id").is(commonVideo.getId()));
        mongoTemplate.updateFirst(query, update, CommonVideo.class);
        deleteCommonVideoCache(commonVideo.getVideoId());
    }

    public AvgOriginFile queryAvgByFileName(String fileName) {
        return queryAvgByFileNameAndParentId(fileName, null);
    }

    public AvgOriginFile queryAvgFileById(int id) {
        return avgOriginFileMapper.selectByPrimaryKey(id);
    }

    public List<AvgOriginFile> selectByTypeAndPage(int type, int offset, int limit, String orderByAndSort) {
        return avgOriginFileMapper.selectByTypeAndPage(type, offset, limit, orderByAndSort);
    }

    public List<AvgOriginFile> selectByTypesAndPage(List<Integer> types, int offset, int limit, String orderByAndSort) {
        return avgOriginFileMapper.selectByTypesAndPage(types, offset, limit, orderByAndSort);
    }

    public PageInfo<AvgOriginFile> selectAvgFileList(List<Integer> types, int page, int pageSize, String orderByAndSort,
                                                     String name, Integer topicId, Integer resource, Integer status, List<String> tagNames) {
        PageHelper.startPage(page, pageSize);
        List<AvgOriginFile> avgOriginFiles = avgOriginFileMapper.selectAvgFileList(types, orderByAndSort, name, topicId, resource,
                status, tagNames);
        return new PageInfo<>(avgOriginFiles);
    }

    public void batchUpdateAvgOriginFileByPrimaryKey(Collection<AvgOriginFile> avgOriginFiles) {
        if (CollectionUtils.isEmpty(avgOriginFiles)) {
            return;
        }
        avgOriginFileMapper.batchUpdateByPrimaryKey(avgOriginFiles);
    }

    public List<AvgOriginFile> queryAllAvgOriginFile() {
        return avgOriginFileMapper.queryAll();
    }

    public List<AvgOriginFile> queryAvgOriginFileByTypes(Collection<Integer> types) {
        return avgOriginFileMapper.queryByTypes(types);
    }

    public int countByType(int type) {
        return avgOriginFileMapper.countByType(type);
    }

    public int countByTypes(List<Integer> types) {
        return avgOriginFileMapper.countByTypes(types);
    }

    public void deleteFile(int id) {
        AvgOriginFile avgOriginFile = avgOriginFileMapper.selectByPrimaryKey(id);
        avgOriginFileMapper.deleteByPrimaryKey(id);
        deleteFileCache(avgOriginFile);
        deleteFileParentIdCache(avgOriginFile.getParentId());
    }

    public void deleteFileByParentId(int parentId) {
        List<AvgOriginFile> avgOriginFiles = avgOriginFileMapper.queryAvgFileByParentId(parentId);
        avgOriginFileMapper.deleteByParentId(parentId);
        avgOriginFiles.forEach(this::deleteFileCache);
        deleteFileParentIdCache(parentId);
    }

    public void updateAvgOriginFile(AvgOriginFile avgOriginFile) {
        avgOriginFileMapper.updateByPrimaryKeySelective(avgOriginFile);
        deleteFileCache(avgOriginFile);
        deleteFileParentIdCache(avgOriginFile.getParentId());
    }

    public List<AvgOriginFile> queryAvgFileByNames(List<String> fileNames) {
        if (CollectionUtils.isEmpty(fileNames)) {
            return new ArrayList<>();
        }
        return avgOriginFileMapper.queryAvgFileByNames(fileNames);
    }

    public List<AvgOriginFile> queryAvgFileByParentId(Integer parentId) {
        return avgOriginFileMapper.queryAvgFileByParentId(parentId);
    }

    public List<AvgDir> queryAvgDirByName(String name) {
        return avgDirMapper.queryAvgDirByName(name);
    }

    public List<AvgDir> queryAvgDirByNameAndType(String name, Integer type) {
        return avgDirMapper.queryAvgDirByNameAndType(name, type);
    }

    public List<AvgOriginFile> queryAvgFileByNameAndType(String name, int type) {
        return avgOriginFileMapper.selectAvgByTypeAndName(name, type);
    }

    public List<AvgOriginFile> queryAvgFileByNameAndTypes(String name, List<Integer> types) {
        if (StringUtils.isBlank(name) || CollectionUtils.isEmpty(types)) {
            return Lists.newArrayList();
        }
        return avgOriginFileMapper.selectAvgByTypeAndNames(name, types);
    }

    public void insertAvgDir(AvgDir avgDir) {
        avgDirMapper.insertSelective(avgDir);
        deleteFileParentIdCache(avgDir.getId());
        deleteAvgDirCache(avgDir.getId());
    }

    public void updateAvgDir(AvgDir avgDir) {
        avgDirMapper.updateByPrimaryKeySelective(avgDir);
        deleteFileParentIdCache(avgDir.getId());
        deleteAvgDirCache(avgDir.getId());
    }

    public PageInfo<AvgDir> queryAvgDirByTypeAndParentId(int type, int parentId, int pageNum, int pageSize,
                                                         String orderByAndSort) {
        PageHelper.startPage(pageNum, pageSize);
        List<AvgDir> avgDirs = avgDirMapper.queryAvgDirByTypeAndParentId(type, parentId, orderByAndSort);
        return new PageInfo<>(avgDirs);
    }

    public PageInfo<AvgDir> queryAvgDirPage(int type, int parentId, int pageNum, int pageSize, String orderByAndSort, String name,
                                            Integer topicId, Integer resource, Integer status, List<String> tagNames) {
        PageHelper.startPage(pageNum, pageSize);
        List<AvgDir> avgDirs = avgDirMapper.queryAvgDirList(type, parentId, orderByAndSort, name, topicId, resource, status,
                tagNames);
        return new PageInfo<>(avgDirs);
    }

    public List<AvgDir> queryDirByTypeAndParentId(int type, int parentId) {
        return avgDirMapper.queryDirByTypeAndParentId(type, parentId);
    }

    public List<AvgDir> queryDirByType(int type) {
        return avgDirMapper.queryDirByType(type);
    }

    // query avg dir by condition
    public List<AvgDir> queryDirList(Integer type, String name, Integer topicId, Integer resource, Integer status) {
        return avgDirMapper.queryDirList(type, name, topicId, resource, status);
    }

    public List<AvgOriginFile> queryFileByTypeAndParentId(int type, int parentId) {
        return avgOriginFileMapper.queryFileByTypeAndParentId(type, parentId);
    }

    // query file by condition
    public List<AvgOriginFile> queryOriginFileList(Integer type, String name, Integer topicId, Integer resource, Integer status,
                                                   Integer parentId) {
        return avgOriginFileMapper.queryOriginFileList(type, name, topicId, resource, status, parentId);
    }

    public List<AvgDir> queryAllAvgDirByTypeAndParentId(int type, int parentId, String orderByAndSort) {
        return avgDirMapper.queryAvgDirByTypeAndParentId(type, parentId, orderByAndSort);

    }

    public List<AvgDir> queryAvgDirsByIds(Collection<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return avgDirMapper.queryAvgByIds(ids);
    }

    public List<AvgDir> queryAvgDirByTypes(Collection<Integer> types) {
        if (CollectionUtils.isEmpty(types)) {
            return new ArrayList<>();
        }
        return avgDirMapper.queryAvgDirByTypes(types);
    }

    public void batchUpdateAvgDirByPrimaryKey(Collection<AvgDir> avgDirs) {
        if (CollectionUtils.isEmpty(avgDirs)) {
            return;
        }
        avgDirMapper.batchUpdateByPrimaryKey(avgDirs);
    }

    public AvgDir queryAvgDirById(int id) {
        return avgDirMapper.selectByPrimaryKey(id);
    }

    public List<AvgDir> queryAllAvgDir() {
        return avgDirMapper.queryAll();
    }

    public List<AvgOriginFile> queryAvgFileByParentIds(List<Integer> ids, String orderByAndSort) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return avgOriginFileMapper.queryAvgFileByParentIds(ids, orderByAndSort);
    }

    public void deleteDir(int id) {
        avgDirMapper.deleteByPrimaryKey(id);
        deleteFileParentIdCache(id);
        deleteAvgDirCache(id);
    }

    public List<AvgDir> queryAvgDirByNames(List<String> dirNames) {
        if (CollectionUtils.isEmpty(dirNames)) {
            return new ArrayList<>();
        }
        return avgDirMapper.queryAvgDirByNames(dirNames);
    }

    public AvgHotZone queryAvgHotZoneById(int id) {
        return avgHotZoneMapper.selectByPrimaryKey(id);
    }

    public List<AvgHotZone> queryAvgHotZoneByIds(Collection<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return avgHotZoneMapper.queryByIds(ids);
    }

    public void updateAvgHotZone(AvgHotZone avgHotZone) {
        avgHotZoneMapper.updateByPrimaryKeySelective(avgHotZone);
        deleteHotZoneCache(avgHotZone);
    }

    public PageInfo<AvgHotZone> queryAvgHotZone(int pageNum, int pageSize, String orderByAndSort, String name, Integer topicId,
                                                Integer resource, Integer status, List<String> tagNames) {
        PageHelper.startPage(pageNum, pageSize);
        List<AvgHotZone> avgHotZones = avgHotZoneMapper.queryAvgHotZone(orderByAndSort, name, topicId, resource, status,
                tagNames);
        return new PageInfo<>(avgHotZones);
    }

    public void insertAvgHotZone(AvgHotZone avgHotZone) {
        avgHotZoneMapper.insertSelective(avgHotZone);
    }

    public AvgHotZone queryAvgHotZoneByName(String name) {
        return avgHotZoneMapper.queryAvgHotZoneByName(name);
    }

    public List<AvgHotZone> queryAvgHotZoneByNames(List<String> names) {
        if (CollectionUtils.isEmpty(names)) {
            return Lists.newArrayList();
        }
        return avgHotZoneMapper.queryAvgHotZoneByNames(names);
    }

    public void batchUpdateAvgHotZoneByPrimaryKey(Collection<AvgHotZone> hotZones) {
        if (CollectionUtils.isEmpty(hotZones)) {
            return;
        }
        avgHotZoneMapper.batchUpdateByPrimaryKey(hotZones);
    }

    public List<AvgHotZone> queryAllAvgHotZone() {
        return avgHotZoneMapper.queryAll();
    }

    // query hot zone from db
    public Map<String, AvgHotZone> queryAllHotZoneFromDB() {
        return avgHotZoneMapper.queryAll().stream().collect(Collectors.toMap(AvgHotZone::getName, Function.identity()));
    }

    public void deleteAvgChapterByProjectId(String projectId) {

        Query query = new Query();
        Criteria criteria = Criteria.where("status").is(CommonStatus.ONLINE.getCode()).and("projectId").is(projectId);
        query.addCriteria(criteria);
        List<AvgChapter> avgChapters = mongoTemplate.find(query, AvgChapter.class);

        Query updateQuery = new Query(Criteria.where("projectId").is(projectId));
        Update update = new Update();
        update.set("status", CommonStatus.DELETED.getCode());
        mongoTemplate.updateMulti(updateQuery, update, AvgChapter.class);
        avgChapters.forEach(avgChapter -> deleteCache(avgChapter.getChapterId()));
    }

    public void deleteAvgProjectByProjectId(String projectId) {
        Query query = new Query(Criteria.where("_id").is(new ObjectId(projectId)));
        Update update = new Update();
        update.set("status", CommonStatus.DELETED.getCode());
        mongoTemplate.updateFirst(query, update, AvgProject.class);
    }

    public void deleteAvgChapterByChapterId(int chapterId) {
        Query query = new Query(Criteria.where("chapterId").is(chapterId));
        Update update = new Update();
        update.set("status", CommonStatus.DELETED.getCode());
        mongoTemplate.updateFirst(query, update, AvgChapter.class);
        deleteCache(chapterId);
    }

    public int queryProjectChapterCount(String projectId) {
        Query query = new Query(Criteria.where("projectId").is(projectId).and("status").is(CommonStatus.ONLINE.getCode()));
        return (int) mongoTemplate.count(query, AvgChapter.class);
    }

    public int queryProjectChapterCount(String projectId, String segmentName, Integer segmentId, Integer type) {
        Query query = new Query();
        query.addCriteria(Criteria.where("projectId").is(projectId).and("status").is(CommonStatus.ONLINE.getCode()));
        if (StringUtils.isNotBlank(segmentName)) {
            query.addCriteria(Criteria.where("chapterName").regex(".*" + segmentName + ".*", "i"));
        }
        if (segmentId != null && segmentId > 0) {
            query.addCriteria(Criteria.where("chapterId").is(segmentId));
        }
        if (type != null) {
            query.addCriteria(Criteria.where("type").is(type));
        }
        return (int) mongoTemplate.count(query, AvgChapter.class);
    }

    public List<AvgChapter> queryAvgChapterList(AvgChapterOrderTypeEnum orderType, String projectId, String chapterName,
                                                Integer chapterId, Integer type, int page, int pageSize) {
        Query query = new Query();
        query.addCriteria(Criteria.where("projectId").is(projectId).and("status").is(CommonStatus.ONLINE.getCode()));
        if (orderType.isAsc()) {
            query.with(Sort.by(Sort.Order.asc(orderType.getField())));
        } else {
            query.with(Sort.by(Sort.Order.desc(orderType.getField())));
        }
        if (StringUtils.isNotBlank(chapterName)) {
            query.addCriteria(Criteria.where("chapterName").regex(".*" + chapterName + ".*", "i"));
        }
        if (chapterId != null && chapterId > 0) {
            query.addCriteria(Criteria.where("chapterId").is(chapterId));
        }
        if (type != null) {
            query.addCriteria(Criteria.where("type").is(type));
        }
        query.collation(Collation.of(Collation.CollationLocale.of("zh")));
        query.skip((long) (page - 1) * pageSize).limit(pageSize);
        return mongoTemplate.find(query, AvgChapter.class);
    }

    public List<AvgChapter> queryAvgChapterList(AvgChapterOrderTypeEnum orderType, String projectId, int type, int page,
                                                int pageSize) {
        Query query = new Query();
        query.addCriteria(Criteria.where("projectId").is(projectId).and("status").is(CommonStatus.ONLINE.getCode()));
        if (orderType.isAsc()) {
            query.with(Sort.by(Sort.Order.asc(orderType.getField())));
        } else {
            query.with(Sort.by(Sort.Order.desc(orderType.getField())));
        }
        if (type != 0) {
            query.addCriteria(Criteria.where("type").is(type));
        }
        query.collation(Collation.of(Collation.CollationLocale.of("zh")));
        query.skip((long) (page - 1) * pageSize).limit(pageSize);
        return mongoTemplate.find(query, AvgChapter.class);
    }

    public List<AvgChapter> queryAvgChapterList(String name, Integer id, int type) {
        Query query = new Query();
        Criteria criteria = Criteria.where("status").is(CommonStatus.ONLINE.getCode());
        if (StringUtils.isNotBlank(name)) {
            criteria.and("chapterName").regex(".*" + name + ".*", "i");
        }
        if (id != null) {
            criteria.and("chapterId").is(id);
        }
        if (type != 0) {
            criteria.and("type").is(type);
        }
        query.addCriteria(criteria);
        return mongoTemplate.find(query, AvgChapter.class);
    }

    public void deleteAvgHotZone(Integer id) {
        AvgHotZone avgHotZone = avgHotZoneMapper.selectByPrimaryKey(id);
        avgHotZoneMapper.deleteByPrimaryKey(id);
        deleteHotZoneCache(avgHotZone);
    }

    public AvgOriginFile queryAvgByFileNameAndParentId(String fileName, Integer parentId) {
        return avgOriginFileMapper.queryAvgByFileNameAndParentId(fileName, parentId);
    }

    public List<AvgChapter> queryTextChapterByChapterIds(Collection<Integer> avgChapterIds) {
        if (CollectionUtils.isEmpty(avgChapterIds)) {
            return new ArrayList<>();
        }
        Query query = new Query(Criteria.where("chapterId")
                .in(avgChapterIds)
                .and("status")
                .is(CommonStatus.ONLINE.getCode())
                .and("type")
                .is(AvgChapterType.TEXT.getCode()));
        return mongoTemplate.find(query, AvgChapter.class);
    }

    public Map<String, AvgOriginFile> queryAvgOriginFileByNames(Collection<String> fileNames) {
        if (CollectionUtils.isEmpty(fileNames)) {
            return new HashMap<>();
        }
        return avgOriginFileMapper.queryAvgFileByNames(fileNames)
                .stream()
                .collect(Collectors.toMap(AvgOriginFile::getName, Function.identity()));
    }

    public Map<String, AvgHotZone> queryAvgHotZoneByNames(Collection<String> hotZoneNames) {
        if (CollectionUtils.isEmpty(hotZoneNames)) {
            return new HashMap<>();
        }
        return avgHotZoneMapper.queryAvgHotZoneByNames(hotZoneNames)
                .stream()
                .collect(Collectors.toMap(AvgHotZone::getName, Function.identity()));
    }

    public Map<String, AvgDir> queryAvgDirByNamesAndType(Collection<String> dirNames, Integer type) {
        if (CollectionUtils.isEmpty(dirNames)) {
            return new HashMap<>();
        }
        return avgDirMapper.queryAvgDirByNamesAndType(dirNames, type)
                .stream()
                .collect(Collectors.toMap(AvgDir::getName, Function.identity()));
    }

    public Map<Integer, List<AvgOriginFile>> queryAvgOriginFileByParentIds(List<Integer> parentIds) {
        if (CollectionUtils.isEmpty(parentIds)) {
            return new HashMap<>();
        }
        return avgOriginFileMapper.queryAvgFileByParentIds(parentIds, null)
                .stream()
                .collect(Collectors.groupingBy(AvgOriginFile::getParentId));
    }

    public Map<Integer, AvgOriginFile> queryAvgOriginFileByIds(Collection<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        return avgOriginFileMapper.queryByIds(ids).stream().collect(Collectors.toMap(AvgOriginFile::getId, Function.identity()));
    }

    public List<AvgChapter> queryAllAvg() {
        return mongoTemplate.find(new Query(), AvgChapter.class);
    }

    private void deleteCache(int avgChapterId) {
        LettuceClusterClient clusterClientByName = LettuceClusterUtil.getClusterClientByName(
                CacheConfig.AVG_CHAPTER_INFO.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.AVG_CHAPTER_INFO.getKeyPattern(), avgChapterId);
        clusterClientByName.del(cacheKey);
    }

    private void deleteFileCache(AvgOriginFile avgOriginFile) {
        LettuceClusterClient clusterClientByName = LettuceClusterUtil.getClusterClientByName(
                CacheConfig.AVG_ORIGIN_FILE_INFO.getReadWriteVip());
        String cacheKey1 = KeyGenerator.generate(CacheConfig.AVG_ORIGIN_FILE_INFO.getKeyPattern(), avgOriginFile.getId());
        String cacheKey2 = KeyGenerator.generate(CacheConfig.AVG_ORIGIN_FILE_INFO_NAME.getKeyPattern(), avgOriginFile.getName());
        ArrayList<String> keys = Lists.newArrayList(cacheKey1, cacheKey2);
        clusterClientByName.del(keys.toArray(new String[0]));
    }

    private void deleteFileParentIdCache(Integer parentId) {
        LettuceClusterClient clusterClientByName = LettuceClusterUtil.getClusterClientByName(
                CacheConfig.AVG_ORIGIN_FILE_INFO_PARENT_ID.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.AVG_ORIGIN_FILE_INFO.getKeyPattern(), parentId);
        clusterClientByName.del(cacheKey);
    }

    private void deleteHotZoneCache(AvgHotZone avgHotZone) {
        LettuceClusterClient clusterClientByName = LettuceClusterUtil.getClusterClientByName(
                CacheConfig.AVG_HOT_ZONE_INFO.getReadWriteVip());
        String cacheKey1 = KeyGenerator.generate(CacheConfig.AVG_HOT_ZONE_INFO.getKeyPattern(), avgHotZone.getId());
        String cacheKey2 = KeyGenerator.generate(CacheConfig.AVG_HOT_ZONE_INFO_NAME.getKeyPattern(), avgHotZone.getName());
        ArrayList<String> keys = Lists.newArrayList(cacheKey1, cacheKey2);
        clusterClientByName.del(keys.toArray(new String[0]));
    }

    private void deleteCommonVideoCache(String videoId) {
        LettuceClusterClient clusterClientByName = LettuceClusterUtil.getClusterClientByName(
                CacheConfig.AVG_COMMON_VIDEO.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.AVG_COMMON_VIDEO.getKeyPattern(), videoId);
        clusterClientByName.del(cacheKey);
    }

    public void insertCommonAudio(CommonAudio commonAudio) {
        mongoTemplate.insert(commonAudio);
        // todo: delete cache if exists
    }

    public List<CommonAudio> queryCommonAudioBatchByAudioIds(List<String> audioIds) {
        if (CollUtil.isEmpty(audioIds)) {
            return Lists.newArrayList();
        }
        Query query = new Query(Criteria.where("audio_id").in(audioIds).and("status").is(CommonAudioStatus.SUCCESS.getCode()));
        return mongoTemplate.find(query, CommonAudio.class);
    }

    @Deprecated
    public List<AvgOriginFile> queryAvgOriginFileByType(Collection<Integer> audioTypeSet) {
        return avgOriginFileMapper.queryAvgFileByTypes(audioTypeSet);
    }

    public void batchInsertCommonAudio(List<CommonAudio> data) {
        mongoTemplate.insert(data, CommonAudio.class);
    }

    public void batchUpdate(List<AvgOriginFile> allAudioFiles) {
        avgOriginFileMapper.batchUpdate(allAudioFiles);
    }

    public List<AvgProject> queryAllAvgProject() {
        Query query = new Query();
        return mongoTemplate.find(query, AvgProject.class);
    }

    public List<AvgChapter> queryAvgChapterByProjectId(String projectId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("projectId").is(projectId).and("status").is(CommonStatus.ONLINE.getCode()));
        return mongoTemplate.find(query, AvgChapter.class);
    }

    public AvgProject getProjectByTopicId(int topicId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("topicId").is(topicId));
        return mongoTemplate.findOne(query, AvgProject.class);
    }

    public List<UserAvgChapterRecord> queryByLimitAndOffset(int offset, int limit) {
        // 创建查询对象
        Query query = new Query();
        // 添加排序条件（按_id字段升序）
        query.with(Sort.by(Sort.Direction.ASC, "_id"));
        // 添加跳过记录数（offset）
        query.skip(offset);
        // 添加每页记录数（limit）
        query.limit(limit);
        // 执行查询
        return mongoTemplate.find(query, UserAvgChapterRecord.class);
    }

    public Map<Integer, List<UserAvgChapterRecord>> queryUserAvgChapterRecordByUserId(Set<Integer> userIds) {
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").in(userIds));
        List<UserAvgChapterRecord> userAvgChapterRecords = mongoTemplate.find(query, UserAvgChapterRecord.class);
        return userAvgChapterRecords.stream().collect(Collectors.groupingBy(UserAvgChapterRecord::getUserId));
    }

    public void batchDeleteAvgChapterRecord(Collection<ObjectId> objectIds) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(objectIds));
        mongoTemplate.remove(query, UserAvgChapterRecord.class);
    }

    public void batchUpdateAvgChapterRecord(Collection<ObjectId> objectIds) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(objectIds));

        Update update = new Update();
        update.set("version", 0);
        update.set("updatedAt", new Date());
        UpdateResult updateResult = mongoTemplate.updateMulti(query, update, UserAvgChapterRecord.class);
        if (updateResult.getMatchedCount() != objectIds.size()) {
            log.error("batchUpdateAvgChapterRecord error, updateResult:{}", updateResult);
        }
    }

    public PageInfo<AvgGyroscope> queryAvgGyroscope(int pageNum, int pageSize, String orderByAndSort, String name,
                                                    Integer topicId, Integer resource, Integer status, List<String> tagNames) {
        PageHelper.startPage(pageNum, pageSize);
        List<AvgGyroscope> avgGyroscopes = avgGyroscopeMapper.queryAvgGyroscope(orderByAndSort, name, topicId, resource, status,tagNames);
        return new PageInfo<>(avgGyroscopes);
    }

    public void insertAvgGyroscope(AvgGyroscope avgGyroscope) {
        avgGyroscopeMapper.insertSelective(avgGyroscope);
    }

    public int updateAvgGyroscope(AvgGyroscope avgGyroscope) {
        deleteGyroscopeCache(avgGyroscope);
        return avgGyroscopeMapper.updateByPrimaryKeySelective(avgGyroscope);
    }

    public AvgGyroscope queryAvgGyroscopeById(int id) {
        return avgGyroscopeMapper.selectByPrimaryKey(id);
    }

    public List<AvgGyroscope> queryGyroscopeByNames(List<String> names) {
        if (CollectionUtils.isEmpty(names)) {
            return Lists.newArrayList();
        }
        return avgGyroscopeMapper.queryGyroscopeByNames(names);
    }

    public void batchUpdateAvgGyroscopeByPrimaryKey(Collection<AvgGyroscope> gyroscopes) {
        if (CollectionUtils.isEmpty(gyroscopes)) {
            return;
        }
        gyroscopes.forEach(this::deleteGyroscopeCache);
        avgGyroscopeMapper.batchUpdateByPrimaryKey(gyroscopes);
    }

    public void deleteAvgGyroscope(int id) {
        AvgGyroscope avgGyroscope = avgGyroscopeMapper.selectByPrimaryKey(id);
        avgGyroscopeMapper.deleteByPrimaryKey(id);
        deleteGyroscopeCache(avgGyroscope);
    }

    public List<AvgOriginFile> selectByTypeAndNames(int type, Collection<String> names) {
        if (CollectionUtils.isEmpty(names)) {
            return Lists.newArrayList();
        }
        return avgOriginFileMapper.selectByTypeAndNames(type, names);
    }

    public void deleteAvgResourceTag() {
        avgResourceTagMapper.deleteAll();
    }

    public void saveAvgResourceTag(Collection<AvgResourceTag> avgResourceTags) {
        avgResourceTagMapper.batchInsert(avgResourceTags);
    }

    public List<String> queryTagNameList() {
        return avgResourceTagMapper.queryTagNameList();
    }

    private void deleteGyroscopeCache(AvgGyroscope avgGyroscope) {
        LettuceClusterClient clusterClientByName = LettuceClusterUtil.getClusterClientByName(
                CacheConfig.AVG_GYROSCOPE_INFO.getReadWriteVip());
        String cacheKey1 = KeyGenerator.generate(CacheConfig.AVG_GYROSCOPE_INFO.getKeyPattern(), avgGyroscope.getId());
        ArrayList<String> keys = Lists.newArrayList(cacheKey1);
        clusterClientByName.del(keys.toArray(new String[0]));
    }

    private void deleteAvgDirCache(Integer dirId) {
        LettuceClusterClient clusterClientByName = LettuceClusterUtil.getClusterClientByName(
                CacheConfig.AVG_DIR_INFO.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.AVG_DIR_INFO.getKeyPattern(), dirId);
        clusterClientByName.del(cacheKey);
    }

}

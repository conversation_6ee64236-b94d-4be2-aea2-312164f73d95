package com.kuaikan.role.game.admin.model.view;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity;
import com.kuaikan.role.game.api.bean.FurnitureActivityConfig;
import com.kuaikan.role.game.api.enums.FurnitureActivityType;

/**
 * <AUTHOR>
 * @date 2025/4/17 19:07
 */

@Data
@Accessors(chain = true)
public class FurnitureActivityView implements Serializable {

    private static final long serialVersionUID = 6139640776053947361L;

    private Integer id;
    private Integer orderId;
    private String name;
    private Long startAt;
    private Long endAt;
    private Integer type;
    private List<BasicFurnitureGroupView> furnitureGroups;
    private ImageInfoView navigationImage;
    private ImageInfoView backgroundImage;
    private boolean showGuarantee;
    private Integer ruleId;
    private String ruleDescription;
    private List<String> exchangeMall;
    private String reminderBubble;
    private RoleAnimationVO roleAnimation;
    private boolean firstFree;
    private FirstPriceVO firstPrice;
    private CumulativeDrawRewardVO cumulativeDrawReward;
    private String operator;
    private Integer status;
    private Date updatedAt;

    public static FurnitureActivityView valueOf(FurnitureActivityConfig furnitureActivityConfig, Map<Integer, String> furnitureGroupMap,
                                                Map<Integer, List<Integer>> activityId2FurnitureGroupIdsMap) {
        FurnitureActivityView furnitureActivityView = new FurnitureActivityView();
        Integer furnitureActivityType = furnitureActivityConfig.getType();
        furnitureActivityView.setId(furnitureActivityConfig.getId());
        furnitureActivityView.setOrderId(furnitureActivityConfig.getOrderId());
        furnitureActivityView.setName(furnitureActivityConfig.getName());
        furnitureActivityView.setStartAt(furnitureActivityConfig.getStartAt());
        furnitureActivityView.setEndAt(furnitureActivityConfig.getEndAt());
        furnitureActivityView.setType(furnitureActivityType);
        furnitureActivityView.setNavigationImage(ImageInfoView.valueOf(furnitureActivityConfig.getConfig().getNavigationImage()));
        furnitureActivityView.setBackgroundImage(ImageInfoView.valueOf(furnitureActivityConfig.getConfig().getBackgroundImage()));
        furnitureActivityView.setShowGuarantee(furnitureActivityConfig.getConfig().isShowGuarantee());
        furnitureActivityView.setRuleId(furnitureActivityConfig.getConfig().getRuleId());
        furnitureActivityView.setRuleDescription(furnitureActivityConfig.getConfig().getRuleDescription());
        furnitureActivityView.setExchangeMall(furnitureActivityConfig.getConfig().getExchangeMall());
        furnitureActivityView.setOperator(furnitureActivityConfig.getOperator());
        furnitureActivityView.setStatus(furnitureActivityConfig.getStatus());
        furnitureActivityView.setUpdatedAt(furnitureActivityConfig.getUpdatedAt());

        // 限时活动池特性
        if (furnitureActivityType == FurnitureActivityType.FURNITURE_ACTIVITY_LIMIT.getCode()) {
            furnitureActivityView.setReminderBubble(furnitureActivityConfig.getConfig().getReminderBubble());
            furnitureActivityView.setRoleAnimation(RoleAnimationVO.valueOf(furnitureActivityConfig.getConfig().getRoleAnimation()));
            furnitureActivityView.setFirstFree(furnitureActivityConfig.getConfig().isFirstFree());
            furnitureActivityView.setFirstPrice(FirstPriceVO.valueOf(furnitureActivityConfig.getConfig().getFirstPrice()));
            furnitureActivityView.setCumulativeDrawReward(CumulativeDrawRewardVO.valueOf(furnitureActivityConfig.getConfig().getCumulativeDrawReward()));
        }

        // 关联的家具套组基本信息
        if (activityId2FurnitureGroupIdsMap.containsKey(furnitureActivityConfig.getId())) {
            List<Integer> furnitureGroupIds = activityId2FurnitureGroupIdsMap.get(furnitureActivityConfig.getId());
            furnitureActivityView.setFurnitureGroups(furnitureGroupIds.stream().map(furnitureGroupId -> {
                String furnitureGroupName = furnitureGroupMap.get(furnitureGroupId);
                return new BasicFurnitureGroupView().setId(furnitureGroupId).setName(furnitureGroupName);
            }).collect(Collectors.toList()));
        }

        return furnitureActivityView;
    }

    @Data
    @Accessors(chain = true)
    public static class RoleAnimationVO implements Serializable {

        private static final long serialVersionUID = 3277392453065239075L;
        private Integer furnitureGroupId;
        private Double zoomRatio;
        private List<CostumeBlindBoxActivity.RoleConfig> roleConfigs;

        public static RoleAnimationVO valueOf(FurnitureActivityConfig.RoleAnimation roleAnimation) {
            if (roleAnimation == null) {
                return null;
            }
            RoleAnimationVO vo = new RoleAnimationVO();
            vo.setFurnitureGroupId(roleAnimation.getFurnitureGroupId());
            vo.setZoomRatio(roleAnimation.getZoomRatio());
            vo.setRoleConfigs(roleAnimation.getRoleConfigs());
            return vo;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class FirstPriceVO implements Serializable {

        private static final long serialVersionUID = 2386126194988973560L;
        private Integer kkbPrice;
        private String cornerText;

        public static FirstPriceVO valueOf(FurnitureActivityConfig.FirstPrice firstPrice) {
            if (firstPrice == null) {
                return null;
            }
            FirstPriceVO vo = new FirstPriceVO();
            vo.setKkbPrice(firstPrice.getKkbPrice());
            vo.setCornerText(firstPrice.getCornerText());
            return vo;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class CumulativeDrawRewardVO implements Serializable {

        private static final long serialVersionUID = -2522085458536789562L;
        private String rewardText;
        private ImageInfoView rewardImage;
        private List<ActivityRewardView> rewards;

        public static CumulativeDrawRewardVO valueOf(FurnitureActivityConfig.CumulativeDrawReward cumulativeDrawReward) {
            if (cumulativeDrawReward == null) {
                return null;
            }
            CumulativeDrawRewardVO vo = new CumulativeDrawRewardVO();
            vo.setRewardText(cumulativeDrawReward.getRewardText());
            vo.setRewardImage(ImageInfoView.valueOf(cumulativeDrawReward.getRewardImage()));
            vo.setRewards(cumulativeDrawReward.getRewards().stream().map(ActivityRewardView::valueOf).collect(Collectors.toList()));
            return vo;
        }
    }
}
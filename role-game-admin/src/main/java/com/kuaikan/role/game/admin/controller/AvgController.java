package com.kuaikan.role.game.admin.controller;

import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.role.game.admin.biz.AvgBiz;
import com.kuaikan.role.game.admin.biz.AvgTextEditorBiz;
import com.kuaikan.role.game.admin.enums.AvgChapterOrderTypeEnum;
import com.kuaikan.role.game.admin.enums.AvgProjectOrderTypeEnum;
import com.kuaikan.role.game.admin.model.param.AnchorUpdateParam;
import com.kuaikan.role.game.admin.model.param.AvgChapterCreateParam;
import com.kuaikan.role.game.admin.model.param.AvgChapterDeleteParam;
import com.kuaikan.role.game.admin.model.param.AvgChapterUpdateParam;
import com.kuaikan.role.game.admin.model.param.AvgDirSaveOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.AvgFileQueryParam;
import com.kuaikan.role.game.admin.model.param.AvgGyroscopeSaveParam;
import com.kuaikan.role.game.admin.model.param.AvgHotZoneAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.AvgOriginFileSaveOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.AvgProjectDeleteParam;
import com.kuaikan.role.game.admin.model.param.AvgProjectUpdateParam;
import com.kuaikan.role.game.admin.model.param.AvgUIStyleZipFileCreateParam;
import com.kuaikan.role.game.admin.model.view.AvgBaseImageView;
import com.kuaikan.role.game.admin.model.view.AvgDirView;
import com.kuaikan.role.game.admin.model.view.AvgGyroscopeFileView;
import com.kuaikan.role.game.admin.model.view.AvgGyroscopeView;
import com.kuaikan.role.game.admin.model.view.AvgHotZoneView;
import com.kuaikan.role.game.admin.model.view.AvgOriginFileView;
import com.kuaikan.role.game.admin.model.view.AvgZipUploadView;
import com.kuaikan.role.game.admin.model.view.PageResult;
import com.kuaikan.role.game.admin.utils.ResponseUtils;
import com.kuaikan.role.game.api.rpc.param.BatchQueryAvgParam;
import com.kuaikan.role.game.api.rpc.param.CheckTopicAvgParam;
import com.kuaikan.role.game.api.rpc.param.UpdateResourceAvgParam;
import com.kuaikan.role.game.api.rpc.param.avg.AvgTextBatchUpdateParam;
import com.kuaikan.role.game.api.rpc.param.avg.AvgTextUpdateParam;

/**
 * <AUTHOR>
 * @date 2024/5/23
 */
@Slf4j
@RestController
@RequestMapping("/v2/admin/role/game/avg")
public class AvgController {

    public static final String CHAPTER = "chapter_";
    public static final String XLS = ".xls";
    public static final String XLSX = ".xlsx";

    @Resource
    private AvgBiz avgBiz;

    @Resource
    private AvgTextEditorBiz avgTextEditorBiz;

    @GetMapping("file/wash")
    public void washData(int type) {
        avgBiz.washData(type);
    }


    @PostMapping("project/create")
    public Map<String, Object> createProject() {
        return ResponseUtils.valueOf(avgBiz.createProject());
    }

    @PostMapping("project/update")
    public Map<String, Object> updateProject(@RequestBody AvgProjectUpdateParam avgProjectUpdateParam) {
        return ResponseUtils.valueOf(avgBiz.updateProject(avgProjectUpdateParam));
    }

    @PostMapping("project/delete")
    public Map<String, Object> updateProject(@RequestBody AvgProjectDeleteParam avgProjectDeleteParam) {
        return ResponseUtils.valueOf(avgBiz.deleteProject(avgProjectDeleteParam));
    }

    @GetMapping("project/list")
    public Map<String, Object> projectList(@RequestParam(name = "orderType", required = false, defaultValue = "1") Integer orderTypeId,
                                           @RequestParam("page") int page, @RequestParam("pageSize") int pageSize) {
        AvgProjectOrderTypeEnum orderType = AvgProjectOrderTypeEnum.getByCode(orderTypeId);
        if (orderType == null) {
            return ResponseUtils.valueOf(BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "orderType参数错误"));
        }
        return ResponseUtils.valueOf(avgBiz.queryAvgProjectList(orderType, page, pageSize));
    }

    @PostMapping("chapter/create")
    public Map<String, Object> createChapter(@RequestBody AvgChapterCreateParam avgChapterCreateParam) {
        return ResponseUtils.valueOf(avgBiz.createChapter(avgChapterCreateParam, false));
    }

    @PostMapping("chapter/update")
    public Map<String, Object> updateChapter(@RequestBody AvgChapterUpdateParam avgChapterUpdateParam) {
        return ResponseUtils.valueOf(avgBiz.updateChapter(avgChapterUpdateParam,false));
    }

    @PostMapping("chapter/delete")
    public Map<String, Object> deleteChapter(@RequestBody AvgChapterDeleteParam avgChapterDeleteParam) {
        return ResponseUtils.valueOf(avgBiz.deleteChapter(avgChapterDeleteParam,false));
    }

    @GetMapping("chapter/list")
    public Map<String, Object> chapterList(@RequestParam(name = "orderType", required = false, defaultValue = "1") Integer orderTypeId,
                                           @RequestParam(name = "type", required = false, defaultValue = "1") Integer type, @RequestParam String projectId,
                                           @RequestParam("page") int page, @RequestParam("pageSize") int pageSize) {
        AvgChapterOrderTypeEnum orderType = AvgChapterOrderTypeEnum.getByCode(orderTypeId);
        if (orderType == null) {
            return ResponseUtils.valueOf(BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "orderType参数错误"));
        }
        return ResponseUtils.valueOf(avgBiz.queryAvgChapterList(orderType, projectId, type, page, pageSize));
    }

    @GetMapping("chapter/query")
    public Map<String, Object> chapterQuery(@RequestParam(required = false) String name, @RequestParam(required = false) Integer id) {
        return ResponseUtils.valueOf(avgBiz.queryAvgChapterList(name, id));
    }

    /**
     * 新版接口，替代chapter/Import
     * @param file
     * @param chapterId
     * @return
     */
    @PostMapping("chapter/upload")
    public Map<String, Object> chapterUpload(@RequestParam("file") MultipartFile file, @RequestParam("chapterId") Integer chapterId) {
        String fileName = file.getOriginalFilename();
        if (!(fileName.endsWith(XLSX) || fileName.endsWith(XLS))) {
            return ResponseUtils.valueOf(BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件格式错误"));
        }
        BizResult<Integer> bizResult = avgBiz.uploadAvgChapter(chapterId, file);
        return ResponseUtils.valueOf(bizResult);
    }

    /**
     * 段落单条文本保存
     *
     * @param param 要更新的文本内容
     * @return 段落id
     */
    @PostMapping("/chapterText/chapterTextSave")
    public Map<String, Object> chapterTextSave(@RequestBody AvgTextUpdateParam param) {
        return ResponseUtils.valueOf(avgTextEditorBiz.saveText(param));
    }

    /**
     * 根据文本id查询文本配置
     *
     * @param chapterId 段落id
     * @param textId    文本id
     * @return 文本行配置
     */
    @GetMapping("/chapterText/findTextByTextId")
    public Map<String, Object> findTextByTextId(@RequestParam("chapterId") Integer chapterId,
        @RequestParam("textId") String textId) {
        return ResponseUtils.valueOf(avgTextEditorBiz.findTextByTextId(chapterId, textId));
    }

    /**
     * 根据段落id和文本id,新增空白文本行
     *
     * @param param 段落id+文本id
     * @return 文本id
     */
    @PostMapping("/chapterText/addBlankText")
    public Map<String, Object> addBlankText(@RequestBody AvgTextUpdateParam param) {
        return ResponseUtils.valueOf(avgTextEditorBiz.addBlankText(param));
    }

    /**
     * 根据段落id分页查询对应文本配置
     *
     * @param chapterId 段落id
     * @param textId 列表定位到的某个textId
     * @param roleName  角色名称,非必传
     * @param pageNum   页数,默认1
     * @param pageSize  条数,默认20
     * @param startNum  截取开始行数,不分页时才处理
     * @param endNum    截取终止行数,不分页时才处理
     */
    @GetMapping("/chapterText/queryPageByChapterId")
    public Map<String, Object> queryPageByChapterId(@RequestParam("chapterId") Integer chapterId,
                                                    @RequestParam(value = "textId", defaultValue = "") String textId,
                                                    @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                    @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
                                                    @RequestParam(value = "roleName", defaultValue = "") String roleName,
                                                    @RequestParam(value = "startNum", required = false) Integer startNum,
                                                    @RequestParam(value = "endNum", required = false) Integer endNum) {
        return ResponseUtils.valueOf(avgTextEditorBiz.queryPageByChapterId(chapterId, textId, pageNum, pageSize, roleName, startNum, endNum));
    }

    /**
     * 根据段落id和文本id,批量替换文本配置
     *
     * @param param 文本批量替换字段传参
     * @return 替换后的段落id
     */
    @PostMapping("/chapterText/updateBatchByTextIds")
    public Map<String, Object> updateBatchByTextIds(@RequestBody AvgTextBatchUpdateParam param) {
        return ResponseUtils.valueOf(avgTextEditorBiz.updateBatchByTextIds(param));
    }

    /**
     * 根据段落id返回所有角色名称结果集
     *
     * @param chapterId 段落id
     * @return 角色名称结果集
     */
    @GetMapping("/chapterText/queryAllRoleNameByChapterId")
    public Map<String, Object> queryAllRoleNameByChapterId(@RequestParam("chapterId") Integer chapterId) {
        return ResponseUtils.valueOf(avgTextEditorBiz.queryAllRoleNameByChapterId(chapterId));
    }

    /**
     * 根据段落id查询所有文本id
     *
     * @param chapterId 段落id
     * @return 所有文本id
     */
    @GetMapping("/chapterText/queryAllTextIdByChapterId")
    public Map<String, Object> queryAllTextIdByChapterId(@RequestParam("chapterId") Integer chapterId) {
        return ResponseUtils.valueOf(avgTextEditorBiz.queryAllTextIdByChapterId(chapterId));
    }

    @PostMapping("/chapterText/chapterTextPreview")
    public Map<String, Object> chapterTextPreview(@RequestBody AvgTextUpdateParam param) {
        return ResponseUtils.valueOf(avgTextEditorBiz.chapterTextPreview(param));
    }

    @GetMapping("file/check/exit")
    public Map<String, Object> checkExit(@RequestParam("fileName") String fileName,
                                         @RequestParam(name = "dirId", required = false, defaultValue = "0") Integer dirId) {
        BizResult<AvgOriginFileView> bizResult = avgBiz.checkExit(fileName, dirId);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("file/check/topic")
    public Map<String, Object> checkTopic(@RequestBody CheckTopicAvgParam checkTopicAvgParam) {
        return ResponseUtils.valueOf(avgBiz.checkTopic(checkTopicAvgParam));
    }

    @PostMapping("file/upload")
    public Map<String, Object> avgFileUpload(@RequestParam("file") MultipartFile file, @RequestParam("key") String key,
                                             @RequestParam(name = "type", required = false) Integer type,
                                             @RequestParam(name = "dirId", required = false) Integer dirId) {
        BizResult<String> bizResult = avgBiz.avgFileUpload(file, key, type, dirId);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("/file/create")
    public Map<String, Object> fileCreate(@Valid @RequestBody AvgOriginFileSaveOrUpdateParam param) {
        BizResult<Void> bizResult = avgBiz.createFile(param,false);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("file/zip/upload")
    public Map<String, Object> avgZipFileUpload(@RequestParam("file") MultipartFile file,
                                                @RequestParam(name = "type") Integer type,
                                                @RequestParam(name = "dirId") Integer dirId) {
        BizResult<List<AvgZipUploadView>> bizResult = avgBiz.avgZipFileUpload(file, type, dirId);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("file/zip/download")
    public Map<String, Object> avgZipFileDownload(@RequestParam("id") Integer id) {
        BizResult<String> bizResult = avgBiz.avgZipFileDownload(id);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("/file/zip/create")
    public Map<String, Object> zipFileCreate(@RequestBody AvgUIStyleZipFileCreateParam param) {
        BizResult<Void> bizResult = avgBiz.createZipFile(param, false);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("/file/zip/update")
    public Map<String, Object> zipFileUpdate(@RequestBody AvgUIStyleZipFileCreateParam param) {
        BizResult<Void> bizResult = avgBiz.updateZipFile(param);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("/dir/create")
    public Map<String, Object> dirCreate(@Valid @RequestBody AvgDirSaveOrUpdateParam param) {
        BizResult<Integer> bizResult = avgBiz.createDir(param, false);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("/dir/update")
    public Map<String, Object> dirUpdate(@Valid @RequestBody AvgDirSaveOrUpdateParam param) {
        BizResult<Void> bizResult = avgBiz.updateDir(param, false);
        return ResponseUtils.valueOf(bizResult);
    }

    @GetMapping("/dir/list")
    public Map<String, Object> dirListByType(@RequestParam("type") int type, @RequestParam("orderByAndSort") String orderByAndSort,
                                             @RequestParam("page") int page, @RequestParam("pageSize") int pageSize) {
        BizResult<PageResult<AvgDirView>> bizResult = avgBiz.dirListByType(type, page, pageSize, orderByAndSort);
        return ResponseUtils.valueOf(bizResult);
    }

    @GetMapping("/dir/listAll")
    public Map<String, Object> dirListAllByType(@RequestParam("type") int type, @RequestParam("orderByAndSort") String orderByAndSort) {
        BizResult<List<AvgDirView>> bizResult = avgBiz.dirListAllByType(type, orderByAndSort);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("/dir/listWithFiles")
    public Map<String, Object> dirListWithFilesByType(@RequestBody AvgFileQueryParam param) {
        BizResult<PageResult<AvgDirView>> bizResult = avgBiz.dirListWithFilesByType(param);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("/dir/delete")
    public Map<String, Object> deleteDir(@RequestParam("id") int id) {
        avgBiz.deleteDir(id);
        return ResponseUtils.success();
    }

    @PostMapping("/file/update")
    public Map<String, Object> fileUpdate(@Valid @RequestBody AvgOriginFileSaveOrUpdateParam param) {
        BizResult<Void> bizResult = avgBiz.updateFile(param);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("/anchor/update")
    public Map<String, Object> anchorUpdate(@RequestBody AnchorUpdateParam param) {
        BizResult<Void> bizResult = avgBiz.updateAnchor(param);
        return ResponseUtils.valueOf(bizResult);
    }

    @GetMapping("/file/list")
    public Map<String, Object> fileListByType(@RequestParam("type") int type, @RequestParam("orderByAndSort") String orderByAndSort,
                                              @RequestParam("page") int page, @RequestParam("pageSize") int pageSize) {
        BizResult<PageView<AvgOriginFileView>> bizResult = avgBiz.fileListByType(type, page, pageSize, orderByAndSort);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("/file/listByTypes")
    public Map<String, Object> fileListByType(@RequestBody AvgFileQueryParam param) {
        BizResult<PageView<AvgOriginFileView>> bizResult = avgBiz.fileListByTypes(param);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("/file/delete")
    public Map<String, Object> deleteFile(@RequestParam("id") int id) {
        avgBiz.deleteFile(id, false);
        return ResponseUtils.success();
    }

    @PostMapping("/resource/alias/delete")
    public Map<String, Object> deleteAlias(@RequestParam("fileId") int fileId, @RequestParam("type") int type) {
        avgBiz.deleteAlias(fileId, type);
        return ResponseUtils.success();
    }

    @PostMapping("hotZone/list")
    public Map<String, Object> avgHotZoneList(@RequestBody AvgFileQueryParam param) {
        BizResult<PageResult<AvgHotZoneView>> bizResult = avgBiz.avgHotZoneList(param);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("hotZone/create")
    public Map<String, Object> avgHotZoneCreate(@Valid @RequestBody AvgHotZoneAddOrUpdateParam param) {
        BizResult<Integer> bizResult = avgBiz.avgHotZoneCreate(param);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("hotZone/update")
    public Map<String, Object> avgHotZoneUpdate(@Valid @RequestBody AvgHotZoneAddOrUpdateParam param) {
        BizResult<Integer> bizResult = avgBiz.avgHotZoneUpdate(param);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("hotZone/delete")
    public Map<String, Object> avgHotZoneDelete(@RequestParam("id") Integer id) {
        BizResult<Void> bizResult = avgBiz.avgHotZoneDelete(id);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("hotZone/baseImg/upload")
    public Map<String, Object> avgHotZoneUpload(@RequestParam("file") MultipartFile file, @RequestParam("key") String key,
                                                @RequestParam("hotZoneId") Integer hotZoneId) {
        BizResult<String> bizResult = avgBiz.avgHotZoneUpload(file, key, hotZoneId);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("queryByIds")
    public Map<String, Object> queryByIds(@RequestBody BatchQueryAvgParam batchQueryAvgParam) {
        Map<String, Object> success = ResponseUtils.success();
        success.put("data", avgBiz.queryByIds(batchQueryAvgParam));
        return success;
    }

    @PostMapping("oldData/handle")
    public Map<String, Object> handleOldData() {
        avgBiz.handleOldData();
        return ResponseUtils.success();
    }

    @PostMapping("/online")
    public Map<String, Object> online(@RequestParam(name = "id") int id, @RequestParam(name = "type") int type) {
        return ResponseUtils.valueOf(avgBiz.online(id, type,false));
    }

    @PostMapping("/offline")
    public Map<String, Object> offline(@RequestParam(name = "id") int id, @RequestParam(name = "type") int type) {
        return ResponseUtils.valueOf(avgBiz.offline(id, type,false));
    }

    @PostMapping("/update/resource")
    public Map<String, Object> updateResource(@Valid @RequestBody UpdateResourceAvgParam updateResourceAvgParam) {
        return ResponseUtils.valueOf(avgBiz.updateResource(updateResourceAvgParam));
    }

    @GetMapping("/getAvgOriginFile")
    public Map<String, Object> getAvgOriginFile(@RequestParam(name = "avg_chapter_id") int avgChapterId) {
        return ResponseUtils.valueOf(avgBiz.getAvgOriginFile(avgChapterId));
    }

    @GetMapping("file/avgFileDownloadUrl")
    public Map<String, Object> avgFileDownloadUrl(@RequestParam("chapterId") int chapterId) {
        BizResult<String> bizResult = avgBiz.avgFileDownloadUrl(chapterId);
        return ResponseUtils.valueOf(bizResult);
    }

    @GetMapping("/changeAvgRelationData")
    public Map<String, Object> changeAvgRelationData() {
        return ResponseUtils.valueOf(avgBiz.changeAvgRelationData());
    }

    @GetMapping("/hisMaterialPush")
    public Map<String, Object> hisMaterialPush() {
        return ResponseUtils.valueOf(avgBiz.hisMaterialPush());
    }

    @PostMapping("record/handle")
    public Map<String, Object> handleRecordOldData() {
        avgBiz.handleRecordOldData();
        return ResponseUtils.success();
    }

    @PostMapping("fileAlias/handle")
    public Map<String, Object> handleFileAliasOldData() {
        return ResponseUtils.valueOf(avgBiz.handleFileAliasOldData());
    }


    @PostMapping("gyroscope/list")
    public Map<String, Object> avgGyroscopeList(@RequestBody AvgFileQueryParam param) {
        BizResult<PageResult<AvgGyroscopeView>> bizResult = avgBiz.avgGyroscopeViewList(param);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("gyroscope/save")
    public Map<String, Object> avgGyroscopeCreate(@Valid @RequestBody AvgGyroscopeSaveParam param) {
        BizResult<Integer> bizResult = avgBiz.avgGyroscopeSave(param);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("gyroscope/delete")
    public Map<String, Object> avgGyroscopeDelete(@RequestParam("id") int id) {
        BizResult<Void> bizResult = avgBiz.avgGyroscopeDelete(id);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("gyroscope/file/upload")
    public Map<String, Object> avgGyroscopeUpload(@RequestParam("file") MultipartFile file,
                                                @RequestParam("gyroscopeId") int gyroscopeId, @RequestParam("type") int type) {
        BizResult<AvgGyroscopeFileView> bizResult = avgBiz.avgGyroscopeUpload(file, gyroscopeId, type);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("/baseImg/upload")
    public Map<String, Object> avgBaseImageUpload(@RequestParam("file") MultipartFile file) {
        BizResult<AvgBaseImageView> bizResult = avgBiz.avgBaseImageUpload(file);
        return ResponseUtils.valueOf(bizResult);
    }

    @PostMapping("avgResourceTag/save")
    public Map<String, Object> avgResourceTagSave(@Valid @RequestBody  List<String> names) {
        BizResult<Integer> bizResult = avgBiz.avgResourceTagSave(names);
        return ResponseUtils.valueOf(bizResult);
    }
    @GetMapping("avgResourceTag/tagName/list")
    public Map<String, Object> avgTagNameList() {
        BizResult<List<String>> bizResult = avgBiz.avgTagNameList();
        return ResponseUtils.valueOf(bizResult);
    }
}

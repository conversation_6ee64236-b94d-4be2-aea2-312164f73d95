package com.kuaikan.role.game.admin.biz;

import static com.kuaikan.role.game.admin.component.QiniuComponent.FREE_BUCKET_NAME;
import static com.kuaikan.role.game.admin.component.QiniuComponent.PAY_BUCKET_NAME;
import static com.kuaikan.role.game.admin.config.ThreadPoolConfig.BIZ_EXECUTOR;
import static com.kuaikan.role.game.api.util.AvgChapterUtils.EXCEL_KEY;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.metadata.data.CellData;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.cdn.core.CdnHandler;
import com.kuaikan.comic.avg.model.dto.AvgChapterDTO;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.dubbomodel.TopicBasicDetailModel;
import com.kuaikan.comic.service.DataFootStoneService;
import com.kuaikan.comic.utils.CdnUtil;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.idgenerator.sdk.BizIdGenerator;
import com.kuaikan.idgenerator.sdk.BufferedIdGenerator;
import com.kuaikan.role.game.admin.common.MaterialConstants;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.component.AvgChapterComponent;
import com.kuaikan.role.game.admin.component.MqComponent;
import com.kuaikan.role.game.admin.component.QiniuComponent;
import com.kuaikan.role.game.admin.component.StartUpComponent;
import com.kuaikan.role.game.admin.config.ApolloConfig;
import com.kuaikan.role.game.admin.converter.AvgChapterConverter;
import com.kuaikan.role.game.admin.enums.AvgChapterOrderTypeEnum;
import com.kuaikan.role.game.admin.enums.AvgProjectOrderTypeEnum;
import com.kuaikan.role.game.admin.model.bo.AvgResourceBO;
import com.kuaikan.role.game.admin.model.param.AnchorUpdateParam;
import com.kuaikan.role.game.admin.model.param.AvgChapterCreateParam;
import com.kuaikan.role.game.admin.model.param.AvgChapterDeleteParam;
import com.kuaikan.role.game.admin.model.param.AvgChapterUpdateParam;
import com.kuaikan.role.game.admin.model.param.AvgDirSaveOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.AvgFileQueryParam;
import com.kuaikan.role.game.admin.model.param.AvgGyroscopeSaveParam;
import com.kuaikan.role.game.admin.model.param.AvgHotZoneAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.AvgOriginFileSaveOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.AvgProjectDeleteParam;
import com.kuaikan.role.game.admin.model.param.AvgProjectUpdateParam;
import com.kuaikan.role.game.admin.model.param.AvgUIStyleZipFileCreateParam;
import com.kuaikan.role.game.admin.model.view.AvgBaseImageView;
import com.kuaikan.role.game.admin.model.view.AvgChapterVO;
import com.kuaikan.role.game.admin.model.view.AvgDirView;
import com.kuaikan.role.game.admin.model.view.AvgGyroscopeFileView;
import com.kuaikan.role.game.admin.model.view.AvgGyroscopeView;
import com.kuaikan.role.game.admin.model.view.AvgHotZoneView;
import com.kuaikan.role.game.admin.model.view.AvgOriginFileInfoView;
import com.kuaikan.role.game.admin.model.view.AvgOriginFileView;
import com.kuaikan.role.game.admin.model.view.AvgProjectVO;
import com.kuaikan.role.game.admin.model.view.AvgZipUploadView;
import com.kuaikan.role.game.admin.model.view.PageResult;
import com.kuaikan.role.game.admin.remote.DigitalAssetRemoteService;
import com.kuaikan.role.game.admin.repository.AvgClosetRepository;
import com.kuaikan.role.game.admin.repository.AvgFileAliasRelationRepository;
import com.kuaikan.role.game.admin.repository.AvgFileChapterRelationRepository;
import com.kuaikan.role.game.admin.repository.AvgRepository;
import com.kuaikan.role.game.admin.utils.FileUtils;
import com.kuaikan.role.game.admin.utils.ZipUtils;
import com.kuaikan.role.game.api.bean.AvgChapter;
import com.kuaikan.role.game.api.bean.AvgChapter.Text;
import com.kuaikan.role.game.api.bean.AvgDir;
import com.kuaikan.role.game.api.bean.AvgFileAliasRelation;
import com.kuaikan.role.game.api.bean.AvgFileChapterRelation;
import com.kuaikan.role.game.api.bean.AvgGyroscope;
import com.kuaikan.role.game.api.bean.AvgHotZone;
import com.kuaikan.role.game.api.bean.AvgOriginFile;
import com.kuaikan.role.game.api.bean.AvgProject;
import com.kuaikan.role.game.api.bean.AvgResourceTag;
import com.kuaikan.role.game.api.bean.CommonAudio;
import com.kuaikan.role.game.api.bean.CommonVideo;
import com.kuaikan.role.game.api.bean.UserAvgChapterRecord;
import com.kuaikan.role.game.api.enums.AnchorLocationType;
import com.kuaikan.role.game.api.enums.AudioBusType;
import com.kuaikan.role.game.api.enums.AvgChapterFileRefreshStatus;
import com.kuaikan.role.game.api.enums.AvgChapterSyncTypeEnum;
import com.kuaikan.role.game.api.enums.AvgChapterType;
import com.kuaikan.role.game.api.enums.AvgFileResourceType;
import com.kuaikan.role.game.api.enums.AvgFileStatusType;
import com.kuaikan.role.game.api.enums.AvgFileType;
import com.kuaikan.role.game.api.enums.AvgGyroscopeFileType;
import com.kuaikan.role.game.api.enums.AvgHotZoneType;
import com.kuaikan.role.game.api.enums.AvgResourceType;
import com.kuaikan.role.game.api.enums.CdnPayType;
import com.kuaikan.role.game.api.enums.CommonAudioStatus;
import com.kuaikan.role.game.api.enums.CommonStatus;
import com.kuaikan.role.game.api.enums.CommonVideoStatus;
import com.kuaikan.role.game.api.enums.OperateTypeEnum;
import com.kuaikan.role.game.api.model.excel.AvgChapterTextExcel;
import com.kuaikan.role.game.api.rpc.param.AvgChapterVOBatchQueryParam;
import com.kuaikan.role.game.api.enums.OperateTypeEnum;
import com.kuaikan.role.game.api.model.excel.AvgChapterTextExcel;
import com.kuaikan.role.game.api.rpc.param.BatchQueryAvgParam;
import com.kuaikan.role.game.api.rpc.param.CheckTopicAvgParam;
import com.kuaikan.role.game.api.rpc.param.UpdateResourceAvgParam;
import com.kuaikan.role.game.api.rpc.result.AvgChapterModel;
import com.kuaikan.role.game.api.rpc.result.AvgChapterSimpleModel;
import com.kuaikan.role.game.api.service.AvgService;
import com.kuaikan.role.game.api.util.AvgChapterUploadUtils;
import com.kuaikan.role.game.api.util.AvgChapterUtils;
import com.kuaikan.role.game.api.util.GsonUtils;
import com.kuaikan.role.game.api.util.excel.listener.AvgChapterTextExcelListener;
import com.kuaikan.role.game.common.enums.CacheConfig;

/**
 * <AUTHOR>
 * @date 2024/5/23
 */
@Service
@Slf4j
public class AvgBiz {

    @Resource
    private ApolloConfig apolloConfig;
    @Resource
    private AvgRepository avgRepository;

    @Resource
    private QiniuComponent qiniuComponent;

    @Resource
    private StartUpComponent startUpComponent;

    @Resource
    private AvgChapterConverter avgChapterConverter;

    @Resource
    private MqComponent mqComponent;

    @Resource
    private AvgFileChapterRelationRepository avgFileChapterRelationRepository;

    @Resource
    private DataFootStoneService dataFootStoneService;

    @Resource
    private DigitalAssetRemoteService digitalAssetRemoteService;

    @Resource
    private AvgService avgService;

    @Resource
    private AvgFileAliasRelationRepository avgFileAliasRelationRepository;
    @Resource
    private AvgClosetRepository avgClosetRepository;

    @Resource
    private AvgChapterComponent avgChapterComponent;

    private static final String AVG_FILE_PATH = "avg/";

    private static final String ZIP_FILE_PATH = "/data/avg/zip_file/";

    public static final String SLASH = "/";

    private final EnumSet<AvgFileType> ZIP_FILE_TYPES = EnumSet.of(AvgFileType.UI_STYLE, AvgFileType.SPINE, AvgFileType.Q_SPINE);

    /** SPINE类型文件后缀 */
    private final Set<String> VALID_SPINE_FILENAME_SUFFIX_SET = Sets.newHashSet(".json", ".atlas", ".png");

    private static final Set<Integer> AUDIO_TYPE_SET = Sets.newHashSet(AvgFileType.BGM.getCode(),
            AvgFileType.SOUND_EFFECT.getCode(), AvgFileType.CV.getCode(), AvgFileType.HIGHLIGHT_VIDEO_VOICE.getCode());

    private static final Set<Integer> VIDEO_TYPE_SET = ImmutableSet.of(AvgFileType.HIGHLIGHT_VIDEO.getCode(),
            AvgFileType.DYNAMIC_BACKGROUND.getCode());

    public static final Set<Integer> SPINE_TYPE_SET = ImmutableSet.of(AvgFileType.DYNAMIC_ROLE.getCode(),
            AvgFileType.UI_STYLE.getCode(), AvgFileType.SPINE.getCode(), AvgFileType.Q_SPINE.getCode());

    public BizResult<Void> createProject() {
        AvgProject avgProject = new AvgProject().setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setChapterCount(0)
                .setStatus(CommonStatus.ONLINE.getCode());
        avgRepository.insertAvgProject(avgProject);
        Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_PROJECT_ADD).add("id", avgProject.getObjectId());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<Void> updateProject(AvgProjectUpdateParam avgProjectAddOrUpdateParam) {
        String projectId = avgProjectAddOrUpdateParam.getProjectId();
        if (StringUtils.isBlank(projectId)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "项目id不能为空");
        }
        if (StringUtils.isBlank(avgProjectAddOrUpdateParam.getName())) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "项目名称不能为空");
        }
        AvgProject avgProject = avgRepository.queryAvgProjectByObjectId(projectId);
        if (avgProject == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "项目Id不存在");
        }
        if (avgProjectAddOrUpdateParam.getTopicId() != null) {
            AvgProject existTopicProject = avgRepository.queryAvgProjectByTopicId(avgProjectAddOrUpdateParam.getTopicId());
            if (existTopicProject != null && !Objects.equals(existTopicProject.getObjectId(), avgProject.getObjectId())) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "此专题已关联其他项目");
            }
        }
        avgProject.setName(avgProjectAddOrUpdateParam.getName());
        avgProject.setTopicId(avgProjectAddOrUpdateParam.getTopicId());
        avgProject.setStyleId(avgProjectAddOrUpdateParam.getStyleId());
        avgRepository.updateAvgProject(avgProject);
        Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_PROJECT_UPDATE)
                .add("id", projectId)
                .add("data", GsonUtils.toJson(avgProjectAddOrUpdateParam));
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> deleteProject(AvgProjectDeleteParam avgProjectDeleteParam) {
        String projectId = avgProjectDeleteParam.getProjectId();
        if (StringUtils.isBlank(projectId)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "项目id不能为空");
        }
        AvgProject avgProject = avgRepository.queryAvgProjectByObjectId(projectId);
        if (avgProject == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "项目Id不存在");
        }
        avgRepository.deleteAvgProjectByProjectId(projectId);
        avgRepository.deleteAvgChapterByProjectId(projectId);
        Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_PROJECT_DELETE).add("id", projectId);
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult queryAvgProjectList(AvgProjectOrderTypeEnum orderTypeEnum, int page, int pageSize) {
        int total = avgRepository.countAllAvgProject();
        List<AvgProject> avgProjects = avgRepository.queryAvgProjectList(orderTypeEnum, page, pageSize);
        List<AvgProjectVO> avgProjectVOList = avgChapterConverter.toAvgProjectVOList(avgProjects);
        PageView<AvgProjectVO> pageView = PageView.form(total, avgProjectVOList);
        return BizResult.success(pageView);
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> createChapter(AvgChapterCreateParam avgChapterCreateParam, boolean isRpcRequest) {
        String projectId = avgChapterCreateParam.getProjectId();
        if (StringUtils.isBlank(projectId)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "项目id不能为空");
        }
        int type = avgChapterCreateParam.getType();
        if (AvgChapterType.getByCode(type) == AvgChapterType.UNKNOWN) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "章节类型不支持");
        }
        AvgProject avgProject = avgRepository.queryAvgProjectByObjectId(projectId);
        if (avgProject == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "项目Id不存在");
        }
        avgProject.setChapterCount(avgProject.getChapterCount() + 1);
        avgRepository.updateAvgProject(avgProject);
        Integer maxChapterId = avgRepository.queryMaxChapterId();
        AvgChapter avgChapter = new AvgChapter().setChapterId(maxChapterId + 1)
                .setProjectId(avgChapterCreateParam.getProjectId())
                .setType(avgChapterCreateParam.getType())
                .setStatus(CommonStatus.ONLINE.getCode());
        avgRepository.insert(avgChapter);
        if (!isRpcRequest) {
            Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_CHAPTER_ADD).add("id", maxChapterId);
            OperateLogUtils.asyncRecord(operation);
        }
        return BizResult.success();
    }

    public BizResult<Void> updateChapter(AvgChapterUpdateParam avgChapterUpdateParam, boolean isRpcRequest) {
        String name = avgChapterUpdateParam.getName();
        Integer styleId = avgChapterUpdateParam.getStyleId();
        Integer chapterId = avgChapterUpdateParam.getChapterId();
        if (chapterId == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "章节id不能为空");
        }
        AvgChapter dbAvgChapter = avgRepository.queryAvgChapterByChapterId(chapterId);
        if (dbAvgChapter == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "章节不存在");
        }
        AvgDir avgDir = null;
        if (Objects.nonNull(avgChapterUpdateParam.getStyleId())) {
            avgDir = avgRepository.queryAvgDirById(avgChapterUpdateParam.getStyleId());
            if (avgDir == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "UI风格不存在");
            }
        }
        AvgChapter avgChapter = new AvgChapter().setChapterId(chapterId).setChapterName(name).setStyleId(styleId);
        avgRepository.updateAvgChapterEmptyStyleId(avgChapter);

        if (Objects.nonNull(styleId)) {
            Integer oldStyleId = dbAvgChapter.getStyleId();
            AvgFileChapterRelation avgFileChapterRelation = Objects.nonNull(oldStyleId)
                    ? avgFileChapterRelationRepository.queryByTypeAndFileIdAndChapterId(oldStyleId, chapterId,
                    AvgResourceType.DIR.getCode())
                    : null;
            if (avgFileChapterRelation != null) {
                avgFileChapterRelationRepository.deleteByIds(Lists.newArrayList(avgFileChapterRelation.getId()));
            }
            AvgFileChapterRelation relation = AvgFileChapterRelation.toBean(styleId, chapterId, AvgResourceType.DIR.getCode());
            avgFileChapterRelationRepository.batchInsert(Lists.newArrayList(relation));
        }
        if (!isRpcRequest) {
            Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_CHAPTER_UPDATE)
                    .add("id", chapterId)
                    .add("data", GsonUtils.toJson(avgChapterUpdateParam));
            OperateLogUtils.asyncRecord(operation);
        }
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> deleteChapter(AvgChapterDeleteParam avgChapterDeleteParam, boolean isRpcRequest) {
        Integer chapterId = avgChapterDeleteParam.getChapterId();
        if (chapterId == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "章节id不能为空");
        }
        AvgChapter avgChapter = avgRepository.queryAvgChapterByChapterId(chapterId);
        if (avgChapter == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "章节不存在");
        }
        AvgProject avgProject = avgRepository.queryAvgProjectByObjectId(avgChapter.getProjectId());
        if (avgProject == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "项目不存在");
        }
        avgProject.setChapterCount(avgProject.getChapterCount() - 1);
        avgRepository.updateAvgProject(avgProject);
        avgRepository.deleteAvgChapterByChapterId(chapterId);
        mqComponent.avgChapterChange(avgChapter);
        List<Integer> relationIds = avgFileChapterRelationRepository.queryByChapterId(chapterId)
                .stream()
                .map(AvgFileChapterRelation::getId)
                .collect(Collectors.toList());
        avgFileChapterRelationRepository.deleteByIds(relationIds);
        if (!isRpcRequest) {
            Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_CHAPTER_DELETE).add("id", chapterId);
            OperateLogUtils.asyncRecord(operation);
        }
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Integer> uploadAvgChapter(Integer chapterId, MultipartFile file) {
        if (chapterId == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "章节id不能为空");
        }
        AvgChapter dbAvgChapter = avgRepository.queryAvgChapterByChapterId(chapterId);
        if (dbAvgChapter == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "章节不存在");
        }
        List<AvgChapterTextExcel> avgChapterTextList;
        try {
            avgChapterTextList =  AvgChapterUploadUtils.getAvgChapterList(file);
        } catch (ExcelDataConvertException e) {
            Integer rowIndex = e.getRowIndex();
            Integer columnIndex = e.getColumnIndex();
            CellData<?> cellData = e.getCellData();
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(),
                    "配置表格式错误,第" + rowIndex + "行,第" + columnIndex + "列,内容为" + cellData.getStringValue());
        } catch (Exception e) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "配置表格式错误");
        }
        String chapterFileKey;
        try {
            chapterFileKey = saveFile(file);
        } catch (IOException e) {
            log.error("uploadAvgChapter error, originalFilename:{}", file.getOriginalFilename(), e);
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "文件上传失败");
        }
        String fileName = file.getOriginalFilename();
        List<AvgChapter.Text> avgChapterList = avgChapterTextList.stream()
                .map(AvgChapterTextExcel::toAvgChapterText)
                .collect(Collectors.toList());
        List<String> hotZoneNameList = avgChapterList.stream().map(Text::getHotZone).collect(Collectors.toList());
        List<AvgHotZone> avgHotZoneList = avgRepository.queryAvgHotZoneByNames(hotZoneNameList);
        Map<String, AvgHotZone> hotZoneMap = avgHotZoneList.stream().collect(Collectors.toMap(AvgHotZone::getName, Function.identity()));
        List<Integer> nextChapterIds = avgChapterList.stream().map(AvgChapter.Text::getNextChapter).collect(Collectors.toList());
        List<AvgChapter> nextChapters = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(nextChapterIds)) {
            nextChapters = avgRepository.queryAvgChapterByChapterIds(nextChapterIds, AvgChapterType.TEXT.getCode());
        }
        List<Integer> insertChapterIds = avgChapterList.stream()
                .map(AvgChapter.Text::getInsertChapter)
                .collect(Collectors.toList());
        List<AvgChapter> insertChapters = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(nextChapterIds)) {
            insertChapters = avgRepository.queryAvgChapterByChapterIds(insertChapterIds, AvgChapterType.INSERT_CHAPTER.getCode());
        }
        List<String> errors = AvgChapter.verify(dbAvgChapter.getType(), avgChapterList, nextChapters, insertChapters, hotZoneMap);
        if (CollectionUtils.isNotEmpty(errors)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), String.join("\r", errors));
        }
        AvgChapter avgChapter = new AvgChapter().setChapterFileName(fileName)
                .setChapterFileKey(chapterFileKey)
                .setChapterId(chapterId)
                .setTextList(avgChapterList)
                .setStyleId(dbAvgChapter.getStyleId());
        avgChapter.setTextContentMd5(avgChapter.getTextContentMd5());
        avgRepository.updateAvgChapter(avgChapter);
        avgChapterComponent.updateChapterResourceRelations(avgChapter);
        avgChapterComponent.pushDigitalAsset(avgChapter.getChapterId(), AvgChapterSyncTypeEnum.UPDATE);
        Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_FILE_UPDATE).add("id", chapterId).add("data", GsonUtils.toJson(avgChapter));
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success(chapterId);
    }

    public BizResult<String> avgFileUpload(MultipartFile file, String key, Integer type, Integer dirId) {
        // 名称及格式尺寸校验
        String msg = fileValidate(file, type, dirId);
        if (Strings.isNotBlank(msg)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), msg);
        }
        String fileKey = AVG_FILE_PATH + System.currentTimeMillis() + "/" + key;
        try {
            qiniuComponent.uploadWithStream(PAY_BUCKET_NAME, file.getBytes(), fileKey);
        } catch (IOException e) {
            log.error("avgFileUpload error", e);
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR);
        }
        return BizResult.success(fileKey);
    }

    public BizResult<String> avgFileDownloadUrl(int chapterId) {
        if (chapterId <= 0) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "章节id不能为空");
        }
        AvgChapter dbAvgChapter = avgRepository.queryAvgChapterByChapterId(chapterId);
        if (Objects.isNull(dbAvgChapter)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落不存在");
        }
        String domain = CdnUtil.getDefaultDomainWithBackSlash();
        // 内容没变，不需要刷新excel关联文件
        if (dbAvgChapter.getRefreshFile() == null || dbAvgChapter.getRefreshFile() == AvgChapterFileRefreshStatus.REFRESHED.getCode()) {
            return BizResult.success(domain + dbAvgChapter.getChapterFileKey());
        }
        String fileKey;
        try {
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            fileKey = AvgChapterUtils.generateChapterExcelStream(dbAvgChapter, arrayOutputStream);
            fileKey = qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, arrayOutputStream.toByteArray(), fileKey);
            if (StringUtils.isBlank(fileKey)) {
                return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "refreshChapterExcelFile 上传段落文本excel失败");
            }
            dbAvgChapter.setRefreshFile(AvgChapterFileRefreshStatus.REFRESHED.getCode());
            dbAvgChapter.setChapterFileKey(fileKey);
            avgRepository.updateAvgChapter(dbAvgChapter);
        } catch (Exception ex) {
            log.error("refreshChapterExcelFile error, chapterId={}", chapterId, ex);
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "refreshChapterExcelFile 生成段落文本excel失败");
        }
        return BizResult.success(domain + fileKey);
    }

    public BizResult<List<AvgZipUploadView>> avgZipFileUpload(MultipartFile file, int type, int dirId) {
        AvgFileType avgFileType = AvgFileType.getByCode(type);
        if (AvgFileType.UNKNOWN == avgFileType) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件类型不支持");
        }
        if (file.isEmpty()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件不能为空");
        }
        if (!Optional.ofNullable(file.getOriginalFilename()).orElse("").endsWith(".zip")) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件格式仅允许zip");
        }
        if (!ZIP_FILE_TYPES.contains(avgFileType)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件类型不支持");
        }
        AvgDir avgDir = avgRepository.queryAvgDirById(dirId);
        if (avgDir == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "dir目录没找到");
        }
        String basicPath = ZIP_FILE_PATH + System.currentTimeMillis() + SLASH;
        String fileOutPath = basicPath + file.getOriginalFilename();
        boolean isSuccess = FileUtils.saveMultipartFile(file, fileOutPath);
        if (!isSuccess) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件上传失败");
        }
        // 解压
        List<File> unzipFileList = FileUtils.unzip(fileOutPath);
        if (CollectionUtils.isEmpty(unzipFileList)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件解压失败");
        }
        // 删除无效文件
        unzipFileList = removeInvalidFiles(avgFileType, unzipFileList);
        if (CollectionUtils.isEmpty(unzipFileList)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "压缩包内无有效文件");
        }

        List<AvgZipUploadView> fileKeys = Lists.newArrayList();
        List<String> fileNames = unzipFileList.stream().map(File::getName).collect(Collectors.toList());
        BizResult<List<AvgZipUploadView>> checkResult = checkFileNames(avgFileType, fileNames);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        String dirFileKey = AVG_FILE_PATH + System.currentTimeMillis() + "/" + file.getOriginalFilename();
        qiniuComponent.uploadWithStream(PAY_BUCKET_NAME, fileOutPath, dirFileKey);
        avgDir.setKey(dirFileKey).setUpdatedAt(new Date());
        avgRepository.updateAvgDir(avgDir);
        for (File unzipFile : unzipFileList) {
            String fileKey = AVG_FILE_PATH + System.currentTimeMillis() + "/" + unzipFile.getName();
            qiniuComponent.uploadWithStream(PAY_BUCKET_NAME, unzipFile, fileKey);
            fileKeys.add(new AvgZipUploadView().setKey(fileKey).setName(unzipFile.getName()));
        }
        FileUtils.deleteDirectory(fileOutPath);
        return BizResult.success(fileKeys);
    }

    public BizResult<String> avgZipFileDownload(int id) {
        String domain = CdnUtil.getDefaultNewDomainWithBackSlash();
        AvgDir avgDir = avgRepository.queryAvgDirById(id);
        if (avgDir == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "dir没找到");
        }

        String key = avgDir.getKey();
        if (key != null) {
            return BizResult.success(CdnHandler.getEncryptionUrl(domain + key, CdnPayType.PAY.getCode()));
        }
        List<AvgOriginFile> avgOriginFiles = avgRepository.queryAvgFileByParentId(avgDir.getId());
        if (CollectionUtils.isEmpty(avgOriginFiles)) {
            return BizResult.success(null);
        }

        String basicPath = ZIP_FILE_PATH + System.currentTimeMillis() + SLASH;
        String fileOutPath = basicPath + avgDir.getName();
        try {
            ZipUtils.createZipPackage(fileOutPath);
            List<ZipUtils.UrlFileInfo> downLoadFiles = avgOriginFiles.stream().map(file -> {
                ZipUtils.UrlFileInfo urlFileInfo = new ZipUtils.UrlFileInfo();
                urlFileInfo.setPath(file.getName());
                urlFileInfo.setUrl(CdnHandler.getEncryptionUrl(domain + file.getKey(), CdnPayType.PAY.getCode()));
                return urlFileInfo;
            }).collect(Collectors.toList());
            boolean result = ZipUtils.zipFilePipe(fileOutPath, downLoadFiles);
            if (result) {
                log.info("文件压缩成功，开始上传对象存储，id={},fileName={}", id, avgDir.getName());
                String dirFileKey = AVG_FILE_PATH + System.currentTimeMillis() + "/" + avgDir.getName();
                qiniuComponent.uploadWithStream(PAY_BUCKET_NAME, fileOutPath, dirFileKey);
                avgDir.setKey(dirFileKey);
                avgRepository.updateAvgDir(avgDir);
                return BizResult.success(CdnHandler.getEncryptionUrl(domain + dirFileKey, CdnPayType.PAY.getCode()));
            }
            return null;
        } finally {
            File file = new File(fileOutPath);
            if (file.exists()) {
                if (file.delete()) {
                    log.info("文件{}删除成功！", fileOutPath);
                } else {
                    log.error("文件{}删除失败！", fileOutPath);
                }
            }
        }
    }

    private List<File> removeInvalidFiles(AvgFileType avgFileType, List<File> files) {
        if (avgFileType == AvgFileType.SPINE || avgFileType == AvgFileType.Q_SPINE) {
            files = files.stream().filter(file -> {
                String fileName = file.getName();
                return VALID_SPINE_FILENAME_SUFFIX_SET.stream().anyMatch(fileName::endsWith);
            }).collect(Collectors.toList());
        } else if (avgFileType == AvgFileType.GYROSCOPE) {
            files = files.stream().filter(file -> file.getName().endsWith("png")).collect(Collectors.toList());
        }
        return files;
    }

    private BizResult<List<AvgZipUploadView>> checkFileNames(AvgFileType avgFileType, List<String> fileNames) {
        // 内容校验 从apollo中获取校验规则
        if (avgFileType == AvgFileType.UI_STYLE) {
            List<String> uiStyleFileNames = apolloConfig.uiStyleFileNames();
            if (CollectionUtils.isNotEmpty(uiStyleFileNames)) {
                if (!CollectionUtils.isEqualCollection(fileNames, uiStyleFileNames)) {
                    return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件内容不符合规范");
                }
            }
        } else if (avgFileType == AvgFileType.SPINE || avgFileType == AvgFileType.Q_SPINE) {
            long count = fileNames.stream().filter(fileName -> fileName.endsWith(".json")).count();
            if (count > 1) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "json不唯一，请检查spine文件");
            }
            count = fileNames.stream().filter(fileName -> fileName.endsWith(".atlas")).count();
            if (count > 1) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "atlas不唯一，请检查spine文件");
            }
        }
        return BizResult.success();
    }

    public BizResult<AvgOriginFileView> checkExit(String fileName, Integer parentId) {
        AvgOriginFile avgOriginFile = avgRepository.queryAvgByFileNameAndParentId(fileName, parentId);
        if (avgOriginFile == null) {
            return BizResult.success();
        }
        String domain = CdnUtil.getDefaultNewDomainWithBackSlash();
        // 获取原名和别名列表
        List<AvgFileAliasRelation> fileAliasRelations = avgFileAliasRelationRepository.findByFileIdsAndType(
                Collections.singletonList(avgOriginFile.getId()), AvgResourceType.FILE.getCode());
        List<String> aliasList = fileAliasRelations.stream().map(AvgFileAliasRelation::getName).collect(Collectors.toList());
        return BizResult.success(AvgOriginFileView.valueOf(avgOriginFile, domain, aliasList));
    }

    public BizResult<AvgOriginFileInfoView> checkTopic(CheckTopicAvgParam checkTopicAvgParam) {
        int type = checkTopicAvgParam.getType();
        List<String> names = checkTopicAvgParam.getNames();
        log.info("query the topic in which the file is used, type:{}, names:{} ", type, names);
        AvgFileType fileType = AvgFileType.getByCode(type);
        if (AvgFileType.UNKNOWN.getCode() == fileType.getCode()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件类型不支持");
        }

        List<AvgFileChapterRelation> avgFileChapterRelations;
        if (SPINE_TYPE_SET.contains(type)) {
            List<AvgDir> avgDirs = avgRepository.queryAvgDirByNames(names)
                    .stream()
                    .filter(v -> v.getType() == type)
                    .collect(Collectors.toList());
            List<Integer> dirIds = avgDirs.stream().map(AvgDir::getId).collect(Collectors.toList());
            avgFileChapterRelations = avgFileChapterRelationRepository.queryByTypeAndFileIds(AvgResourceType.DIR.getCode(),
                    dirIds);
        } else if (type == AvgFileType.HOT_ZONE.getCode()) {
            List<AvgHotZone> avgHotZones = avgRepository.queryAvgHotZoneByNames(names);
            List<Integer> zoneIds = avgHotZones.stream().map(AvgHotZone::getId).collect(Collectors.toList());
            avgFileChapterRelations = avgFileChapterRelationRepository.queryByTypeAndFileIds(AvgResourceType.HOT_ZONE.getCode(),
                    zoneIds);
        } else if (type == AvgFileType.GYROSCOPE.getCode()) {
            List<AvgGyroscope> avgGyroscopes = avgRepository.queryGyroscopeByNames(names);
            List<Integer> zoneIds = avgGyroscopes.stream().map(AvgGyroscope::getId).collect(Collectors.toList());
            avgFileChapterRelations = avgFileChapterRelationRepository.queryByTypeAndFileIds(AvgResourceType.GYROSCOPE.getCode(),
                    zoneIds);
        } else {
            List<AvgOriginFile> avgOriginFiles = avgRepository.queryAvgFileByNames(names)
                    .stream()
                    .filter(v -> v.getType() == type)
                    .collect(Collectors.toList());
            List<Integer> fileIds = avgOriginFiles.stream().map(AvgOriginFile::getId).collect(Collectors.toList());
            avgFileChapterRelations = avgFileChapterRelationRepository.queryByTypeAndFileIds(AvgResourceType.FILE.getCode(),
                    fileIds);
        }

        // key = 文件id，value = 段落id列表
        Map<Integer, List<Integer>> fileChapterRelationMap = avgFileChapterRelations.stream()
                .collect(Collectors.groupingBy(AvgFileChapterRelation::getFileId,
                        Collectors.mapping(AvgFileChapterRelation::getChapterId, Collectors.toList())));
        List<Integer> chapterIds = avgFileChapterRelations.stream()
                .map(AvgFileChapterRelation::getChapterId)
                .distinct()
                .collect(Collectors.toList());

        // key = 段落id，value = 该段落所属的主题
        Map<Integer, AvgOriginFileInfoView.TopicView> chapterTopicMap = Maps.newHashMap();
        for (Integer chapterId : chapterIds) {
            RpcResult<AvgChapterDTO> rpcResult = dataFootStoneService.getAvgChapterBySegmentId(chapterId);
            if (!rpcResult.isSuccess()) {
                log.error("查询专题信息失败，type = {}, chapterId = {}, rpcResult = {}", type, chapterId, rpcResult);
                return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "查询专题信息失败");
            }
            AvgChapterDTO avgChapterDTO = rpcResult.getData();
            if (null == avgChapterDTO) {
                log.info("专题信息不存在，type = {}, chapterId = {}, rpcResult = {}", type, chapterId, rpcResult);
                continue;
            }
            TopicBasicDetailModel topicSyncInfo = dataFootStoneService.getTopicSyncInfo(avgChapterDTO.getTopicId());
            AvgOriginFileInfoView.TopicView topicView = new AvgOriginFileInfoView.TopicView();
            topicView.setTopicId(avgChapterDTO.getTopicId());
            topicView.setTopicName(topicSyncInfo.getTopic().getTitle());
            chapterTopicMap.put(chapterId, topicView);
        }

        // key = 文件id，value = 使用到的主题列表
        Map<Integer, List<AvgOriginFileInfoView.TopicView>> matchTopicMap = fileChapterRelationMap.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue()
                        .stream()
                        .map(chapterTopicMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())));

        return BizResult.success(AvgOriginFileInfoView.valueOf(matchTopicMap));
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> createFile(AvgOriginFileSaveOrUpdateParam param, boolean isRpcRequest) {
        String key = param.getKey();
        int type = param.getType();
        String fileName = param.getFileName();
        Integer dirId = param.getDirId();
        int resource = param.getResource();
        Integer topicId = param.getTopicId();
        List<String> tagNames = param.getTagNames();
        List<String> customTags = param.getCustomTags();
        Date now = new Date();

        AvgFileType avgFileType = AvgFileType.getByCode(type);
        if (AvgFileType.UNKNOWN == avgFileType) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件类型不支持");
        }
        if (resource == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && topicId == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "该资源未选择所属专题");
        }
        AvgOriginFile avgOriginFile = new AvgOriginFile().setKey(key)
                .setType(type)
                .setName(fileName)
                .setStatus(AvgFileStatusType.ONLINE.getCode())
                .setResource(resource);
        AvgOriginFile.Config config = topicId != null
                ? new AvgOriginFile.Config().setTopicId(topicId)
                : new AvgOriginFile.Config();
        config.setTagNames(tagNames).setCustomTags(customTags);
        if (VIDEO_TYPE_SET.contains(type)) {
            String videoId = String.valueOf(BizIdGenerator.getId());
            CommonVideo commonVideo = new CommonVideo().setVideoId(videoId)
                    .setStatus(CommonVideoStatus.WAITING.getCode())
                    .setOriginVideo(new CommonVideo.VideoInfo().setVideoKey(key))
                    .setCreatedAt(now)
                    .setUpdatedAt(now);
            avgRepository.insertCommonVideo(commonVideo);
            config.setVideoId(videoId);
        }
        if (AUDIO_TYPE_SET.contains(type)) {
            String audioId = String.valueOf(BizIdGenerator.getId());
            CommonAudio commonAudio = new CommonAudio().setAudioId(audioId)
                    .setBusType(AudioBusType.AVG_AUDIO.getCode())
                    .setStatus(CommonAudioStatus.WAITING.getCode())
                    .setOriginAudio(new CommonAudio.AudioInfo().setUrl(key))
                    .setLowBitAudio(new CommonAudio.AudioInfo())
                    .setCommitTaskTime(now)
                    .setCreatedAt(now)
                    .setUpdatedAt(now);
            avgRepository.insertCommonAudio(commonAudio);
            config.setAudioId(audioId);
        }
        if (avgFileType == AvgFileType.STUFF) {
            config.setAnchorInfo(new AvgOriginFile.AnchorInfo().setType(AnchorLocationType.CENTER.getCode()));
        }
        if (dirId != null) {
            avgOriginFile.setParentId(dirId);
        } else {
            avgOriginFile.setParentId(startUpComponent.getRootAvgDirId(avgFileType.getCode()));
        }
        avgOriginFile.setConfig(config);
        avgRepository.insertAvgOriginFile(avgOriginFile);
        if (AvgFileType.getAvgFileTypes().contains(type)) {
            avgFileAliasRelationRepository.insert(new AvgFileAliasRelation().setName(avgOriginFile.getName())
                    .setFileId(avgOriginFile.getId())
                    .setType(AvgResourceType.FILE.getCode()));
        }
        if (!isRpcRequest) {
            Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_ORIGIN_FILE_CREATE)
                    .add("key", key)
                    .add("typeName", avgFileType.getDesc());
            OperateLogUtils.asyncRecord(operation);
        }
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> updateFile(AvgOriginFileSaveOrUpdateParam param) {
        int id = param.getId();
        String fileKey = param.getKey();
        String name = param.getFileName();
        int resource = param.getResource();
        Integer topicId = param.getTopicId();
        List<String> tagNames = param.getTagNames();
        List<String> customTags = param.getCustomTags();
        List<String> nickNames = param.getNickNames();

        AvgOriginFile avgOriginFile = avgRepository.queryAvgFileById(id);
        if (avgOriginFile == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件不存在");
        }
        if (resource == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && topicId == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "该资源未选择所属专题");
        }
        // alias
        List<String> createFileAlias = Lists.newArrayList();
        Optional.ofNullable(name).ifPresent(createFileAlias::add);
        Optional.ofNullable(nickNames).ifPresent(createFileAlias::addAll);
        if (createFileAlias.contains(avgOriginFile.getName())) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件名重复");
        }
        createFileAlias.add(avgOriginFile.getName());

        boolean hasDuplicatesName = createFileAlias.size() != Sets.newHashSet(createFileAlias).size();
        if (hasDuplicatesName) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "别名重复");
        }

        // 每次更新,删除文件名和别名,重建关系
        List<AvgFileAliasRelation> currentFileAliases = avgFileAliasRelationRepository.findByFileIdsAndType(
                Collections.singletonList(id), AvgResourceType.FILE.getCode());
        List<Integer> deleteIds = currentFileAliases.stream().map(AvgFileAliasRelation::getId).collect(Collectors.toList());
        List<AvgFileAliasRelation> existFileAlias = avgFileAliasRelationRepository.findByNames(createFileAlias)
                .stream()
                .filter(aliasRelation -> !deleteIds.contains(aliasRelation.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existFileAlias)) {
            log.error("update avg file alias exists:{}", createFileAlias);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "别名已存在");
        }

        List<String> aliasList = currentFileAliases.stream().map(AvgFileAliasRelation::getName).collect(Collectors.toList());
        avgFileAliasRelationRepository.deleteAliasRelationByIds(deleteIds, aliasList);

        List<AvgFileAliasRelation> fileAliasRelations = buildAvgAliasByFileInfo(id, AvgResourceType.FILE.getCode(),
                createFileAlias);
        avgFileAliasRelationRepository.batchInsert(fileAliasRelations);

        avgOriginFile.setUpdatedAt(new Date());
        avgOriginFile.setName(name);
        avgOriginFile.setKey(fileKey);
        avgOriginFile.setResource(resource);
        AvgOriginFile.Config config = Optional.ofNullable(avgOriginFile.getConfig()).orElse(new AvgOriginFile.Config());
        config.setTopicId(topicId);
        config.setTagNames(tagNames);
        config.setCustomTags(customTags);
        avgOriginFile.setConfig(config);
        avgRepository.updateAvgOriginFile(avgOriginFile);

        if (VIDEO_TYPE_SET.contains(avgOriginFile.getType())) {
            // 更新mongo
            if (avgOriginFile.getConfig() != null) {
                CommonVideo commonVideo = avgRepository.queryCommonVideoByVideoId(avgOriginFile.getConfig().getVideoId());
                if (Objects.nonNull(commonVideo)) {
                    CommonVideo.VideoInfo originVideo = commonVideo.getOriginVideo();
                    originVideo.setVideoKey(fileKey);
                    commonVideo.setOriginVideo(originVideo);
                    commonVideo.setStatus(CommonVideoStatus.WAITING.getCode());
                    avgRepository.updateCommonVideo(commonVideo);
                }
            }
        }

        if (AUDIO_TYPE_SET.contains(avgOriginFile.getType())) {
            if (avgOriginFile.getConfig() != null) {
                CommonAudio commonAudio = avgRepository.queryCommonAudioByAudioId(avgOriginFile.getConfig().getAudioId());
                if (Objects.nonNull(commonAudio)) {
                    CommonAudio.AudioInfo originAudio = commonAudio.getOriginAudio();
                    originAudio.setUrl(fileKey);
                    commonAudio.setOriginAudio(originAudio);
                    commonAudio.setStatus(CommonAudioStatus.WAITING.getCode());
                    avgRepository.updateCommonAudio(commonAudio);
                }
            }
        }
        return BizResult.success();
    }

    public BizResult<PageView<AvgOriginFileView>> fileListByType(int type, int page, int pageSize, String orderByAndSort) {
        int total = avgRepository.countByType(type);
        int offset = (page - 1) * pageSize;
        List<AvgOriginFile> avgOriginFiles = avgRepository.selectByTypeAndPage(type, offset, pageSize, orderByAndSort);
        String domain = CdnUtil.getDefaultNewDomainWithBackSlash();
        List<Integer> fileIds = avgOriginFiles.stream().map(AvgOriginFile::getId).collect(Collectors.toList());
        Map<Integer, List<String>> fileIdAliasMap = avgFileAliasRelationRepository.findAliasGroupByIds(fileIds,
                AvgResourceType.FILE.getCode());
        List<AvgOriginFileView> avgOriginFileViews = avgOriginFiles.stream()
                .map((avgOriginFile) -> AvgOriginFileView.valueOf(avgOriginFile, domain,
                        fileIdAliasMap.get(avgOriginFile.getId())))
                .collect(Collectors.toList());
        return BizResult.success(PageView.form(total, avgOriginFileViews));
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> deleteFile(int id, boolean isRpcRequest) {
        AvgOriginFile avgOriginFile = avgRepository.queryAvgFileById(id);
        if (avgOriginFile == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件不存在");
        }
        avgRepository.deleteFile(id);
        deleteFileAliasRelations(id, AvgResourceType.FILE.getCode());
        if (!isRpcRequest) {
            Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_ORIGIN_FILE_DELETE)
                    .add("id", id)
                    .add("fileInfo", JsonUtils.toJson(avgOriginFile));
            OperateLogUtils.asyncRecord(operation);
        }
        return BizResult.success();
    }

    // fix data
    @Transactional(rollbackFor = Exception.class)
    public void deleteAlias(int fileId, int type) {
        deleteFileAliasRelations(fileId, type);
    }

    private String fileValidate(MultipartFile file, Integer type, Integer dirId) {
        switch (AvgFileType.getByCode(type)) {
            case DYNAMIC_BACKGROUND:
                if (StringUtils.isEmpty(file.getOriginalFilename()) || !file.getOriginalFilename().endsWith(".mp4")) {
                    return "文件格式仅允许mp4";
                }
                try {
                    if (file.getBytes().length > MaterialConstants.DYNAMIC_BACKGROUND_SIZE_LIMIT) {
                        return "文件大小不能超过" + MaterialConstants.DYNAMIC_BACKGROUND_SIZE_LIMIT / 1024 / 1024 + "M";
                    }
                } catch (IOException e) {
                    return "文件读取失败";
                }
                break;
            case DYNAMIC_ROLE:
                if (StringUtils.isEmpty(file.getOriginalFilename()) || !file.getOriginalFilename().endsWith(".png")) {
                    return "文件格式仅允许png";
                }

                try {
                    BufferedImage img = ImageIO.read(file.getInputStream());
                    if (img.getWidth() != MaterialConstants.DEFAULT_WIGHT_LIMIT
                            || img.getHeight() != MaterialConstants.DEFAULT_HIGH_LIMIT) {
                        return "图片分辨率必须为"
                                + MaterialConstants.DEFAULT_WIGHT_LIMIT
                                + "px"
                                + "*"
                                + MaterialConstants.DEFAULT_HIGH_LIMIT
                                + "px";
                    }
                } catch (IOException e) {
                    return "文件读取失败";
                }
                break;
            case HIGHLIGHT_VIDEO:
                if (StringUtils.isEmpty(file.getOriginalFilename()) || !file.getOriginalFilename().endsWith(".mp4")) {
                    return "文件格式仅允许mp4";
                }
                try {
                    if (file.getBytes().length > MaterialConstants.HIGHLIGHT_VIDEO_SIZE_LIMIT) {
                        return "文件大小不能超过" + MaterialConstants.HIGHLIGHT_VIDEO_SIZE_LIMIT / 1024 / 1024 + "M";
                    }
                } catch (IOException e) {
                    return "文件读取失败";
                }
                break;
            case HOT_ZONE:
            case GYROSCOPE:
                try {
                    BufferedImage img = ImageIO.read(file.getInputStream());
                    if (img.getWidth() != MaterialConstants.DEFAULT_WIGHT_LIMIT
                            || img.getHeight() != MaterialConstants.DEFAULT_HIGH_LIMIT) {
                        return "图片分辨率必须为"
                                + MaterialConstants.DEFAULT_WIGHT_LIMIT
                                + "px"
                                + "*"
                                + MaterialConstants.DEFAULT_HIGH_LIMIT
                                + "px";
                    }
                } catch (IOException e) {
                    return "文件读取失败";
                }
                break;
            case HIGHLIGHT_VIDEO_VOICE:
                if (StringUtils.isEmpty(file.getOriginalFilename()) || !file.getOriginalFilename().endsWith(".mp3")) {
                    return "文件格式仅允许mp3";
                }
            default:
                return "";
        }
        return "";
    }

    public BizResult<Integer> createDir(AvgDirSaveOrUpdateParam param, boolean isRpcRequest) {
        int type = param.getType();
        String name = param.getName();
        int resource = param.getResource();
        Integer topicId = param.getTopicId();
        List<String> tagNames = param.getTagNames();
        List<String> customTags = param.getCustomTags();

        if (StringUtils.isBlank(name)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件夹名称不能为空");
        }
        if (StringUtils.equals(name, "root")) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件夹名称不能为root");
        }
        if (resource == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && topicId == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "该资源未选择所属专题");
        }
        List<AvgFileAliasRelation> existAliasRelations = avgFileAliasRelationRepository.findByNames(
                Collections.singletonList(name));
        if (CollectionUtils.isNotEmpty(existAliasRelations)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件夹已存在");
        }
        AvgFileType avgFileType = AvgFileType.getByCode(type);
        Integer rootAvgDirId = startUpComponent.getRootAvgDirId(avgFileType.getCode());
        AvgDir.Config config = new AvgDir.Config();
        if (topicId != null) {
            config.setTopicId(topicId);
        }
        config.setTagNames(tagNames).setCustomTags(customTags);
        AvgDir avgDir = new AvgDir().setName(name)
                .setType(type)
                .setParentId(rootAvgDirId)
                .setConfig(config)
                .setStatus(AvgFileStatusType.ONLINE.getCode())
                .setResource(resource);
        avgRepository.insertAvgDir(avgDir);
        if (AvgFileType.getAvgDirTypes().contains(type)) {
            avgFileAliasRelationRepository.insert(new AvgFileAliasRelation().setName(avgDir.getName())
                    .setFileId(avgDir.getId())
                    .setType(AvgResourceType.DIR.getCode()));
        }
        if (!isRpcRequest) {
            Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_DIR_CREATE)
                    .add("name", name)
                    .add("typeName", avgFileType.getDesc());
            OperateLogUtils.asyncRecord(operation);
        }
        return BizResult.success(avgDir.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> updateDir(AvgDirSaveOrUpdateParam param, boolean isRpcRequest) {
        int id = param.getId();
        int type = param.getType();
        String name = param.getName();
        int resource = param.getResource();
        Integer topicId = param.getTopicId();
        List<String> nickNames = param.getNickNames();
        List<String> tagNames = param.getTagNames();
        List<String> customTags = param.getCustomTags();

        if (StringUtils.isBlank(name)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件夹名称不能为空");
        }
        if (StringUtils.equals(name, "root")) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件夹名称不能为root");
        }
        if (resource == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && topicId == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "该资源未选择所属专题");
        }
        // alias
        List<String> createDirAlias = Lists.newArrayList(name);
        Optional.ofNullable(nickNames).ifPresent(createDirAlias::addAll);

        boolean hasDuplicatesName = createDirAlias.size() != Sets.newHashSet(createDirAlias).size();
        if (hasDuplicatesName) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "别名重复");
        }
        AvgDir avgDir = avgRepository.queryAvgDirById(id);
        if (avgDir == null) {
            log.info("this dir file is not exists ,id = {}", id);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "该dir文件夹不存在，id = " + id);
        }
        List<AvgDir> avgDirs = avgRepository.queryAvgDirByNameAndType(name, type)
                .stream()
                .filter(v -> v.getId() != id)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(avgDirs)) {
            log.info("The folder you want to replace already exists , name = {} , type = {}", name, type);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "要更换的文件夹已存在，name = " + name);
        }

        // 文件夹类型,校验别名是否重复
        List<AvgFileAliasRelation> currentFileAliases = avgFileAliasRelationRepository.findByFileIdsAndType(
                Collections.singletonList(id), AvgResourceType.DIR.getCode());
        List<Integer> deleteIds = currentFileAliases.stream().map(AvgFileAliasRelation::getId).collect(Collectors.toList());
        List<AvgFileAliasRelation> existFileAlias = avgFileAliasRelationRepository.findByNames(createDirAlias)
                .stream()
                .filter(aliasRelation -> !deleteIds.contains(aliasRelation.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existFileAlias)) {
            log.error("update avg dir alias exists:{}", createDirAlias);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "别名已存在");
        }

        List<String> aliasList = currentFileAliases.stream().map(AvgFileAliasRelation::getName).collect(Collectors.toList());
        avgFileAliasRelationRepository.deleteAliasRelationByIds(deleteIds, aliasList);

        List<AvgFileAliasRelation> fileAliasRelations = buildAvgAliasByFileInfo(id, AvgResourceType.DIR.getCode(),
                createDirAlias);
        avgFileAliasRelationRepository.batchInsert(fileAliasRelations);

        AvgFileType avgFileType = AvgFileType.getByCode(type);
        avgDir.setName(name);
        avgDir.setResource(resource);
        AvgDir.Config config = avgDir.getConfig();
        if (config == null) {
            config = new AvgDir.Config();
        }
        config.setTopicId(topicId);
        config.setTagNames(tagNames);
        config.setCustomTags(customTags);
        avgDir.setConfig(config);
        avgRepository.updateAvgDir(avgDir);
        if (!isRpcRequest) {
            Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_DIR_UPDATE)
                    .add("name", name)
                    .add("typeName", avgFileType.getDesc());
            OperateLogUtils.asyncRecord(operation);
        }
        return BizResult.success();
    }

    public BizResult<PageResult<AvgDirView>> dirListByType(int type, int pageNum, int pageSize, String orderByAndSort) {
        Integer rootAvgDirId = startUpComponent.getRootAvgDirId(type);
        PageInfo<AvgDir> avgDirs = avgRepository.queryAvgDirByTypeAndParentId(type, rootAvgDirId, pageNum, pageSize,
                orderByAndSort);
        List<Integer> dirIds = avgDirs.getList().stream().map(AvgDir::getId).collect(Collectors.toList());
        Map<Integer, List<String>> dirAliasMap = avgFileAliasRelationRepository.findAliasGroupByIds(dirIds,
                AvgResourceType.DIR.getCode());
        return BizResult.success(PageResult.from(avgDirs, avgDir -> AvgDirView.valueOf(avgDir, dirAliasMap.get(avgDir.getId()))));
    }

    public BizResult<List<AvgDirView>> dirListAllByType(int type, String orderByAndSort) {
        Integer rootAvgDirId = startUpComponent.getRootAvgDirId(type);
        List<AvgDir> avgDirs = avgRepository.queryAllAvgDirByTypeAndParentId(type, rootAvgDirId, orderByAndSort);
        List<Integer> dirIds = avgDirs.stream().map(AvgDir::getId).collect(Collectors.toList());
        Map<Integer, List<String>> dirAliasMap = avgFileAliasRelationRepository.findAliasGroupByIds(dirIds,
                AvgResourceType.DIR.getCode());
        List<AvgDirView> collect = avgDirs.stream()
                .map(avgDir -> AvgDirView.valueOf(avgDir, dirAliasMap.get(avgDir.getId())))
                .collect(Collectors.toList());
        return BizResult.success(collect);
    }

    public BizResult<PageResult<AvgDirView>> dirListWithFilesByType(AvgFileQueryParam param) {
        int type = param.getTypes().get(0);
        int pageNum = param.getPage();
        int pageSize = param.getPageSize();
        String orderByAndSort = param.getOrderByAndSort();
        String innerOrderByAndSort = param.getInnerOrderByAndSort();
        String domain = CdnUtil.getDefaultNewDomainWithBackSlash();

        if (type != AvgFileType.Q_SPINE.getCode()) {
            Integer rootAvgDirId = startUpComponent.getRootAvgDirId(type);
            PageInfo<AvgDir> avgDirs = avgRepository.queryAvgDirPage(type, rootAvgDirId, pageNum, pageSize, orderByAndSort,
                    param.getName(), param.getTopicId(), param.getResource(), param.getStatus(), param.getTagNames());
            List<Integer> dirIds = avgDirs.getList().stream().map(AvgDir::getId).collect(Collectors.toList());
            List<AvgOriginFile> avgOriginFiles = avgRepository.queryAvgFileByParentIds(dirIds, innerOrderByAndSort);
            // file alias map
            List<Integer> fileIds = avgOriginFiles.stream().map(AvgOriginFile::getId).collect(Collectors.toList());
            Map<Integer, List<String>> fileIdAliasMap = avgFileAliasRelationRepository.findAliasGroupByIds(fileIds, AvgResourceType.FILE.getCode());
            // 对分组后的每个列表按name进行排序
            Map<Integer, List<AvgOriginFileView>> dirId2Files = avgOriginFiles.stream()
                    .map((avgOriginFile) -> AvgOriginFileView.valueOf(avgOriginFile, domain, fileIdAliasMap.get(avgOriginFile.getId())))
                    .collect(Collectors.groupingBy(AvgOriginFileView::getParentId, Collectors.collectingAndThen(Collectors.toList(),
                            list -> list.stream().sorted(Comparator.comparing(AvgOriginFileView::getName)).collect(Collectors.toList()))));
            // dir alias map
            Map<Integer, List<String>> dirAliasMap = avgFileAliasRelationRepository.findAliasGroupByIds(dirIds,
                    AvgResourceType.DIR.getCode());
            return BizResult.success(PageResult.from(avgDirs,
                    avgDir -> AvgDirView.valueOf(avgDir, dirId2Files.get(avgDir.getId()), dirAliasMap.get(avgDir.getId()))));
        } else {
            // Q-spine,目录和文件
            List<AvgDir> avgDirs = avgRepository.queryDirList(type, param.getName(), param.getTopicId(), param.getResource(),
                    param.getStatus());
            List<AvgOriginFile> avgOriginFiles = avgRepository.queryOriginFileList(type, param.getName(), param.getTopicId(),
                    param.getResource(), param.getStatus(), 0);

            // 在最后处理资源时下发别名
            List<AvgDirView.AvgDirOrFileView> dirFiles = avgDirs.stream()
                    .map(AvgDirView.AvgDirOrFileView::valueOf)
                    .collect(Collectors.toList());
            List<AvgDirView.AvgDirOrFileView> fileFiles = avgOriginFiles.stream()
                    .map(AvgDirView.AvgDirOrFileView::valueOf)
                    .collect(Collectors.toList());

            // alias map
            List<Integer> dirIds = avgDirs.stream().map(AvgDir::getId).collect(Collectors.toList());
            List<Integer> fileIds = avgOriginFiles.stream().map(AvgOriginFile::getId).collect(Collectors.toList());
            Map<Integer, List<String>> dirAliasMap = avgFileAliasRelationRepository.findAliasGroupByIds(dirIds,
                    AvgResourceType.DIR.getCode());
            Map<Integer, List<String>> fileAliasMap = avgFileAliasRelationRepository.findAliasGroupByIds(fileIds,
                    AvgResourceType.FILE.getCode());

            dirFiles.addAll(fileFiles);
            // 按照排序字段排序
            String[] split = orderByAndSort.split(" ");
            // orderby 存在name或者updatedAt
            String orderBy = split[0];
            //sort 存在升序或者降序
            String sort = split[1];
            //对dirFiles进行排序并且分页
            List<AvgDirView.AvgDirOrFileView> pageList = dirFiles.stream().sorted((o1, o2) -> {
                if ("name".equals(orderBy)) {
                    return "asc".equals(sort) ? o1.getName().compareTo(o2.getName()) : o2.getName().compareTo(o1.getName());
                } else {
                    return "asc".equals(sort)
                            ? o1.getUpdatedAt().compareTo(o2.getUpdatedAt())
                            : o2.getUpdatedAt().compareTo(o1.getUpdatedAt());
                }
            }).skip((long) (pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
            List<AvgOriginFile> originFiles = avgRepository.queryAvgFileByParentIds(pageList.stream()
                    .filter(AvgDirView.AvgDirOrFileView::isDir)
                    .map(AvgDirView.AvgDirOrFileView::getId)
                    .collect(Collectors.toList()), innerOrderByAndSort);
            Map<Integer, List<AvgOriginFileView>> dirId2Files = originFiles.stream()
                    .map((avgOriginFile) -> AvgOriginFileView.valueOf(avgOriginFile, domain,
                            fileAliasMap.get(avgOriginFile.getId())))
                    .collect(Collectors.groupingBy(AvgOriginFileView::getParentId));

            PageResult<AvgDirView> pageResult = new PageResult<>();
            pageResult.setTotalCount(CollectionUtils.size(avgDirs) + CollectionUtils.size(avgOriginFiles));
            // 下发dir和关联file别名
            List<AvgDirView> pageView = pageList.stream().map(item -> {
                if (item.isDir()) {
                    return AvgDirView.valueOf(AvgFileType.Q_SPINE.getCode(), item,
                            dirId2Files.getOrDefault(item.getId(), new ArrayList<>()), dirAliasMap.get(item.getId()));
                } else {
                    AvgOriginFile avgOriginFile = new AvgOriginFile().setId(item.getId())
                            .setKey(item.getKey())
                            .setName(item.getName())
                            .setConfig(new AvgOriginFile.Config().setTopicId(item.getTopicId()))
                            .setStatus(item.getStatus())
                            .setResource(item.getResource());
                    return AvgDirView.valueOf(AvgFileType.Q_SPINE.getCode(), item,
                            Lists.newArrayList(AvgOriginFileView.valueOf(avgOriginFile, domain, fileAliasMap.get(item.getId()))),
                            dirAliasMap.get(item.getId())).setDir(false);
                }
            }).collect(Collectors.toList());
            pageResult.setList(pageView);
            pageResult.setPageNum(pageNum);
            pageResult.setPageSize(pageSize);
            return BizResult.success(pageResult);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteDir(int id) {
        avgRepository.deleteFileByParentId(id);
        avgRepository.deleteDir(id);
        deleteFileAliasRelations(id, AvgResourceType.DIR.getCode());
    }

    public BizResult<String> avgHotZoneUpload(MultipartFile file, String key, int id) {
        AvgHotZone avgHotZone = avgRepository.queryAvgHotZoneById(id);
        if (avgHotZone == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "点触热区不存在");
        }
        String msg = fileValidate(file, AvgFileType.HOT_ZONE.getCode(), null);
        if (Strings.isNotBlank(msg)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), msg);
        }
        String fileKey = AVG_FILE_PATH + System.currentTimeMillis() + "/" + key;
        try {
            qiniuComponent.uploadWithStream(PAY_BUCKET_NAME, file.getBytes(), fileKey);
        } catch (IOException e) {
            log.error("avgFileUpload error", e);
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR);
        }
        avgHotZone.setImageKey(fileKey);
        avgHotZone.setUpdatedAt(new Date());
        avgRepository.updateAvgHotZone(avgHotZone);
        return BizResult.success(fileKey);
    }

    public BizResult<PageResult<AvgHotZoneView>> avgHotZoneList(AvgFileQueryParam param) {
        int page = param.getPage();
        int pageSize = param.getPageSize();
        String orderByAndSort = param.getOrderByAndSort();
        String domain = CdnUtil.getDefaultNewDomainWithBackSlash();

        PageInfo<AvgHotZone> avgHotZones = avgRepository.queryAvgHotZone(page, pageSize, orderByAndSort, param.getName(),
                param.getTopicId(), param.getResource(), param.getStatus(), param.getTagNames());

        // hot zone alias
        List<Integer> fileIds = avgHotZones.getList().stream().map(AvgHotZone::getId).collect(Collectors.toList());
        Map<Integer, List<String>> avgHotZoneAliasMap = avgFileAliasRelationRepository.findAliasGroupByIds(fileIds,
                AvgResourceType.HOT_ZONE.getCode());

        return BizResult.success(PageResult.from(avgHotZones,
                (hotZone) -> AvgHotZoneView.valueOf(hotZone, domain, avgHotZoneAliasMap.get(hotZone.getId()))));
    }

    public BizResult<Integer> avgHotZoneCreate(AvgHotZoneAddOrUpdateParam param) {
        if (validateChineseName(param.getName())) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "热区名字不能是中文");
        }
        List<AvgFileAliasRelation> existAlias = avgFileAliasRelationRepository.findByNames(
                Collections.singletonList(param.getName()));
        if (CollectionUtils.isNotEmpty(existAlias)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "点触热区已存在");
        }
        AvgHotZone avgHotZone = new AvgHotZone().setName(param.getName()).setImageKey(param.getImageKey());
        AvgHotZoneAddOrUpdateParam.HotZoneConfigParam hotZoneConfigParam = param.getConfig();
        if (Objects.nonNull(hotZoneConfigParam)) {
            BizResult<Integer> checkResult = checkTimeTouchParam(hotZoneConfigParam);
            if (checkResult != null) {
                return checkResult;
            }
            avgHotZone.setConfig(AvgHotZoneAddOrUpdateParam.toConfig(hotZoneConfigParam));
            String errorMsg = avgHotZone.rectangleCheck();
            if (StringUtils.isNotBlank(errorMsg)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), errorMsg);
            }
        }
        avgRepository.insertAvgHotZone(avgHotZone);
        avgFileAliasRelationRepository.insert(new AvgFileAliasRelation().setName(avgHotZone.getName())
                .setFileId(avgHotZone.getId())
                .setType(AvgResourceType.HOT_ZONE.getCode()));
        return BizResult.success(avgHotZone.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Integer> avgHotZoneUpdate(AvgHotZoneAddOrUpdateParam param) {
        int id = param.getId();
        int resource = param.getResource();
        Integer topicId = param.getTopicId();
        List<String> nickNames = param.getNickNames();
        List<String> tagNames = param.getTagNames();
        List<String> customTags = param.getCustomTags();

        if (resource == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && topicId == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "该资源未选择所属专题");
        }
        if (validateChineseName(param.getName())) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "热区名字不能是中文");
        }
        AvgHotZone hotZone = avgRepository.queryAvgHotZoneById(param.getId());
        if (hotZone == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "点触热区不存在");
        }
        // alias
        List<String> createHotZoneAlias = Lists.newArrayList(param.getName());
        Optional.ofNullable(nickNames).ifPresent(createHotZoneAlias::addAll);

        boolean hasDuplicatesName = createHotZoneAlias.size() != Sets.newHashSet(createHotZoneAlias).size();
        if (hasDuplicatesName) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "别名重复");
        }
        hotZone.setName(param.getName()).setImageKey(param.getImageKey());
        AvgHotZoneAddOrUpdateParam.HotZoneConfigParam hotZoneConfigParam = param.getConfig();
        if (Objects.nonNull(hotZoneConfigParam)) {
            BizResult<Integer> checkResult = checkTimeTouchParam(hotZoneConfigParam);
            if (checkResult != null) {
                return checkResult;
            }
            hotZone.setConfig(AvgHotZoneAddOrUpdateParam.toConfig(hotZoneConfigParam));
            String errorMsg = hotZone.rectangleCheck();
            if (StringUtils.isNotBlank(errorMsg)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), errorMsg);
            }
        }

        // 校验别名是否和别名以及热区的资源名重复
        List<AvgFileAliasRelation> currentFileAliases = avgFileAliasRelationRepository.findByFileIdsAndType(
                Collections.singletonList(id), AvgResourceType.HOT_ZONE.getCode());
        List<Integer> deleteIds = currentFileAliases.stream().map(AvgFileAliasRelation::getId).collect(Collectors.toList());
        List<AvgFileAliasRelation> existFileAlias = avgFileAliasRelationRepository.findByNames(createHotZoneAlias)
                .stream()
                .filter(aliasRelation -> !deleteIds.contains(aliasRelation.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existFileAlias)) {
            log.error("update avg hot zone alias exists:{}", createHotZoneAlias);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "别名已存在");
        }

        List<String> aliasList = currentFileAliases.stream().map(AvgFileAliasRelation::getName).collect(Collectors.toList());
        avgFileAliasRelationRepository.deleteAliasRelationByIds(deleteIds, aliasList);

        List<AvgFileAliasRelation> fileAliasRelations = buildAvgAliasByFileInfo(id, AvgResourceType.HOT_ZONE.getCode(),
                createHotZoneAlias);
        avgFileAliasRelationRepository.batchInsert(fileAliasRelations);

        hotZone.setResource(resource);
        AvgHotZone.Config config = hotZone.getConfig() != null ? hotZone.getConfig() : new AvgHotZone.Config();
        config.setTopicId(topicId);
        config.setTagNames(tagNames);
        config.setCustomTags(customTags);
        hotZone.setConfig(config);
        avgRepository.updateAvgHotZone(hotZone);
        return BizResult.success(hotZone.getId());
    }

    public BizResult queryAvgChapterList(AvgChapterOrderTypeEnum orderType, String projectId, int type, int page, int pageSize) {
        int total = avgRepository.queryProjectChapterCount(projectId);
        List<AvgChapter> avgChapters = avgRepository.queryAvgChapterList(orderType, projectId, type, page, pageSize);
        Set<Integer> styleIds = avgChapters.stream().map(AvgChapter::getStyleId).collect(Collectors.toSet());
        Set<Integer> exitStyleIds = avgRepository.queryAvgDirsByIds(styleIds)
                .stream()
                .map(AvgDir::getId)
                .collect(Collectors.toSet());
        List<AvgChapterVO> avgChapterVOList = avgChapterConverter.toAvgChapterVOList(avgChapters, exitStyleIds);
        PageView<AvgChapterVO> pageView = PageView.form(total, avgChapterVOList);
        return BizResult.success(pageView);
    }

    public BizResult queryAvgChapterList(String name, Integer id) {
        List<AvgChapter> avgChapters = avgRepository.queryAvgChapterList(name, id, AvgChapterType.TEXT.getCode());
        Set<Integer> styleIds = avgChapters.stream().map(AvgChapter::getStyleId).collect(Collectors.toSet());
        Set<Integer> exitStyleIds = avgRepository.queryAvgDirsByIds(styleIds)
                .stream()
                .map(AvgDir::getId)
                .collect(Collectors.toSet());
        List<AvgChapterVO> avgChapterVOList = avgChapterConverter.toAvgChapterVOList(avgChapters, exitStyleIds);
        PageView<AvgChapterVO> pageView = PageView.form(0, avgChapterVOList);
        return BizResult.success(pageView);
    }

    public BizResult<PageView<AvgChapterSimpleModel>> queryAvgChapterList(AvgChapterOrderTypeEnum orderType, String segmentName,
            Integer segmentId, Integer topicId, Integer type, int page, int pageSize) {
        AvgProject avgProject = avgRepository.getProjectByTopicId(topicId);
        if (avgProject == null) {
            log.info("queryTopicRelatedChapter, no topicId related avgProject, topicId={}", topicId);
            return BizResult.success(PageView.empty());
        }
        String projectId = avgProject.getObjectId();
        int total = avgRepository.queryProjectChapterCount(projectId, segmentName, segmentId, type);
        List<AvgChapter> avgChapters = avgRepository.queryAvgChapterList(orderType, projectId, segmentName, segmentId, type, page,
                pageSize);
        List<AvgChapterSimpleModel> avgChapterSimpleModels = avgChapters.stream()
                .map(AvgChapterSimpleModel::valueOf)
                .collect(Collectors.toList());
        return BizResult.success(PageView.form(total, avgChapterSimpleModels));
    }

    public String saveFile(MultipartFile excelFile) throws IOException {
        String fileKey = String.format(EXCEL_KEY, BufferedIdGenerator.getId(), excelFile.getOriginalFilename());
        qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, excelFile.getBytes(), fileKey);
        return fileKey;
    }

    public BizResult<Void> createZipFile(AvgUIStyleZipFileCreateParam param, boolean isRpcRequest) {
        AvgFileType avgFileType = AvgFileType.getByCode(param.getType());
        if (AvgFileType.UNKNOWN == avgFileType) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件类型不支持");
        }
        Date now = new Date();
        List<AvgOriginFile> avgOriginFiles = Lists.newArrayList();
        param.getFiles().forEach(file -> {
            AvgOriginFile avgOriginFile = new AvgOriginFile().setKey(file.getKey())
                    .setType(param.getType())
                    .setName(file.getName())
                    .setStatus(AvgFileStatusType.ONLINE.getCode())
                    .setResource(AvgFileResourceType.UNKNOW.getCode())
                    .setCreatedAt(now)
                    .setUpdatedAt(now);
            if (param.getDirId() != null) {
                avgOriginFile.setParentId(param.getDirId());
            } else {
                avgOriginFile.setParentId(startUpComponent.getRootAvgDirId(avgFileType.getCode()));
            }
            avgOriginFiles.add(avgOriginFile);
        });
        avgRepository.batchInsertAvgOriginFile(avgOriginFiles);
        for (AvgOriginFile avgOriginFile : avgOriginFiles) {
            if (!isRpcRequest) {
                Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_ORIGIN_FILE_CREATE)
                        .add("key", avgOriginFile.getKey())
                        .add("typeName", avgFileType.getDesc());
                OperateLogUtils.asyncRecord(operation);
            }
        }
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> updateZipFile(AvgUIStyleZipFileCreateParam param) {
        AvgFileType avgFileType = AvgFileType.getByCode(param.getType());
        if (!ZIP_FILE_TYPES.contains(avgFileType)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件类型不支持");
        }
        if (null == param.getDirId() || param.getDirId() == 0) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件夹id不正确");
        }
        List<AvgOriginFile> oldAvgOriginFiles = avgRepository.queryAvgFileByParentId(param.getDirId());
        if (CollectionUtils.isEmpty(oldAvgOriginFiles)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "avg文件不存在");
        }
        Date now = new Date();
        avgRepository.deleteFileByParentId(param.getDirId());
        List<AvgOriginFile> newAvgOriginFiles = Lists.newArrayList();
        param.getFiles().forEach(file -> {
            AvgOriginFile avgOriginFile = new AvgOriginFile().setKey(file.getKey())
                    .setType(param.getType())
                    .setName(file.getName())
                    .setParentId(param.getDirId())
                    .setStatus(AvgFileStatusType.ONLINE.getCode())
                    .setResource(AvgFileResourceType.UNKNOW.getCode())
                    .setCreatedAt(now)
                    .setUpdatedAt(now);
            newAvgOriginFiles.add(avgOriginFile);
        });
        avgRepository.batchInsertAvgOriginFile(newAvgOriginFiles);
        newAvgOriginFiles.forEach(avgOriginFile -> {
            Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_ORIGIN_FILE_UPDATE)
                    .add("key", avgOriginFile.getKey())
                    .add("typeName", avgFileType.getDesc());
            OperateLogUtils.asyncRecord(operation);
        });
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> avgHotZoneDelete(Integer id) {
        avgRepository.deleteAvgHotZone(id);
        deleteFileAliasRelations(id, AvgResourceType.HOT_ZONE.getCode());
        return BizResult.success();
    }

    public BizResult<PageView<AvgOriginFileView>> fileListByTypes(AvgFileQueryParam param) {
        List<Integer> types = param.getTypes();
        int page = param.getPage();
        int pageSize = param.getPageSize();
        String orderByAndSort = param.getOrderByAndSort();
        String domain = CdnUtil.getDefaultNewDomainWithBackSlash();
        PageInfo<AvgOriginFile> avgOriginFiles = avgRepository.selectAvgFileList(types, page, pageSize, orderByAndSort,
                param.getName(), param.getTopicId(), param.getResource(), param.getStatus(), param.getTagNames());
        List<Integer> fileIds = avgOriginFiles.getList().stream().map(AvgOriginFile::getId).collect(Collectors.toList());
        Map<Integer, List<String>> fileIdAliasMap = avgFileAliasRelationRepository.findAliasGroupByIds(fileIds,
                AvgResourceType.FILE.getCode());
        List<AvgOriginFileView> avgOriginFileViews = avgOriginFiles.getList()
                .stream()
                .map((avgOriginFile) -> AvgOriginFileView.valueOf(avgOriginFile, domain,
                        fileIdAliasMap.get(avgOriginFile.getId())))
                .collect(Collectors.toList());
        return BizResult.success(PageView.form((int) avgOriginFiles.getTotal(), avgOriginFileViews));
    }

    public Map<Integer, AvgChapterModel> queryByIds(BatchQueryAvgParam batchQueryAvgParam) {
        if (CollectionUtils.isEmpty(batchQueryAvgParam.getAvgChapterIds())) {
            return Maps.newHashMap();
        }
        RpcResult<Map<Integer, AvgChapterModel>> rpcResult = avgService.batchQueryAvgChapter(
                batchQueryAvgParam.getAvgChapterIds());
        if (!rpcResult.isSuccess()) {
            return Maps.newHashMap();
        }
        return rpcResult.getData();
    }

    public BizResult<Void> handleOldData() {
        List<AvgProject> avgProjects = avgRepository.queryAllAvgProject();
        for (AvgProject avgProject : avgProjects) {
            log.info("handle avgProject, projectId:{}", avgProject.getObjectId());
            List<AvgChapter> avgChapters = avgRepository.queryAvgChapterByProjectId(avgProject.getObjectId());
            for (AvgChapter avgChapter : avgChapters) {
                avgChapter.setType(1);
                List<AvgChapter.Text> textList = avgChapter.getTextList();
                if (CollectionUtils.isEmpty(textList)) {
                    continue;
                }
                for (AvgChapter.Text text : textList) {
                    if (text.getNameCard() != null) {
                        text.setNameCardStr(String.valueOf(text.getNameCard()));
                    } else {
                        text.setNameCardStr("");
                    }
                }
                avgRepository.updateAvgChapter(avgChapter);
                log.info("update avgChapter, chapterId:{}", avgChapter.getChapterId());
            }
            log.info("handle avgProject, projectId:{} success", avgProject.getObjectId());
        }
        return BizResult.success();
    }

    public BizResult<Void> handleRecordOldData() {
        int offset = 0;
        int pageSize = 10000;
        while (true) {
            List<UserAvgChapterRecord> userAvgChapterRecords = avgRepository.queryByLimitAndOffset(offset, pageSize);
            if (CollectionUtils.isEmpty(userAvgChapterRecords)) {
                break;
            }
            Set<UserAvgChapterRecord> initRecords = userAvgChapterRecords.stream()
                    .filter(item -> item.getVersion() == null)
                    .collect(Collectors.toSet());
            log.info("handleRecordOldData, query size:{}, update size:{}", userAvgChapterRecords.size(), initRecords.size());
            if (!initRecords.isEmpty()) {
                try {
                    Map<Integer, List<UserAvgChapterRecord>> userInitRecordMap = initRecords.stream()
                            .collect(Collectors.groupingBy(UserAvgChapterRecord::getUserId));
                    Set<Integer> userIds = initRecords.stream().map(UserAvgChapterRecord::getUserId).collect(Collectors.toSet());
                    Map<Integer, List<UserAvgChapterRecord>> userAllRecords = avgRepository.queryUserAvgChapterRecordByUserId(
                            userIds);
                    Set<ObjectId> allObjectIds = initRecords.stream()
                            .map(item -> new ObjectId(item.getObjectId()))
                            .collect(Collectors.toSet());
                    List<ObjectId> needDeleteIds = Lists.newArrayList();
                    for (Map.Entry<Integer, List<UserAvgChapterRecord>> entry : userAllRecords.entrySet()) {
                        Integer userId = entry.getKey();
                        Map<Integer, UserAvgChapterRecord> chapterId2RecordMap = userInitRecordMap.getOrDefault(userId,
                                        new ArrayList<>())
                                .stream()
                                .collect(Collectors.toMap(UserAvgChapterRecord::getChapterId, Function.identity()));
                        List<UserAvgChapterRecord> exitRecords = entry.getValue();
                        for (UserAvgChapterRecord exitRecord : exitRecords) {
                            if (chapterId2RecordMap.containsKey(exitRecord.getChapterId())) {
                                Integer version = exitRecord.getVersion();
                                if (Objects.equals(version, 0)) {
                                    UserAvgChapterRecord userAvgChapterRecord = chapterId2RecordMap.get(
                                            exitRecord.getChapterId());
                                    needDeleteIds.add(new ObjectId(userAvgChapterRecord.getObjectId()));
                                }
                            }
                        }
                    }
                    //needUpdateIds = allObjectIds - needUpdateIds
                    List<ObjectId> needUpdateIds = allObjectIds.stream()
                            .filter(item -> !needDeleteIds.contains(item))
                            .collect(Collectors.toList());
                    avgRepository.batchDeleteAvgChapterRecord(needDeleteIds);
                    avgRepository.batchUpdateAvgChapterRecord(needUpdateIds);
                } catch (Exception e) {
                    log.error("Error during batch update", e);
                }
            }
            offset += pageSize;
        }

        return BizResult.success();
    }

    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public void washData(int type) {
        List<AvgOriginFile> allAudioFiles = avgRepository.queryAvgOriginFileByType(Collections.singleton(type));
        List<CommonAudio> data = Lists.newArrayList();
        for (AvgOriginFile avgOriginFile : allAudioFiles) {
            AvgOriginFile.Config config = avgOriginFile.getConfig();
            if (config == null) {
                config = new AvgOriginFile.Config();
            }

            String audioId = String.valueOf(BizIdGenerator.getId());
            Date now = new Date();
            CommonAudio commonAudio = new CommonAudio().setAudioId(audioId)
                    .setBusType(AudioBusType.AVG_AUDIO.getCode())
                    .setStatus(CommonAudioStatus.WAITING.getCode())
                    .setOriginAudio(new CommonAudio.AudioInfo().setUrl(avgOriginFile.getKey()))
                    .setLowBitAudio(new CommonAudio.AudioInfo())
                    .setCommitTaskTime(now)
                    .setCreatedAt(now)
                    .setUpdatedAt(now);
            data.add(commonAudio);

            config.setAudioId(audioId);
            avgOriginFile.setConfig(config);
        }

        avgRepository.batchInsertCommonAudio(data);
        avgRepository.batchUpdate(allAudioFiles);

        LettuceClusterClient client1 = LettuceClusterUtil.getClusterClientByName(
                CacheConfig.AVG_ORIGIN_FILE_INFO.getReadWriteVip());
        List<String> ids = allAudioFiles.stream()
                .map(avgOriginFile -> KeyGenerator.generate(CacheConfig.AVG_ORIGIN_FILE_INFO.getKeyPattern(),
                        avgOriginFile.getId()))
                .collect(Collectors.toList());
        client1.del(ids.toArray(new String[0]));
        List<String> names = allAudioFiles.stream()
                .map(avgOriginFile -> KeyGenerator.generate(CacheConfig.AVG_ORIGIN_FILE_INFO_NAME.getKeyPattern(),
                        avgOriginFile.getName()))
                .collect(Collectors.toList());
        client1.del(names.toArray(new String[0]));

        LettuceClusterClient client2 = LettuceClusterUtil.getClusterClientByName(
                CacheConfig.AVG_ORIGIN_FILE_INFO_PARENT_ID.getReadWriteVip());
        List<String> parentIds = allAudioFiles.stream()
                .map(avgOriginFile -> KeyGenerator.generate(CacheConfig.AVG_ORIGIN_FILE_INFO.getKeyPattern(),
                        avgOriginFile.getParentId()))
                .collect(Collectors.toList());
        client2.del(parentIds.toArray(new String[0]));
    }

    private boolean validateChineseName(String name) {
        // 定义正则表达式模式，匹配任何中文字符
        String pattern = "[\\u4e00-\\u9fa5]";
        // 创建Pattern对象
        return Pattern.compile(pattern).matcher(name).find();
    }

    public BizResult<Void> updateAnchor(AnchorUpdateParam param) {
        AvgOriginFile avgOriginFile = avgRepository.queryAvgFileById(param.getId());
        if (avgOriginFile == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件不存在");
        }
        if (avgOriginFile.getType() != AvgFileType.STUFF.getCode()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件类型不正确");
        }
        if (!param.isValidPoint(param.getAnchor())) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "锚点信息不正确");
        }
        AvgOriginFile.Config config = avgOriginFile.getConfig();
        config = null != config
                ? config.setAnchorInfo(param.getAnchor())
                : new AvgOriginFile.Config().setAnchorInfo(param.getAnchor());
        avgOriginFile.setConfig(config);
        avgRepository.updateAvgOriginFile(avgOriginFile);
        return BizResult.success();
    }

    public BizResult<Void> online(int id, int type, boolean isRpcRequest) {
        if (type == AvgFileType.UNKNOWN.getCode()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件类型不支持");
        }
        // 区分是不是spine文件
        if (SPINE_TYPE_SET.contains(type)) {
            AvgDir avgDir = avgRepository.queryAvgDirById(id);
            if (Objects.isNull(avgDir)) {
                // Q版立绘图片处理
                if (type == AvgFileType.Q_SPINE.getCode()) {
                    Map<Integer, AvgOriginFile> avgOriginFileMap = avgRepository.queryAvgOriginFileByIds(Lists.newArrayList(id));
                    AvgOriginFile avgOriginFile = avgOriginFileMap.get(id);
                    if (Objects.nonNull(avgOriginFile)) {
                        avgOriginFile.setStatus(AvgFileStatusType.ONLINE.getCode()).setUpdatedAt(new Date());
                        avgRepository.updateAvgOriginFile(avgOriginFile);
                        return BizResult.success();
                    }
                }
                log.error("avgDir File is not exist, id={}", id);
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "资源文件不存在");
            }
            if (!avgDir.getStatus().equals(AvgFileStatusType.OFFLINE.getCode())) {
                log.error("avgDir File status not offline , id={}, status={}", id, avgDir.getStatus());
                return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "dir资源文件非停用状态");
            }
            avgDir.setStatus(AvgFileStatusType.ONLINE.getCode()).setUpdatedAt(new Date());
            avgRepository.updateAvgDir(avgDir);
        } else if (type == AvgFileType.HOT_ZONE.getCode()) {
            AvgHotZone zone = avgRepository.queryAvgHotZoneById(id);
            if (Objects.isNull(zone)) {
                log.error("hotZone is not exist, id={}", id);
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "热区资源文件不存在");
            }
            if (!zone.getStatus().equals(AvgFileStatusType.OFFLINE.getCode())) {
                log.error("hotZone status not offline , id={}, status={}", id, zone.getStatus());
                return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "热区资源文件非停用状态");
            }
            zone.setStatus(AvgFileStatusType.ONLINE.getCode()).setUpdatedAt(new Date());
            avgRepository.updateAvgHotZone(zone);
        } else if (type == AvgFileType.GYROSCOPE.getCode()) {
            AvgGyroscope gyroscope = avgRepository.queryAvgGyroscopeById(id);
            if (Objects.isNull(gyroscope)) {
                log.error("gyroscope is not exist, id={}", id);
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "陀螺仪资源文件不存在");
            }
            if (!gyroscope.getStatus().equals(AvgFileStatusType.OFFLINE.getCode())) {
                log.error("hotZone status not offline , id={}, status={}", id, gyroscope.getStatus());
                return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "陀螺仪资源文件非停用状态");
            }
            gyroscope.setStatus(AvgFileStatusType.ONLINE.getCode()).setUpdatedAt(new Date());
            avgRepository.updateAvgGyroscope(gyroscope);
        } else {
            AvgOriginFile avgOriginFile = avgRepository.queryAvgFileById(id);
            if (Objects.isNull(avgOriginFile)) {
                log.error("avgOriginFile is not exist, id={}", id);
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "资源文件不存在");
            }
            if (!avgOriginFile.getStatus().equals(AvgFileStatusType.OFFLINE.getCode())) {
                log.error("avgOriginFile status not offline , id={}, status={}", id, avgOriginFile.getStatus());
                return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "资源文件非停用状态");
            }
            avgOriginFile.setStatus(AvgFileStatusType.ONLINE.getCode()).setUpdatedAt(new Date());
            avgRepository.updateAvgOriginFile(avgOriginFile);
        }
        if (!isRpcRequest) {
            Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_FILE_ONLINE)
                    .add("fileId", id)
                    .add("type", type);
            OperateLogUtils.asyncRecord(operation);
        }
        return BizResult.success();
    }

    public BizResult<Void> offline(int id, int type, boolean isRpcRequest) {
        if (type == AvgFileType.UNKNOWN.getCode()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件类型不支持");
        }
        if (SPINE_TYPE_SET.contains(type)) {
            AvgDir avgDir = avgRepository.queryAvgDirById(id);
            if (Objects.isNull(avgDir)) {
                // Q版立绘图片处理
                if (type == AvgFileType.Q_SPINE.getCode()) {
                    Map<Integer, AvgOriginFile> avgOriginFileMap = avgRepository.queryAvgOriginFileByIds(Lists.newArrayList(id));
                    AvgOriginFile avgOriginFile = avgOriginFileMap.get(id);
                    if (Objects.nonNull(avgOriginFile)) {
                        avgOriginFile.setStatus(AvgFileStatusType.OFFLINE.getCode()).setUpdatedAt(new Date());
                        avgRepository.updateAvgOriginFile(avgOriginFile);
                        return BizResult.success();
                    }
                }
                log.error("avgDir File is not exist, id={}", id);
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "资源文件不存在");
            }
            if (!avgDir.getStatus().equals(AvgFileStatusType.ONLINE.getCode())) {
                log.error("avgDir File status not offline , id={}, status={}", id, avgDir.getStatus());
                return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "dir资源文件非启用状态");
            }
            avgDir.setStatus(AvgFileStatusType.OFFLINE.getCode()).setUpdatedAt(new Date());
            avgRepository.updateAvgDir(avgDir);
        } else if (type == AvgFileType.HOT_ZONE.getCode()) {
            AvgHotZone zone = avgRepository.queryAvgHotZoneById(id);
            if (Objects.isNull(zone)) {
                log.error("zone is not exist, id={}", id);
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "热区资源文件不存在");
            }
            if (!zone.getStatus().equals(AvgFileStatusType.ONLINE.getCode())) {
                log.error("zone status not online , id={}, status={}", id, zone.getStatus());
                return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "热区资源文件非启用状态");
            }
            zone.setStatus(AvgFileStatusType.OFFLINE.getCode()).setUpdatedAt(new Date());
            avgRepository.updateAvgHotZone(zone);
        } else if (type == AvgFileType.GYROSCOPE.getCode()) {
            AvgGyroscope avgGyroscope = avgRepository.queryAvgGyroscopeById(id);
            if (Objects.isNull(avgGyroscope)) {
                log.error("gyroscope is not exist, id={}", id);
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "陀螺仪资源文件不存在");
            }
            if (!avgGyroscope.getStatus().equals(AvgFileStatusType.ONLINE.getCode())) {
                log.error("gyroscope status not online , id={}, status={}", id, avgGyroscope.getStatus());
                return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "陀螺仪资源文件非启用状态");
            }
            avgGyroscope.setStatus(AvgFileStatusType.OFFLINE.getCode()).setUpdatedAt(new Date());
            avgRepository.updateAvgGyroscope(avgGyroscope);
        } else {
            AvgOriginFile avgOriginFile = avgRepository.queryAvgFileById(id);
            if (Objects.isNull(avgOriginFile)) {
                log.error("avgOriginFile is not exist, id={}", id);
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "资源文件不存在");
            }
            if (!avgOriginFile.getStatus().equals(AvgFileStatusType.ONLINE.getCode())) {
                log.error("avgOriginFile status not online , id={}, status={}", id, avgOriginFile.getStatus());
                return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "资源文件非启用状态");
            }
            avgOriginFile.setStatus(AvgFileStatusType.OFFLINE.getCode()).setUpdatedAt(new Date());
            avgRepository.updateAvgOriginFile(avgOriginFile);
        }
        if (!isRpcRequest) {
            Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_FILE_OFFLINE)
                    .add("fileId", id)
                    .add("type", type);
            OperateLogUtils.asyncRecord(operation);
        }
        return BizResult.success();
    }

    public BizResult<Void> updateResource(UpdateResourceAvgParam updateResourceAvgParam) {
        int type = updateResourceAvgParam.getType();
        Integer resource = updateResourceAvgParam.getResource();
        Integer topicId = updateResourceAvgParam.getTopicId();
        List<String> names = updateResourceAvgParam.getNames();
        List<String> tagNames = updateResourceAvgParam.getTagNames();
        List<String> customTags = updateResourceAvgParam.getCustomTags();
        boolean deleteFlag = updateResourceAvgParam.isDeleteFlag();
        int operateType = updateResourceAvgParam.getOperateType();
        AvgFileType fileType = AvgFileType.getByCode(type);
        if (CollectionUtils.isEmpty(names)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "修改资源范围不可为空");
        }
        if (operateType == OperateTypeEnum.OPERATE_TAGNAMES.getCode()
                && CollectionUtils.isEmpty(tagNames)
                && CollectionUtils.isEmpty(customTags)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "标签不可为空");
        }
        if (fileType.getCode() == AvgFileType.UNKNOWN.getCode()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件类型不支持");
        }
        if (SPINE_TYPE_SET.contains(type)) {
            List<AvgDir> avgDirs = avgRepository.queryAvgDirByNames(names)
                    .stream()
                    .filter(v -> v.getType() == type)
                    .collect(Collectors.toList());
            if (avgDirs.isEmpty()) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "所填资源名称不存在");
            }
            for (AvgDir avgDir : avgDirs) {
                if (Objects.nonNull(resource)) {
                    avgDir.setResource(resource);
                }
                AvgDir.Config config = avgDir.getConfig() != null ? avgDir.getConfig() : new AvgDir.Config();
                if (Objects.nonNull(topicId)) {
                    config.setTopicId(topicId);
                }
                if (deleteFlag) {
                    config.setTagNames(removeFromList(config.getTagNames(), tagNames));
                    config.setCustomTags(removeFromList(config.getCustomTags(), customTags));
                } else {
                    config.setTagNames(mergeListIfPresent(config.getTagNames(), tagNames));
                    config.setCustomTags(mergeListIfPresent(config.getCustomTags(), customTags));
                }
                avgDir.setConfig(config);
            }
            avgRepository.batchUpdateAvgDirByPrimaryKey(avgDirs);
        } else if (type == AvgFileType.HOT_ZONE.getCode()) {
            List<AvgHotZone> avgHotZones = avgRepository.queryAvgHotZoneByNames(names);
            if (avgHotZones.isEmpty()) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "所填资源名称不存在");
            }
            for (AvgHotZone zone : avgHotZones) {
                if (Objects.nonNull(resource)) {
                    zone.setResource(resource);
                }
                AvgHotZone.Config config = zone.getConfig() != null ? zone.getConfig() : new AvgHotZone.Config();
                if (Objects.nonNull(topicId)) {
                    config.setTopicId(topicId);
                }
                if (deleteFlag) {
                    config.setTagNames(removeFromList(config.getTagNames(), tagNames));
                    config.setCustomTags(removeFromList(config.getCustomTags(), customTags));
                } else {
                    config.setTagNames(mergeListIfPresent(config.getTagNames(), tagNames));
                    config.setCustomTags(mergeListIfPresent(config.getCustomTags(), customTags));
                }
                zone.setConfig(config);
            }
            avgRepository.batchUpdateAvgHotZoneByPrimaryKey(avgHotZones);
        } else if (type == AvgFileType.GYROSCOPE.getCode()) {
            List<AvgGyroscope> avgGyroscopes = avgRepository.queryGyroscopeByNames(names);
            if (avgGyroscopes.isEmpty()) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "所填资源名称不存在");
            }
            for (AvgGyroscope gyroscope : avgGyroscopes) {
                if (Objects.nonNull(resource)) {
                    gyroscope.setResource(resource);
                }
                AvgGyroscope.Config config = gyroscope.getConfig() != null ? gyroscope.getConfig() : new AvgGyroscope.Config();
                if (Objects.nonNull(topicId)) {
                    config.setTopicId(topicId);
                }
                if (deleteFlag) {
                    config.setTagNames(removeFromList(config.getTagNames(), tagNames));
                    config.setCustomTags(removeFromList(config.getCustomTags(), customTags));
                } else {
                    config.setTagNames(mergeListIfPresent(config.getTagNames(), tagNames));
                    config.setCustomTags(mergeListIfPresent(config.getCustomTags(), customTags));
                }
                gyroscope.setConfig(config);
            }
            avgRepository.batchUpdateAvgGyroscopeByPrimaryKey(avgGyroscopes);
        } else {
            List<AvgOriginFile> avgOriginFiles = avgRepository.queryAvgFileByNames(names);
            if (avgOriginFiles.isEmpty()) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "所填资源名称不存在");
            }
            for (AvgOriginFile originFile : avgOriginFiles) {
                if (Objects.nonNull(resource)) {
                    originFile.setResource(resource);
                }
                AvgOriginFile.Config config = originFile.getConfig() != null
                        ? originFile.getConfig()
                        : new AvgOriginFile.Config();
                if (Objects.nonNull(topicId)) {
                    config.setTopicId(topicId);
                }
                if (deleteFlag) {
                    config.setTagNames(removeFromList(config.getTagNames(), tagNames));
                    config.setCustomTags(removeFromList(config.getCustomTags(), customTags));
                } else {
                    config.setTagNames(mergeListIfPresent(config.getTagNames(), tagNames));
                    config.setCustomTags(mergeListIfPresent(config.getCustomTags(), customTags));
                }
                originFile.setConfig(config);
            }
            avgRepository.batchUpdateAvgOriginFileByPrimaryKey(avgOriginFiles);
        }
        return BizResult.success();
    }

    public BizResult<Void> getAvgOriginFile(int chapterId) {
        BIZ_EXECUTOR.submit(() -> {
            avgChapterComponent.pushDigitalAsset(chapterId, AvgChapterSyncTypeEnum.INIT);
        });
        return BizResult.success();
    }

    // 初始化章节资源的方法，不会再使用了
    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> changeAvgRelationData() {
        // 查找mongo中所有未删除的段落，循环每一个段落中的texts
        List<AvgChapter> avgChapters = avgRepository.queryAllAvg()
                .stream()
                .filter(avgChapter -> avgChapter.getStatus() == 1)
                .filter(avgChapter -> CollectionUtils.isNotEmpty(avgChapter.getTextList()))
                .collect(Collectors.toList());
        // 对每一个text中涉及到的资源，根据名称和类型去不同的库中查找
        for (AvgChapter avgChapter : avgChapters) {
            // 插入文件和段落的关系
            AvgChapter.DirFileContents dirFileContents = avgChapter.getDirFileContents();
            List<String> aliasList = Stream.of(dirFileContents.getDynamicRoleDirNames(), dirFileContents.getSpineNames(),
                            dirFileContents.getQSpineNames(), dirFileContents.getFileNames(), dirFileContents.getHotZoneNames())
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(Set::stream)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Integer, List<AvgFileAliasRelation>> resourceTypeAliasMap = avgFileAliasRelationRepository.findByNamesGroupByType(
                    aliasList);

            // 获取目录类型资源
            List<AvgFileAliasRelation> dirAliasRelationList = resourceTypeAliasMap.getOrDefault(AvgResourceType.DIR.getCode(),
                    Collections.emptyList());
            Set<Integer> dirIds = dirAliasRelationList.stream().map(AvgFileAliasRelation::getFileId).collect(Collectors.toSet());
            // 获取文件类型资源
            List<AvgFileAliasRelation> fileAliasRelationList = resourceTypeAliasMap.getOrDefault(AvgResourceType.FILE.getCode(),
                    Collections.emptyList());
            Set<Integer> originFileIds = fileAliasRelationList.stream()
                    .map(AvgFileAliasRelation::getFileId)
                    .collect(Collectors.toSet());
            // 获取热区类型资源
            List<AvgFileAliasRelation> hotZoneAliasRelationList = resourceTypeAliasMap.getOrDefault(
                    AvgResourceType.HOT_ZONE.getCode(), Collections.emptyList());
            Set<Integer> hotZoneIds = hotZoneAliasRelationList.stream()
                    .map(AvgFileAliasRelation::getFileId)
                    .collect(Collectors.toSet());

            // 批量插入
            List<AvgFileChapterRelation> relations = Stream.of(dirIds.stream()
                                    .map(id -> AvgFileChapterRelation.toBean(id, avgChapter.getChapterId(), AvgResourceType.DIR.getCode())),
                            originFileIds.stream()
                                    .map(id -> AvgFileChapterRelation.toBean(id, avgChapter.getChapterId(),
                                            AvgResourceType.FILE.getCode())), hotZoneIds.stream()
                                    .map(id -> AvgFileChapterRelation.toBean(id, avgChapter.getChapterId(),
                                            AvgResourceType.HOT_ZONE.getCode()))).flatMap(Function.identity()) // 将多个流合并为一个流
                    .collect(Collectors.toList());
            avgFileChapterRelationRepository.batchInsert(relations);
        }
        return BizResult.success();
    }

    public BizResult<Void> hisMaterialPush() {
        // 查询所有
        List<AvgChapter> avgChapters = avgRepository.queryAllAvg()
                .stream()
                .filter(avgChapter -> avgChapter.getStatus() == 1)
                .collect(Collectors.toList());
        for (AvgChapter avgChapter : avgChapters) {
            avgChapterComponent.pushDigitalAsset(avgChapter.getChapterId(), AvgChapterSyncTypeEnum.INIT);
        }
        return BizResult.success();
    }

    public BizResult<PageResult<AvgGyroscopeView>> avgGyroscopeViewList(AvgFileQueryParam param) {
        int page = param.getPage();
        int pageSize = param.getPageSize();
        String orderByAndSort = param.getOrderByAndSort();
        String domain = CdnUtil.getDefaultNewDomainWithBackSlash();

        PageInfo<AvgGyroscope> avgGyroscopes = avgRepository.queryAvgGyroscope(page, pageSize, orderByAndSort, param.getName(),
                param.getTopicId(), param.getResource(), param.getStatus(), param.getTagNames());

        List<Integer> fileIds = avgGyroscopes.getList().stream().map(AvgGyroscope::getId).collect(Collectors.toList());
        Map<Integer, List<String>> avgGyroscopeAliasMap = avgFileAliasRelationRepository.findAliasGroupByIds(fileIds,
                AvgResourceType.GYROSCOPE.getCode());

        return BizResult.success(PageResult.from(avgGyroscopes,
                (gyroscope) -> AvgGyroscopeView.valueOf(gyroscope, domain, avgGyroscopeAliasMap.get(gyroscope.getId()))));
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Integer> avgGyroscopeSave(AvgGyroscopeSaveParam param) {
        if (validateChineseName(param.getName())) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "陀螺仪名字不能是中文");
        }
        if (param.getConfig() != null && CollectionUtils.isNotEmpty(param.getConfig().getGyroscopes())) {
            List<String> videoKeys = param.getConfig()
                    .getGyroscopes()
                    .stream()
                    .map(AvgGyroscopeSaveParam.GyroscopeItemParam::getImmediateVideo)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            Set<String> existVideoFiles = avgRepository.selectByTypeAndNames(AvgFileType.HIGHLIGHT_VIDEO.getCode(), videoKeys)
                    .stream()
                    .filter(ele -> ele.getStatus() == AvgFileStatusType.ONLINE.getCode())
                    .map(AvgOriginFile::getName)
                    .collect(Collectors.toSet());
            List<String> notExistVideoKeys = videoKeys.stream()
                    .filter(name -> !existVideoFiles.contains(name))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notExistVideoKeys)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(),
                        String.format("%s 高光视频不存在或已经下架", String.join(",", notExistVideoKeys)));
            }
        }
        if (Objects.isNull(param.getId())) {
            List<AvgFileAliasRelation> existAlias = avgFileAliasRelationRepository.findByNames(
                    Collections.singletonList(param.getName()));
            if (CollectionUtils.isNotEmpty(existAlias)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "陀螺仪已存在");
            }
            AvgGyroscope avgGyroscope = new AvgGyroscope().setName(param.getName()).setImageKey(param.getImageKey());
            if (Objects.nonNull(param.getConfig())) {
                avgGyroscope.setConfig(AvgGyroscopeSaveParam.toConfig(param.getConfig(), null));
            }
            avgRepository.insertAvgGyroscope(avgGyroscope);
            avgFileAliasRelationRepository.insert(new AvgFileAliasRelation().setName(avgGyroscope.getName())
                    .setFileId(avgGyroscope.getId())
                    .setType(AvgResourceType.GYROSCOPE.getCode()));
            return BizResult.success(avgGyroscope.getId());
        } else {
            int id = param.getId();
            Integer resource = param.getResource();
            Integer topicId = param.getTopicId();
            List<String> nickNames = param.getNickNames();
            if (resource == AvgFileResourceType.SPECIAL_RESOURCE.getCode() && topicId == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "该陀螺仪未选择所属专题");
            }
            AvgGyroscope avgGyroscope = avgRepository.queryAvgGyroscopeById(id);
            if (avgGyroscope == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "陀螺仪不存在");
            }
            // alias
            List<String> createAlias = Lists.newArrayList(param.getName());
            Optional.ofNullable(nickNames).ifPresent(createAlias::addAll);

            boolean hasDuplicatesName = createAlias.size() != Sets.newHashSet(createAlias).size();
            if (hasDuplicatesName) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "别名重复");
            }
            avgGyroscope.setName(param.getName()).setImageKey(param.getImageKey());
            if (Objects.nonNull(param.getConfig())) {
                avgGyroscope.setConfig(AvgGyroscopeSaveParam.toConfig(param.getConfig(), param.getTopicId()));
            }

            // 校验别名是否和别名以及热区的资源名重复
            List<AvgFileAliasRelation> currentFileAliases = avgFileAliasRelationRepository.findByFileIdsAndType(
                    Collections.singletonList(id), AvgResourceType.GYROSCOPE.getCode());
            List<Integer> deleteIds = currentFileAliases.stream().map(AvgFileAliasRelation::getId).collect(Collectors.toList());
            List<AvgFileAliasRelation> existFileAlias = avgFileAliasRelationRepository.findByNames(createAlias)
                    .stream()
                    .filter(aliasRelation -> !deleteIds.contains(aliasRelation.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existFileAlias)) {
                log.error("update avg gyroscope alias exists:{}", createAlias);
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "别名已存在");
            }

            List<String> aliasList = currentFileAliases.stream().map(AvgFileAliasRelation::getName).collect(Collectors.toList());
            avgFileAliasRelationRepository.deleteAliasRelationByIds(deleteIds, aliasList);

            List<AvgFileAliasRelation> fileAliasRelations = buildAvgAliasByFileInfo(id, AvgResourceType.GYROSCOPE.getCode(),
                    createAlias);
            avgFileAliasRelationRepository.batchInsert(fileAliasRelations);

            avgGyroscope.setResource(resource);
            AvgGyroscope.Config config = avgGyroscope.getConfig() != null ? avgGyroscope.getConfig() : new AvgGyroscope.Config();
            config.setTagNames(param.getTagNames());
            config.setCustomTags(param.getCustomTags());
            config.setTopicId(topicId);
            avgGyroscope.setConfig(config);
            avgGyroscope.setUpdatedAt(new Date());
            avgRepository.updateAvgGyroscope(avgGyroscope);
            return BizResult.success(avgGyroscope.getId());
        }
    }

    public BizResult<AvgGyroscopeFileView> avgGyroscopeUpload(MultipartFile file, int id, int fileType) {
        if (file.isEmpty()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件不能为空");
        }
        AvgGyroscope avgGyroscope = avgRepository.queryAvgGyroscopeById(id);
        if (avgGyroscope == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "陀螺仪不存在");
        }
        String domain = CdnUtil.getDefaultNewDomainWithBackSlash();
        if (fileType == AvgGyroscopeFileType.PNG.getCode()) {
            String msg = fileValidate(file, AvgFileType.GYROSCOPE.getCode(), null);
            if (Strings.isNotBlank(msg)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), msg);
            }
            if (!Optional.ofNullable(file.getOriginalFilename()).orElse("").endsWith(".png")) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件格式仅允许png");
            }
            String fileKey = AVG_FILE_PATH + System.currentTimeMillis() + "/" + file.getOriginalFilename();
            try {
                qiniuComponent.uploadWithStream(PAY_BUCKET_NAME, file.getBytes(), fileKey);
            } catch (IOException e) {
                log.error("avgFileUpload error", e);
                return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR);
            }
            avgGyroscope.setImageKey(fileKey);
            avgRepository.updateAvgGyroscope(avgGyroscope);
            String imageUrl = CdnHandler.getEncryptionUrl(domain + fileKey, CdnPayType.PAY.getCode());
            return BizResult.success(
                    new AvgGyroscopeFileView().setFileUrl(imageUrl).setFileKey(fileKey).setFileName(file.getName()));
        } else if (fileType == AvgGyroscopeFileType.ZIP.getCode()) {
            if (!Optional.ofNullable(file.getOriginalFilename()).orElse("").endsWith(".zip")) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件格式仅允许zip");
            }
            String basicPath = ZIP_FILE_PATH + System.currentTimeMillis() + SLASH;
            String fileOutPath = basicPath + file.getOriginalFilename();
            boolean isSuccess = FileUtils.saveMultipartFile(file, fileOutPath);
            if (!isSuccess) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件上传失败");
            }
            // 解压
            List<File> unzipFileList = FileUtils.unzip(fileOutPath);
            if (CollectionUtils.isEmpty(unzipFileList)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件解压失败");
            }
            // 删除无效文件
            unzipFileList = removeInvalidFiles(AvgFileType.GYROSCOPE, unzipFileList);
            try {
                for (File imageFile : unzipFileList) {
                    BufferedImage img = ImageIO.read(Files.newInputStream(imageFile.toPath()));
                    if (img.getWidth() != MaterialConstants.GYROSCOPE_ZIP_PNG_WIGHT_LIMIT
                            || img.getHeight() != MaterialConstants.GYROSCOPE_ZIP_PNG_HEIGHT_LIMIT) {
                        return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "晃动图片分辨率必须为"
                                + MaterialConstants.GYROSCOPE_ZIP_PNG_WIGHT_LIMIT
                                + "px *"
                                + MaterialConstants.GYROSCOPE_ZIP_PNG_HEIGHT_LIMIT
                                + "px");
                    }
                }
            } catch (Exception e) {
                log.error("Error load image file", e);
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "晃动图片读取失败");
            }
            if (CollectionUtils.isEmpty(unzipFileList)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "陀螺仪压缩包内无有效文件,需要png格式图片");
            }
            if (unzipFileList.size() > 20) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "陀螺仪压缩包内最多20个png格式图片");
            }
            AvgGyroscopeFileView fileView = new AvgGyroscopeFileView().setFileName(file.getOriginalFilename());
            List<AvgGyroscopeFileView.AvgGyroscopeFileItemView> fileList = Lists.newArrayListWithExpectedSize(
                    unzipFileList.size());
            for (File unzipFile : unzipFileList) {
                String fileKey = AVG_FILE_PATH + System.currentTimeMillis() + "/" + unzipFile.getName();
                qiniuComponent.uploadWithStream(PAY_BUCKET_NAME, unzipFile, fileKey);
                fileList.add(new AvgGyroscopeFileView.AvgGyroscopeFileItemView().setFileKey(fileKey)
                        .setFileName(unzipFile.getName())
                        .setFileUrl(CdnHandler.getEncryptionUrl(domain + fileKey, CdnPayType.PAY.getCode())));
            }
            fileView.setFileList(fileList);
            FileUtils.deleteDirectory(fileOutPath);
            return BizResult.success(fileView);
        }
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> avgGyroscopeDelete(int id) {
        avgRepository.deleteAvgGyroscope(id);
        deleteFileAliasRelations(id, AvgResourceType.GYROSCOPE.getCode());
        return BizResult.success();
    }


    private BizResult<AvgResourceBO> getAvgFile(int chapterId) {
        // 根据章节id查询该章节下所有的段落
        RpcResult<AvgChapterDTO> result = dataFootStoneService.getAvgChapter(chapterId);
        if (!result.isSuccess() || result.getData() == null) {
            log.info("查询章节信息失败 , chapterId = {}", chapterId);
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "查询章节信息失败");
        }
        AvgChapterDTO dto = result.getData();
        List<Integer> avgSegmentIds = dto.getAvgSegmentIds();
        List<AvgChapter> avgChapters = avgRepository.queryTextChapterByChapterIds(avgSegmentIds);
        Map<String, AvgResourceBO.AvgMaterial> avgMaterialMap = Maps.newHashMap();
        String domain = CdnUtil.getDefaultNewDomainWithBackSlash();
        // 循环段落数据
        avgChapters.forEach(chapter -> {
            // 构建每一个段落的所有资源
            buildAvgResource(chapter, avgMaterialMap, domain);
        });
        List<AvgResourceBO.AvgMaterial> avgMaterials = new ArrayList<>(avgMaterialMap.values());
        AvgResourceBO response = new AvgResourceBO();
        response.setTopicId(dto.getTopicId());
        response.setChapterId(chapterId);
        response.setAvgMaterials(avgMaterials);
        return BizResult.success(response);
    }


    public BizResult<AvgBaseImageView> avgBaseImageUpload(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isBlank(fileName)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件名不能为空");
        }
        String fileKey = AVG_FILE_PATH + System.currentTimeMillis() + "/" + fileName.replace(" ", "");
        try {
            qiniuComponent.uploadWithStream(PAY_BUCKET_NAME, file.getBytes(), fileKey);
        } catch (IOException e) {
            log.error("avgFileUpload error", e);
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR);
        }
        AvgBaseImageView avgBaseImageView = new AvgBaseImageView().setFileKey(fileKey)
                .setFileUrl(CdnHandler.getEncryptionUrl(CdnUtil.getDefaultNewDomainWithBackSlash() + fileKey, CdnPayType.PAY.getCode()))
                .setFileName(file.getOriginalFilename());
        return BizResult.success(avgBaseImageView);
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Integer> avgResourceTagSave(@Valid List<String> names) {
        List<AvgResourceTag> list = names.stream()
                .map(name -> new AvgResourceTag().setTagName(name))
                .collect(Collectors.toList());
        avgRepository.deleteAvgResourceTag();
        if (CollectionUtils.isNotEmpty(list)) {
            avgRepository.saveAvgResourceTag(list);
        }
        return BizResult.success();
    }

    public BizResult<Integer> chapterUpload(List<AvgChapterTextExcel> avgChapterTextList, Integer chapterId, String fileName,
                                            String chapterFileKey, boolean isRpcRequest) {
        if (chapterId == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "章节id不能为空");
        }
        AvgChapter dbAvgChapter = avgRepository.queryAvgChapterByChapterId(chapterId);
        if (dbAvgChapter == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "章节不存在");
        }

        List<AvgChapter.Text> avgChapterList = avgChapterTextList.stream()
                .map(AvgChapterTextExcel::toAvgChapterText)
                .collect(Collectors.toList());
        List<Integer> nextChapterIds = avgChapterList.stream().map(AvgChapter.Text::getNextChapter).collect(Collectors.toList());
        List<AvgChapter> nextChapters = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(nextChapterIds)) {
            nextChapters = avgRepository.queryAvgChapterByChapterIds(nextChapterIds, AvgChapterType.TEXT.getCode());
        }
        List<Integer> insertChapterIds = avgChapterList.stream()
                .map(AvgChapter.Text::getInsertChapter)
                .collect(Collectors.toList());
        List<AvgChapter> insertChapters = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(nextChapterIds)) {
            insertChapters = avgRepository.queryAvgChapterByChapterIds(insertChapterIds, AvgChapterType.INSERT_CHAPTER.getCode());
        }
        List<String> hotZoneNameList = avgChapterList.stream().map(Text::getHotZone).collect(Collectors.toList());
        List<AvgHotZone> avgHotZoneList = avgRepository.queryAvgHotZoneByNames(hotZoneNameList);
        Map<String, AvgHotZone> hotZoneMap = avgHotZoneList.stream().collect(Collectors.toMap(AvgHotZone::getName, Function.identity()));
        List<String> errors = AvgChapter.verify(dbAvgChapter.getType(), avgChapterList, nextChapters, insertChapters, hotZoneMap);
        if (CollectionUtils.isNotEmpty(errors)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), String.join("\r", errors));
        }
        AvgChapter avgChapter = new AvgChapter().setChapterFileName(fileName)
                .setChapterFileKey(chapterFileKey)
                .setChapterId(chapterId)
                .setTextList(avgChapterList);
        avgChapter.setTextContentMd5(avgChapter.getTextContentMd5());
        avgRepository.updateAvgChapter(avgChapter);
        avgChapterComponent.updateChapterResourceRelations(avgChapter);
        avgChapterComponent.pushDigitalAsset(avgChapter.getChapterId(), AvgChapterSyncTypeEnum.UPDATE);
        if (!isRpcRequest) {
            Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_FILE_UPDATE)
                    .add("id", chapterId)
                    .add("data", GsonUtils.toJson(avgChapter));
            OperateLogUtils.asyncRecord(operation);
        }

        return BizResult.success(chapterId);
    }

    public List<AvgChapterSimpleModel> queryTopicRelatedChapter(int topicId) {
        if (topicId <= 0) {
            return Lists.newArrayList();
        }
        AvgProject avgProject = avgRepository.getProjectByTopicId(topicId);
        if (avgProject == null) {
            log.info("queryTopicRelatedChapter, no topicId related avgProject, topicId={}", topicId);
            return Lists.newArrayList();
        }
        List<AvgChapter> avgChapters = avgRepository.queryAvgChapterByProjectId(avgProject.getObjectId());
        return avgChapters.stream()
                .filter(chapter -> chapter.getType() != null && chapter.getType() == AvgChapterType.TEXT.getCode() && CollectionUtils.isNotEmpty(
                        chapter.getTextList()))
                .map(AvgChapterSimpleModel::valueOf)
                .collect(Collectors.toList());
    }

    private void buildAvgResource(AvgChapter chapter, Map<String, AvgResourceBO.AvgMaterial> avgMaterialMap, String domain) {
        // excel 配置文件,免费空间
        buildAvgMaterialConfig(avgMaterialMap, chapter.getChapterFileKey(), chapter.getChapterFileName());

        List<AvgFileChapterRelation> avgFileChapterRelations = avgFileChapterRelationRepository.queryByChapterId(
                chapter.getChapterId());
        // 普通文件
        List<AvgFileChapterRelation> avgOriginFileRelations = avgFileChapterRelations.stream()
                .filter(relation -> relation.getType() == AvgResourceType.FILE.getCode())
                .collect(Collectors.toList());
        List<Integer> fileIds = avgOriginFileRelations.stream()
                .map(AvgFileChapterRelation::getFileId)
                .collect(Collectors.toList());
        // key = id，value = model
        Map<Integer, AvgOriginFile> avgOriginFileMap = avgRepository.queryAvgOriginFileByIds(fileIds);

        for (AvgFileChapterRelation originFile : avgOriginFileRelations) {
            AvgOriginFile avgOriginFile = avgOriginFileMap.get(originFile.getFileId());
            if (avgOriginFile == null) {
                log.error("chapterId={} avgOriginFile is null, fileId={}", chapter.getChapterId(), originFile.getFileId());
                continue;
            }
            AvgFileType fileType = AvgFileType.getByCode(avgOriginFile.getType());
            buildAvgMaterial(avgMaterialMap, fileType, avgOriginFile.getKey(), avgOriginFile.getName(), domain);
        }

        // dir压缩文件
        List<AvgFileChapterRelation> avgDirFileRelations = avgFileChapterRelations.stream()
                .filter(relation -> relation.getType() == AvgResourceType.DIR.getCode())
                .collect(Collectors.toList());
        List<Integer> parentIds = avgDirFileRelations.stream()
                .map(AvgFileChapterRelation::getFileId)
                .collect(Collectors.toList());
        Map<Integer, AvgDir> avgDirMap = avgRepository.queryAvgDirsByIds(parentIds)
                .stream()
                .collect(Collectors.toMap(AvgDir::getId, Function.identity()));

        // dir返回压缩包链接
        for (Integer dirId : parentIds) {
            AvgDir avgDir = avgDirMap.get(dirId);
            AvgFileType dirType = AvgFileType.getByCode(avgDir.getType());
            buildAvgMaterial(avgMaterialMap, dirType, avgDir.getKey(), avgDir.getName(), domain);
        }

        // 热区文件
        List<AvgFileChapterRelation> hotZoneFiles = avgFileChapterRelations.stream()
                .filter(relation -> relation.getType() == AvgResourceType.HOT_ZONE.getCode())
                .collect(Collectors.toList());
        List<Integer> hotZoneIds = hotZoneFiles.stream().map(AvgFileChapterRelation::getFileId).collect(Collectors.toList());
        Map<Integer, AvgHotZone> hotZoneMap = avgRepository.queryAvgHotZoneByIds(hotZoneIds)
                .stream()
                .collect(Collectors.toMap(AvgHotZone::getId, Function.identity()));
        for (Integer id : hotZoneIds) {
            AvgHotZone hotZone = hotZoneMap.get(id);
            buildAvgMaterial(avgMaterialMap, AvgFileType.HOT_ZONE, hotZone.getImageKey(), hotZone.getName(), domain);
        }
    }

    // pay file download
    private void buildAvgMaterial(Map<String, AvgResourceBO.AvgMaterial> avgMaterialMap, AvgFileType fileType,
            String chapterFileKey, String chapterFileName, String domain) {
        // 没有资产就不上报了
        if (StringUtils.isBlank(chapterFileKey)) {
            return;
        }
        AvgResourceBO.AvgMaterial avgMaterial = getAvgMaterial(avgMaterialMap, fileType);
        List<AvgResourceBO.Material> materials = avgMaterial.getMaterials();
        String privateDownloadUrl = qiniuComponent.getPrivateDownloadUrl(chapterFileKey, 7200);
        AvgResourceBO.Material material = AvgResourceBO.Material.valueOf(chapterFileName, chapterFileKey,
                domain + privateDownloadUrl, CdnPayType.PAY.getCode());
        materials.add(material);
    }

    // free excel download
    private void buildAvgMaterialConfig(Map<String, AvgResourceBO.AvgMaterial> avgMaterialMap, String chapterFileKey,
            String chapterFileName) {
        if (StringUtils.isBlank(chapterFileKey)) {
            return;
        }
        String freeBucketDomain = CdnUtil.getDefaultDomainWithBackSlash();
        AvgResourceBO.AvgMaterial avgMaterial = getAvgMaterial(avgMaterialMap, AvgFileType.CONF_DOC);
        List<AvgResourceBO.Material> materials = avgMaterial.getMaterials();
        String privateDownloadUrl = qiniuComponent.getPrivateDownloadUrl(chapterFileKey, 7200);
        AvgResourceBO.Material material = AvgResourceBO.Material.valueOf(chapterFileName, chapterFileKey,
                freeBucketDomain + privateDownloadUrl, CdnPayType.FREE.getCode());
        materials.add(material);
    }

    private static AvgResourceBO.AvgMaterial getAvgMaterial(Map<String, AvgResourceBO.AvgMaterial> avgMaterialMap,
            AvgFileType dirType) {
        AvgResourceBO.AvgMaterial avgMaterial = avgMaterialMap.get(dirType.getDigitalAssetType().getCode());
        if (avgMaterial == null) {
            avgMaterial = new AvgResourceBO.AvgMaterial();
            avgMaterial.setMaterialType(dirType.getDigitalAssetType().getCode());
            avgMaterial.setName(dirType.getDigitalAssetType().getDesc());
            avgMaterial.setMaterials(new ArrayList<>());
            avgMaterialMap.put(dirType.getDigitalAssetType().getCode(), avgMaterial);
        }
        return avgMaterial;
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Map<String, String>> handleFileAliasOldData() {
        List<AvgFileAliasRelation> avgFileAliasRelations = Lists.newArrayList();
        List<AvgOriginFile> avgOriginFiles = avgRepository.queryAvgOriginFileByTypes(AvgFileType.getAvgFileTypes());
        List<AvgDir> avgDirs = avgRepository.queryAvgDirByTypes(AvgFileType.getAvgDirTypes());
        List<AvgHotZone> avgHotZones = avgRepository.queryAllAvgHotZone();

        buildAvgFileAlias(avgOriginFiles, avgFileAliasRelations);
        buildAvgDirAlias(avgDirs, avgFileAliasRelations);
        buildHotZoneAlias(avgHotZones, avgFileAliasRelations);

        Map<String, String> resultMap = checkDuplicateAlias(avgFileAliasRelations);
        if (MapUtils.isNotEmpty(resultMap)) {
            log.error("checkDuplicateAlias exists error: {}", resultMap);
            return BizResult.result(resultMap, ResponseCodeMsg.BAD_REQUEST);
        }

        int totalCount = avgFileAliasRelationRepository.batchInsert(avgFileAliasRelations);
        resultMap.put("success", "init avg alias total count: " + totalCount);
        return BizResult.success(resultMap);
    }

    public BizResult<List<String>> avgTagNameList() {
        return BizResult.success(avgRepository.queryTagNameList());
    }

    private Map<String, String> checkDuplicateAlias(List<AvgFileAliasRelation> avgFileAliasRelations) {
        if (CollectionUtils.isEmpty(avgFileAliasRelations)) {
            Map<String, String> errorResult = Maps.newHashMap();
            errorResult.put("error", "avg file relations is null");
            return errorResult;
        }
        Map<String, List<AvgFileAliasRelation>> nameToRelationsMap = avgFileAliasRelations.stream()
                .collect(Collectors.groupingBy(AvgFileAliasRelation::getName));
        // 过滤出 List 大小大于 1 的条目
        return nameToRelationsMap.entrySet()
                .stream()
                .filter(entry -> entry.getValue().size() > 1)
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue()
                        .stream()
                        .map(relation -> relation.getFileId() + "-" + relation.getType())
                        .collect(Collectors.joining(", "))));
    }

    private static void buildAvgFileAlias(List<AvgOriginFile> avgOriginFiles, List<AvgFileAliasRelation> avgFileAliasRelations) {
        if (CollectionUtils.isEmpty(avgOriginFiles)) {
            return;
        }
        List<AvgFileAliasRelation> fileAliasRelations = avgOriginFiles.stream()
                .map(AvgFileAliasRelation::valueOf)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        avgFileAliasRelations.addAll(fileAliasRelations);
    }

    private static void buildAvgDirAlias(List<AvgDir> avgDirs, List<AvgFileAliasRelation> avgFileAliasRelations) {
        if (CollectionUtils.isEmpty(avgDirs)) {
            return;
        }
        List<AvgFileAliasRelation> dirAliasRelations = avgDirs.stream()
                .map(AvgFileAliasRelation::valueOf)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        avgFileAliasRelations.addAll(dirAliasRelations);
    }

    private static void buildHotZoneAlias(List<AvgHotZone> avgHotZones, List<AvgFileAliasRelation> avgFileAliasRelations) {
        if (CollectionUtils.isEmpty(avgHotZones)) {
            return;
        }
        List<AvgFileAliasRelation> hotZoneAliasRelations = avgHotZones.stream()
                .map(AvgFileAliasRelation::valueOf)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        avgFileAliasRelations.addAll(hotZoneAliasRelations);
    }

    // build alias relation
    private static List<AvgFileAliasRelation> buildAvgAliasByFileInfo(Integer fileId, Integer fileType, List<String> aliasList) {
        if (fileId == null || fileType == null || CollectionUtils.isEmpty(aliasList)) {
            return Collections.emptyList();
        }
        return AvgFileAliasRelation.valueOf(fileId, fileType, aliasList);
    }

    protected static boolean isUiStyleRelation(AvgFileChapterRelation relation, AvgDir chapterUiStyleDir) {
        if (chapterUiStyleDir == null) {
            // 如果没有UI样式目录，则不需要保留任何关系
            return false;
        }
        // 保留UI样式目录的目录类型关系
        return Objects.equals(relation.getFileId(), chapterUiStyleDir.getId())
                && relation.getType() == AvgResourceType.DIR.getCode();
    }

    /**
     * 删除avg资源文件名+别名关系
     * @param fileId 文件ID
     * @param resourceType 资源类型 com.kuaikan.role.game.api.enums.AvgResourceType
     */
    private void deleteFileAliasRelations(int fileId, int resourceType) {
        List<AvgFileAliasRelation> currentFileAliases = avgFileAliasRelationRepository.findByFileIdsAndType(
                Collections.singletonList(fileId), resourceType);
        List<Integer> deleteIds = currentFileAliases.stream().map(AvgFileAliasRelation::getId).collect(Collectors.toList());
        List<String> aliasList = currentFileAliases.stream().map(AvgFileAliasRelation::getName).collect(Collectors.toList());
        avgFileAliasRelationRepository.deleteAliasRelationByIds(deleteIds, aliasList);
    }

    private BizResult<Integer> checkTimeTouchParam(AvgHotZoneAddOrUpdateParam.HotZoneConfigParam config) {
        List<AvgHotZoneAddOrUpdateParam.HotZoneParam> hotZones = config.getHotZoneV2();
        if (CollectionUtils.isNotEmpty(hotZones)) {
            List<AvgHotZoneAddOrUpdateParam.HotZoneParam> timeTouchHotZones = hotZones.stream()
                    .filter(e -> e.getStyleType() == AvgHotZoneType.TOUCH.getCode() && Objects.nonNull(e.getTimedTouch()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(timeTouchHotZones)) {
                return null;
            }
            if (hotZones.size() > 1) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "限时点触热区只能单独配置");
            }
            AvgHotZoneAddOrUpdateParam.HotZoneParam timeTouchHotZone = timeTouchHotZones.get(0);
            AvgHotZoneAddOrUpdateParam.TimedTouchParam timedTouch = timeTouchHotZone.getTimedTouch();
            if (timedTouch.getDuration() <= 0) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "限时点触热区持续时间不能小于等于0");
            }
            int nextChapterId = timedTouch.getNextChapterId();
            AvgChapter avgChapter = avgRepository.queryAvgChapterByChapterId(nextChapterId);
            if (Objects.isNull(avgChapter) || !Objects.equals(avgChapter.getStatus(), CommonStatus.ONLINE.getCode())) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "AVG段落id不存在");
            }
            Optional<AvgChapter.Text> optionalText = Optional.ofNullable(avgChapter.getTextList())
                    .orElseGet(Lists::newArrayList)
                    .stream()
                    .filter(e -> StringUtils.equals(e.getTextId(), timedTouch.getNextTextId()))
                    .findFirst();
            if (!optionalText.isPresent()) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "成功后接text id不存在");
            }
        }
        return null;
    }

    private List<String> mergeListIfPresent(List<String> source, List<String> toMerge) {
        List<String> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(source)) {
            result.addAll(source);
        }
        if (CollectionUtils.isNotEmpty(toMerge)) {
            result.addAll(new ArrayList<>(toMerge));
        }
        return result;
    }

    private List<String> removeFromList(List<String> source, List<String> toRemove) {
        if (CollectionUtils.isEmpty(source)) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(toRemove)) {
            return new ArrayList<>(source);
        }

        List<String> result = new ArrayList<>(source);
        result.removeAll(toRemove);
        return result;
    }
}

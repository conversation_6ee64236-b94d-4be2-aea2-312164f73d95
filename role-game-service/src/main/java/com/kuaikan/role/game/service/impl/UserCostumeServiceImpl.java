package com.kuaikan.role.game.service.impl;

import static com.kuaikan.role.game.component.AbTestComponent.BASE_GROUP;
import static com.kuaikan.role.game.component.SaComponent.ROLE_CHANGE_CLOTHES;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.NotNull;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.api.bean.BlindBoxDrawContext;
import com.kuaikan.role.game.api.bean.BlindBoxDrawResult;
import com.kuaikan.role.game.api.bean.BlindBoxProbRuleConfig;
import com.kuaikan.role.game.api.bean.Costume;
import com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity;
import com.kuaikan.role.game.api.bean.CostumeBlindBoxActivityRewardRecord;
import com.kuaikan.role.game.api.bean.CostumeBlindBoxConfig;
import com.kuaikan.role.game.api.bean.CostumePart;
import com.kuaikan.role.game.api.bean.CostumePartRelation;
import com.kuaikan.role.game.api.bean.Furniture;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bean.RewardOrder;
import com.kuaikan.role.game.api.bean.RewardOrderBlindBoxExtraInfo;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.RoleCostumeRelation;
import com.kuaikan.role.game.api.bean.RoleGroupCostumeBlindBoxConfig;
import com.kuaikan.role.game.api.bean.UserActionRecord;
import com.kuaikan.role.game.api.bean.UserCostume;
import com.kuaikan.role.game.api.bean.UserCostumePart;
import com.kuaikan.role.game.api.bean.UserRole;
import com.kuaikan.role.game.api.bean.UserRoleOngoingSchedule;
import com.kuaikan.role.game.api.bean.UserRoleProperty;
import com.kuaikan.role.game.api.bo.CostumeCarouselInfo;
import com.kuaikan.role.game.api.common.RoleGameResponse;
import com.kuaikan.role.game.api.enums.ActivityStatus;
import com.kuaikan.role.game.api.enums.BlindBoxActivityStatus;
import com.kuaikan.role.game.api.enums.CostumeLevel;
import com.kuaikan.role.game.api.enums.CostumeStatus;
import com.kuaikan.role.game.api.enums.FurnitureType;
import com.kuaikan.role.game.api.enums.RewardOrderType;
import com.kuaikan.role.game.api.enums.UserActionType;
import com.kuaikan.role.game.api.model.ActivityCostumePreviewModel;
import com.kuaikan.role.game.api.model.ActivityDetailModel;
import com.kuaikan.role.game.api.model.BlindBoxActivityModel;
import com.kuaikan.role.game.api.model.BlindBoxActivityRecordModel;
import com.kuaikan.role.game.api.model.BlindBoxDrawResultModel;
import com.kuaikan.role.game.api.model.BlindBoxModel;
import com.kuaikan.role.game.api.model.BlindBoxRuleModel;
import com.kuaikan.role.game.api.model.ExperimentRuleModel;
import com.kuaikan.role.game.api.model.GroupCostumeModel;
import com.kuaikan.role.game.api.model.RoleCostumeListModel;
import com.kuaikan.role.game.api.model.UserBlindBoxActivityInfoModel;
import com.kuaikan.role.game.api.model.UserRoleCostumeModel;
import com.kuaikan.role.game.api.rpc.param.ActivityPrizeParam;
import com.kuaikan.role.game.api.rpc.param.BlindBoxActivityParam;
import com.kuaikan.role.game.api.rpc.param.BlindBoxActivityRecordParam;
import com.kuaikan.role.game.api.rpc.param.BlindBoxParam;
import com.kuaikan.role.game.api.rpc.param.BlindBoxRuleParam;
import com.kuaikan.role.game.api.rpc.param.CostumeExposureParam;
import com.kuaikan.role.game.api.rpc.param.CostumePreviewParam;
import com.kuaikan.role.game.api.rpc.param.CurrentRolesCostumeParam;
import com.kuaikan.role.game.api.rpc.param.ListGroupCostumeInfoParam;
import com.kuaikan.role.game.api.rpc.param.ListUserRoleCostumeParam;
import com.kuaikan.role.game.api.rpc.param.MockLotteryParam;
import com.kuaikan.role.game.api.rpc.param.RolesCostumeParam;
import com.kuaikan.role.game.api.rpc.param.SwitchCostumeParam;
import com.kuaikan.role.game.api.rpc.param.UserCostumeParam;
import com.kuaikan.role.game.api.rpc.result.CostumeModel;
import com.kuaikan.role.game.api.service.UserCostumeService;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;
import com.kuaikan.role.game.common.enums.RedDotEventType;
import com.kuaikan.role.game.component.AbTestComponent;
import com.kuaikan.role.game.component.CostumeBlindBoxComponent;
import com.kuaikan.role.game.component.CostumeComponent;
import com.kuaikan.role.game.component.RedDotComponent;
import com.kuaikan.role.game.component.RoleComponent;
import com.kuaikan.role.game.component.SaComponent;
import com.kuaikan.role.game.component.UserCostumePartComponent;
import com.kuaikan.role.game.component.UserScheduleComponent;
import com.kuaikan.role.game.component.UserStuffComponent;
import com.kuaikan.role.game.config.ApolloConfig;
import com.kuaikan.role.game.dao.mongo.RoleGroupBlindBoxConfigDao;
import com.kuaikan.role.game.dao.mongo.UserActionRecordDAO;
import com.kuaikan.role.game.dao.mongo.UserPageVisitDAO;
import com.kuaikan.role.game.repository.BlindBoxProbRuleConfigRepository;
import com.kuaikan.role.game.repository.CostumeBlindBoxActivityRepository;
import com.kuaikan.role.game.repository.CostumePartRelationRepository;
import com.kuaikan.role.game.repository.CostumePartRepository;
import com.kuaikan.role.game.repository.CostumeRepository;
import com.kuaikan.role.game.repository.FurnitureRepository;
import com.kuaikan.role.game.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.repository.RewardOrderRepository;
import com.kuaikan.role.game.repository.RoleCostumeRelationRepository;
import com.kuaikan.role.game.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.repository.RoleRepository;
import com.kuaikan.role.game.repository.UserCostumePartComposeOrderRepository;
import com.kuaikan.role.game.repository.UserCostumePartRepository;
import com.kuaikan.role.game.repository.UserCostumeRepository;
import com.kuaikan.role.game.repository.UserRolePropertyRepository;
import com.kuaikan.role.game.repository.UserRoleRepository;

/**
 * <AUTHOR>
 * @version 2024-03-01
 */
@DubboService
@Slf4j
public class UserCostumeServiceImpl implements UserCostumeService {

    private static final String BLIND_BOX_COMPLETE_ATMOSPHERE_TEXT = "%s集齐了%s装扮";
    private static final String BLIND_BOX_BUTTON_TITLE_TEXT = "加料%s次";
    @Resource
    private UserCostumeRepository userCostumeRepository;
    @Resource
    private CostumeRepository costumeRepository;
    @Resource
    private CostumePartRelationRepository costumePartRelationRepository;
    @Resource
    private CostumePartRepository costumePartRepository;
    @Resource
    private RoleCostumeRelationRepository roleCostumeRelationRepository;
    @Resource
    private UserRoleRepository userRoleRepository;
    @Resource
    private ApolloConfig apolloConfig;
    @Resource
    private UserCostumePartComponent userCostumePartComponent;
    @Resource
    private UserStuffComponent userStuffComponent;
    @Resource
    private UserCostumePartComposeOrderRepository userCostumePartComposeOrderRepository;
    @Resource
    private RoleComponent roleComponent;
    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;
    @Resource
    private RoleRepository roleRepository;
    @Resource
    private UserActionRecordDAO userActionRecordDAO;
    @Resource
    private SaComponent saComponent;
    @Resource
    private CostumeComponent costumeComponent;
    @Resource
    private UserRolePropertyRepository userRolePropertyRepository;
    @Resource
    private UserScheduleComponent userScheduleComponent;
    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;
    @Resource
    private RoleGroupBlindBoxConfigDao roleGroupBlindBoxConfigDao;
    @Resource
    private RedDotComponent redDotComponent;
    @Resource
    private CostumeBlindBoxActivityRepository costumeBlindBoxActivityRepository;
    @Resource
    private UserCostumePartRepository userCostumePartRepository;
    @Resource
    private RewardOrderRepository rewardOrderRepository;
    @Resource
    private UserPageVisitDAO userPageVisitDAO;
    @Resource
    private AbTestComponent abTestComponent;
    @Resource
    private BlindBoxProbRuleConfigRepository blindBoxProbRuleConfigRepository;
    @Resource
    private CostumeBlindBoxComponent costumeBlindBoxComponent;
    @Resource
    private FurnitureRepository furnitureRepository;

    @Override
    public RpcResult<List<UserRoleCostumeModel>> listUserRoleCostume(ListUserRoleCostumeParam param) {
        final UserRole userRole = userRoleRepository.queryUserRoleFromCache(param.getUserId(), param.getRoleId());
        if (userRole == null) {
            log.warn("listUserRoleCostume userRole not found, param:{}", param);
            return RpcResult.result(RoleGameResponse.ROLE_NOT_ADOPTED);
        }
        final List<RoleCostumeRelation> allRoleCostumeRelations = roleCostumeRelationRepository.queryByRoleIdFromCache(param.getRoleId());
        final Set<Integer> costumeIds = allRoleCostumeRelations.stream().map(RoleCostumeRelation::getCostumeId).collect(Collectors.toSet());
        final Map<Integer, Costume> costumeMap = costumeRepository.queryByIds(costumeIds);

        final List<CostumeModel> allCostumeModelsByRoleId = costumeComponent.getCostumeModels(costumeIds);
        final List<UserCostume> userCostumes = userCostumeRepository.queryByUserIdFromCache(param.getUserId());
        final Map<Integer, UserCostume> userCostumeMap = userCostumes.stream().collect(Collectors.toMap(UserCostume::getCostumeId, Function.identity()));
        final Map<Integer, RoleCostumeRelation> costumeIdRoleRelationMap = allRoleCostumeRelations.stream()
                .collect(Collectors.toMap(RoleCostumeRelation::getCostumeId, Function.identity()));
        final List<UserRoleCostumeModel> userRoleCostumeModels = allCostumeModelsByRoleId.stream().map(costumeModel -> {
            final UserCostume userCostume = userCostumeMap.get(costumeModel.getId());
            //用户未拥有且已下架的，不展示
            if (userCostume == null && costumeMap.get(costumeModel.getId()).getStatus() != CostumeStatus.ON_SHELF.getCode()) {
                log.debug("userCostume is null and costume is off shelf, costumeId:{}", costumeModel.getId());
                return null;
            }
            if (apolloConfig.getNotShowCostumeIds().contains(costumeModel.getId())) {
                log.debug("not show costume, costumeId:{}", costumeModel.getId());
                return null;
            }
            final RoleCostumeRelation roleCostumeRelation = costumeIdRoleRelationMap.get(costumeModel.getId());
            return UserRoleCostumeModel.valueOf(userCostume, costumeModel,
                    userRole.getEffectiveCostumeId() != null && userRole.getEffectiveCostumeId() == costumeModel.getId(), roleCostumeRelation, null, false);
        }).filter(Objects::nonNull).collect(Collectors.toList());
        final List<Integer> whiteList = keyValueConfigRepository.getWhiteList();
        final boolean inWhiteList = whiteList.contains(param.getUserId());
        if (inWhiteList) {
            userRoleCostumeModels.forEach(userRoleCostumeModel -> userRoleCostumeModel.setHasOwned(true));
        }
        log.debug("listUserRoleCostume, param:{}, result:{}, userCostumeMap:{}, inWhiteList:{}", param, userRoleCostumeModels, userCostumeMap, inWhiteList);
        return RpcResult.success(userRoleCostumeModels);
    }

    @Override
    public RpcResult<List<GroupCostumeModel>> listGroupCostume(ListGroupCostumeInfoParam param) {
        int userId = param.getUserId();
        int groupId = param.getGroupId();

        //  获取角色组中的角色关系
        final List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupIdFromCache(groupId);
        final List<Integer> groupRoleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toList());
        Map<Integer, Role> roleMap = roleRepository.queryByIdsFromCache(groupRoleIds);
        //  用户领养的角色
        List<UserRole> userRoles = userRoleRepository.queryUserRoleFromCache(userId);
        Set<Integer> userRoleIdSet = userRoles.stream().map(UserRole::getRoleId).collect(Collectors.toSet());

        //  获取角色组中的角色装扮关系, key: 角色id, value: 角色装扮列表
        final Map<Integer, List<RoleCostumeRelation>> groupRoleCostumeRelationMap = roleCostumeRelationRepository.queryByRoleIdFromCache(groupRoleIds);
        //  所有角色装扮关系
        final List<RoleCostumeRelation> groupRoleCostumeRelations = Lists.newArrayList();
        for (List<RoleCostumeRelation> roleCostumeRelations : groupRoleCostumeRelationMap.values()) {
            groupRoleCostumeRelations.addAll(roleCostumeRelations);
        }

        final Set<Integer> groupCostumeIds = groupRoleCostumeRelations.stream().map(RoleCostumeRelation::getCostumeId).collect(Collectors.toSet());

        //  角色组所有装扮，key: costumeId, value: costume
        final Map<Integer, Costume> costumeMap = costumeRepository.queryByIds(groupCostumeIds);
        Set<Integer> onShelfCostumeSet = costumeMap.values()
                .stream()
                .filter(costume -> costume.getStatus() == CostumeStatus.ON_SHELF.getCode())
                .map(Costume::getId)
                .collect(Collectors.toSet());

        //  角色组已经上架的 relation
        final Map<Integer, RoleCostumeRelation> costumeIdRoleRelationMap = groupRoleCostumeRelations.stream()
                .filter(roleCostumeRelation -> onShelfCostumeSet.contains(roleCostumeRelation.getCostumeId()))
                .collect(Collectors.toMap(RoleCostumeRelation::getCostumeId, Function.identity()));

        //  用户拥有的所有装扮
        final List<UserCostume> userCostumes = userCostumeRepository.queryByUserIdFromCache(userId);
        final Map<Integer, UserCostume> userCostumeMap = userCostumes.stream().collect(Collectors.toMap(UserCostume::getCostumeId, Function.identity()));

        RoleGroupCostumeBlindBoxConfig groupCostumeBlindBoxConfig = roleGroupBlindBoxConfigDao.findByGroupId(groupId);
        if (groupCostumeBlindBoxConfig == null) {
            log.warn("group costume blind box config is null, groupId:{}", groupId);
            return RpcResult.result(RoleGameResponse.GROUP_BLIND_BOX_CONFIG_NOT_FOUND);
        }

        boolean groupCostumeBlindBox = apolloConfig.getGroupCostumeBlindBox();
        List<RoleGroupCostumeBlindBoxConfig.Config> costumeBlindBoxConfigs = groupCostumeBlindBoxConfig.getCostumeBlindBoxConfigs();
        Map<Integer, RoleGroupCostumeBlindBoxConfig.Config> costumeConfigMap = costumeBlindBoxConfigs.stream()
                .collect(Collectors.toMap(RoleGroupCostumeBlindBoxConfig.Config::getCostumeId, Function.identity()));

        List<GroupCostumeModel> result = groupRoleIds.stream().map(roleId -> {
            GroupCostumeModel groupCostumeModel = new GroupCostumeModel();
            groupCostumeModel.setRoleId(roleId);
            Role role = roleMap.get(roleId);

            List<RoleCostumeRelation> roleCostumeRelations = groupRoleCostumeRelationMap.get(roleId);
            Set<Integer> roleCostumeIds = roleCostumeRelations.stream()
                    .map(RoleCostumeRelation::getCostumeId)
                    .filter(onShelfCostumeSet::contains)
                    .collect(Collectors.toSet());
            //  角色所有已经上架的装扮
            List<CostumeModel> costumeModels = costumeComponent.getCostumeModels(roleCostumeIds);
            List<Integer> costumeIds = costumeModels.stream().map(CostumeModel::getId).collect(Collectors.toList());
            // key = costumeId
            Map<Integer, Boolean> showNewCornerMarkMap = redDotComponent.batchHasCostumeNewEvent(userId, costumeIds);
            List<UserRoleCostumeModel> userRoleCostumeModels;
            if (userRoleIdSet.contains(roleId)) {
                final UserRole userRole = userRoleRepository.queryUserRoleFromCache(userId, roleId);
                userRoleCostumeModels = costumeModels.stream().map(costumeModel -> {
                    int costumeId = costumeModel.getId();
                    if (role.getDefaultCostumeId() == costumeId) {
                        costumeModel.setCpCostumeId(0);
                        costumeModel.setRelatedStoryId(0);
                    }
                    final UserCostume userCostume = userCostumeMap.get(costumeId);
                    final RoleCostumeRelation roleCostumeRelation = costumeIdRoleRelationMap.get(costumeId);
                    boolean showNewCornerMark = showNewCornerMarkMap.get(costumeId) != null ? showNewCornerMarkMap.get(costumeId) : false;
                    UserRoleCostumeModel userRoleCostumeModel = UserRoleCostumeModel.valueOf(userCostume, costumeModel,
                            userRole.getEffectiveCostumeId() != null && userRole.getEffectiveCostumeId() == costumeModel.getId(), roleCostumeRelation,
                            costumeConfigMap.get(costumeModel.getId()), groupCostumeBlindBox);
                    userRoleCostumeModel.setShowNewCornerMark(showNewCornerMark);
                    return userRoleCostumeModel;
                }).collect(Collectors.toList());
            } else {
                userRoleCostumeModels = costumeModels.stream().map(costumeModel -> {
                    int costumeId = costumeModel.getId();
                    if (role.getDefaultCostumeId() == costumeId) {
                        costumeModel.setCpCostumeId(0);
                        costumeModel.setRelatedStoryId(0);
                    }
                    final UserCostume userCostume = userCostumeMap.get(costumeId);
                    final RoleCostumeRelation roleCostumeRelation = costumeIdRoleRelationMap.get(costumeId);
                    boolean showNewCornerMark = showNewCornerMarkMap.get(costumeId) != null ? showNewCornerMarkMap.get(costumeId) : false;
                    UserRoleCostumeModel userRoleCostumeModel = UserRoleCostumeModel.valueOf(userCostume, costumeModel, false, roleCostumeRelation,
                            costumeConfigMap.get(costumeModel.getId()), groupCostumeBlindBox);
                    userRoleCostumeModel.setShowNewCornerMark(showNewCornerMark);
                    return userRoleCostumeModel;
                }).collect(Collectors.toList());
            }

            final List<Integer> whiteList = keyValueConfigRepository.getWhiteList();
            final boolean inWhiteList = whiteList.contains(userId);
            if (inWhiteList) {
                userRoleCostumeModels.forEach(userRoleCostumeModel -> userRoleCostumeModel.setHasOwned(true));
            }
            groupCostumeModel.setRoleCostumeModels(userRoleCostumeModels);
            return groupCostumeModel;
        }).collect(Collectors.toList());
        userPageVisitDAO.upsertCostume(userId);
        return RpcResult.success(result);
    }

    /**
     * 预览装扮
     */
    @Override
    public RpcResult<Void> preview(CostumePreviewParam param) {
        userCostumeRepository.updateByUserIdCostumeId(param.getUserId(), param.getCostumeId(), false);
        return RpcResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<Void> switchCostume(SwitchCostumeParam param) {
        final int userId = param.getUserId();
        int costumeId = param.getCostumeId();
        Costume costume = costumeRepository.queryByIdFromCache(costumeId);
        if (costume == null) {
            return RpcResult.result(RoleGameResponse.COSTUME_NOT_FOUND);
        }
        List<RoleCostumeRelation> roleCostumeRelations = roleCostumeRelationRepository.queryByRoleIdFromCache(param.getRoleId());
        if (roleCostumeRelations.stream().noneMatch(roleCostumeRelation -> roleCostumeRelation.getCostumeId() == costumeId)) {
            return RpcResult.result(RoleGameResponse.ROLE_COSTUME_RELATION_NOT_FOUND);
        }
        final List<Integer> whiteList = keyValueConfigRepository.getWhiteList();
        if (!whiteList.contains(userId)) {
            final UserCostume userCostume = userCostumeRepository.queryByUserIdCostumeId(userId, param.getCostumeId());
            if (userCostume == null) {
                log.warn("switchCostume userCostume not found, param:{}", param);
                return RpcResult.result(RoleGameResponse.USER_COSTUME_NOT_FOUND);
            }
        } else {
            log.info("switchCostume in whiteList, param:{}", param);
        }
        Role role = roleRepository.queryByIdFromCache(param.getRoleId());
        if (role == null) {
            log.warn("switchCostume role not found, param:{}", param);
            return RpcResult.result(RoleGameResponse.ROLE_NOT_EXIT);
        }
        UserRole userRoleInDb = userRoleRepository.queryUserRoleFromDBFilterTrial(userId)
                .stream()
                .filter(userRole -> userRole.getRoleId() == param.getRoleId())
                .findAny()
                .orElse(null);
        if (userRoleInDb == null) {
            log.warn("switchCostume userRole not found, param:{}", param);
            return RpcResult.result(RoleGameResponse.ROLE_NOT_ADOPTED);
        }
        UserRole userRole = new UserRole().setUserId(userId).setEffectiveCostumeId(param.getCostumeId()).setRoleId(param.getRoleId());
        userRoleRepository.updateByUserIdAndRoleIdSelective(userRole);
        Set<Integer> useAllCostumeIds = userCostumeRepository.queryByUserIdFromCache(param.getUserId())
                .stream()
                .map(UserCostume::getCostumeId)
                .collect(Collectors.toSet());
        Set<Integer> roleAllCostume = roleCostumeRelationRepository.queryByRoleIdFromCache(role.getId())
                .stream()
                .map(RoleCostumeRelation::getCostumeId)
                .collect(Collectors.toSet());
        int clothCount = useAllCostumeIds.stream().filter(roleAllCostume::contains).collect(Collectors.toSet()).size();
        UserRoleProperty userRoleProperty = userRolePropertyRepository.queryByUserIdAndRoleId(userId, param.getRoleId());
        Map<String, Object> data = new HashMap<>(8);
        data.put("ClothesID", costume.getId());
        data.put("ClothesName", costume.getName());
        data.put("RoleLevel", userRoleProperty.getRoleLevel());
        data.put("OCID", param.getRoleId());
        data.put("OCName", role.getName());
        data.put("ClothCount", clothCount);
        data.put("ClothesDegree", CostumeLevel.getByLevel(costume.getLevel()).getDesc());
        saComponent.uploadEventData(userId, ROLE_CHANGE_CLOTHES, data);
        recordSwitchCostumeAction(userId, param.getRoleId());
        return RpcResult.success();
    }

    private void recordSwitchCostumeAction(int userId, int roleId) {
        UserActionRecord actionRecord = new UserActionRecord().setUserId(userId)
                .setActionType(UserActionType.SWITCH_COSTUME.getCode())
                .setRoleId(roleId)
                .setCreatedAt(System.currentTimeMillis());
        userActionRecordDAO.insert(actionRecord);
    }

    @Override
    public RpcResult<BlindBoxModel> getBlindBox(BlindBoxParam param) {
        int roleId = param.getRoleId();
        int groupId = param.getGroupId();
        Boolean groupCostumeBlindBox = apolloConfig.getGroupCostumeBlindBox();

        List<Integer> roleIds;
        if (groupId != 0) {
            List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupIdFromCache(groupId);
            if (CollUtil.isEmpty(roleGroupRelations)) {
                return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组不存在");
            }
            if (groupCostumeBlindBox) {
                roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toList());
            } else {
                roleIds = Collections.singletonList(roleId);
            }
        } else {
            roleIds = Collections.singletonList(roleId);
        }

        List<BlindBoxModel.LotteryShow> lotteryShows = getLotteryShows(roleIds, groupId);
        if (CollUtil.isEmpty(lotteryShows)) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "奖池配置为空");
        }
        final Role role = roleRepository.queryByIdFromCache(param.getRoleId());
        BlindBoxModel model = new BlindBoxModel();
        model.setDescription(role.getTopicName());
        final List<CostumeCarouselInfo> costumeCarousels = userCostumePartComponent.getCostumeCarousels();
        model.setCompleteAtmosphere(getBlindBoxCompleteAtmosphere(costumeCarousels));
        model.setLotteryShows(lotteryShows);
        model.setButtons(getButtons());
        final KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.COSTUME_BLIND_BOX_CONFIG);
        if (keyValueConfig == null) {
            log.warn("getBlindBox keyValueConfig is null");
            return RpcResult.success(model);
        }
        CostumeBlindBoxConfig config = JsonUtils.findObject(keyValueConfig.getValue(), CostumeBlindBoxConfig.class);
        model.setRuleDescription(config.getRuleDescription());
        return RpcResult.success(model);
    }

    @Override
    public RpcResult<BlindBoxModel> getBlindBoxV2(BlindBoxParam param) {
        int roleId = param.getRoleId();
        int groupId = param.getGroupId();
        Boolean groupCostumeBlindBox = apolloConfig.getGroupCostumeBlindBox();

        List<Integer> roleIds;
        if (groupId != 0) {
            List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupIdFromCache(groupId);
            if (CollUtil.isEmpty(roleGroupRelations)) {
                return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组不存在");
            }
            if (groupCostumeBlindBox) {
                roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toList());
            } else {
                roleIds = Collections.singletonList(roleId);
            }
        } else {
            roleIds = Collections.singletonList(roleId);
        }

        List<BlindBoxModel.LotteryShow> lotteryShows = getLotteryShows(roleIds, groupId);
        final Role role = roleRepository.queryByIdFromCache(param.getRoleId());
        BlindBoxModel model = new BlindBoxModel();
        model.setDescription(role.getTopicName());
        final List<CostumeCarouselInfo> costumeCarousels = userCostumePartComponent.getCostumeCarousels();
        model.setCompleteAtmosphere(getBlindBoxCompleteAtmosphere(costumeCarousels));
        model.setLotteryShows(lotteryShows);
        // 普通盲盒button
        BlindBoxRuleModel ruleModel = getButtons(
                new BlindBoxRuleParam().setType(RewardOrderType.COSTUME_BLIND_BOX.getCode()).setGroupId(groupId).setRequestInfo(param.getRequestInfo()));
        model.setButtons(ruleModel.getButtons());
        model.setRuleDescription(ruleModel.getRuleDescription());
        return RpcResult.success(model);
    }

    @Override
    public RpcResult<BlindBoxDrawResultModel> getBlindBoxLotteryMock(MockLotteryParam param) {
        BlindBoxProbRuleConfig probRuleConfig = blindBoxProbRuleConfigRepository.queryByIdFromCache(param.getRuleId());
        if (probRuleConfig == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "获取概率规则失败，规则ID：" + param.getRuleId());
        }

        BlindBoxDrawContext drawContext = buildDrawContext(probRuleConfig, param);
        BlindBoxDrawResult drawResult = costumeBlindBoxComponent.draw(drawContext);
        if (drawResult == null || drawResult.getDrawInfos().isEmpty()) {
            log.error("drawResult is null, drawContext:{}", JsonUtils.writeValueAsString(drawContext));
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "获取抽奖失败");
        }

        return RpcResult.success(buildDrawResultModel(drawResult));
    }

    @Override
    public RpcResult<RoleCostumeListModel> queryCurrentRolesCostume(CurrentRolesCostumeParam param) {
        final int userId = param.getUserId();
        final List<UserRole> userRoles = userRoleRepository.queryUserRoleFromCache(userId);
        Set<Integer> costumeIds = Sets.newHashSetWithExpectedSize(userRoles.size());
        for (UserRole userRole : userRoles) {
            final Integer costumeId = userRole.getEffectiveCostumeId();
            costumeIds.add(costumeId);
        }
        final Map<Integer, Costume> costumeMap = costumeRepository.queryByIds(costumeIds);
        List<RoleCostumeListModel.RoleCostumeModel> roleCostumeListModels = Lists.newArrayListWithExpectedSize(userRoles.size());
        for (UserRole userRole : userRoles) {
            RoleCostumeListModel.RoleCostumeModel roleCostumeModel = new RoleCostumeListModel.RoleCostumeModel();
            roleCostumeModel.setCostumeId(userRole.getEffectiveCostumeId());
            final Costume costume = costumeMap.get(userRole.getEffectiveCostumeId());
            if (costume != null) {
                roleCostumeModel.setSpineMaterial(costume.getConfig().getActionSpineMaterial());
            }
            roleCostumeModel.setRoleId(userRole.getRoleId());
            roleCostumeListModels.add(roleCostumeModel);
        }
        RoleCostumeListModel roleCostumeListModel = new RoleCostumeListModel();
        roleCostumeListModel.setRoleCostumeModels(roleCostumeListModels);
        return RpcResult.success(roleCostumeListModel);
    }

    @Override
    public RpcResult<RoleCostumeListModel> queryCurrentRolesCostume(RolesCostumeParam param) {
        final int userId = param.getUserId();
        final List<UserRole> userRoles = userRoleRepository.queryUserRoleFromCache(userId);
        Set<Integer> costumeIds = Sets.newHashSetWithExpectedSize(userRoles.size());
        for (UserRole userRole : userRoles) {
            final Integer costumeId = userRole.getEffectiveCostumeId();
            costumeIds.add(costumeId);
        }
        final Map<Integer, Costume> costumeMap = costumeRepository.queryByIds(costumeIds);
        List<RoleCostumeListModel.RoleCostumeModel> roleCostumeListModels = Lists.newArrayListWithExpectedSize(userRoles.size());
        for (UserRole userRole : userRoles) {
            RoleCostumeListModel.RoleCostumeModel roleCostumeModel = new RoleCostumeListModel.RoleCostumeModel();
            roleCostumeModel.setCostumeId(userRole.getEffectiveCostumeId());
            final Costume costume = costumeMap.get(userRole.getEffectiveCostumeId());
            if (costume != null) {
                roleCostumeModel.setSpineMaterial(costume.getConfig().getActionSpineMaterial());
            }
            roleCostumeModel.setRoleId(userRole.getRoleId());
            roleCostumeListModels.add(roleCostumeModel);
        }
        RoleCostumeListModel roleCostumeListModel = new RoleCostumeListModel();
        roleCostumeListModel.setRoleCostumeModels(roleCostumeListModels);
        return RpcResult.success(roleCostumeListModel);
    }

    @Override
    public RpcResult<RoleCostumeListModel> queryDefaultRolesCostume(RolesCostumeParam param) {
        final int userId = param.getUserId();
        final List<UserRole> userRoles = userRoleRepository.queryUserRoleFromCache(userId);
        List<Integer> userRoleIds = userRoles.stream().map(UserRole::getRoleId).collect(Collectors.toList());

        List<Role> roleListByIds = roleRepository.getRoleListByIds(userRoleIds);
        Map<Integer, Integer> roleDefaultCostumeMap = roleListByIds.stream().collect(Collectors.toMap(Role::getId, Role::getDefaultCostumeId));

        List<Integer> defaultCostumeIds = roleListByIds.stream().map(Role::getDefaultCostumeId).collect(Collectors.toList());
        final Map<Integer, Costume> defaultCostumeMap = costumeRepository.queryByIds(defaultCostumeIds);

        List<RoleCostumeListModel.RoleCostumeModel> roleCostumeListModels = Lists.newArrayListWithExpectedSize(userRoleIds.size());

        for (Integer userRoleId : userRoleIds) {
            RoleCostumeListModel.RoleCostumeModel roleCostumeModel = new RoleCostumeListModel.RoleCostumeModel();
            Integer defaultCostumeId = roleDefaultCostumeMap.get(userRoleId);

            roleCostumeModel.setRoleId(userRoleId);
            roleCostumeModel.setCostumeId(defaultCostumeId);
            final Costume costume = defaultCostumeMap.get(defaultCostumeId);
            if (costume != null) {
                roleCostumeModel.setSpineMaterial(costume.getConfig().getActionSpineMaterial());
            }
            roleCostumeListModels.add(roleCostumeModel);
        }
        RoleCostumeListModel roleCostumeListModel = new RoleCostumeListModel();
        roleCostumeListModel.setRoleCostumeModels(roleCostumeListModels);
        return RpcResult.success(roleCostumeListModel);
    }

    @Override
    public RpcResult<RoleCostumeListModel> queryRolesCostumeByConfig(RolesCostumeParam param) {
        //  开启开关下发默认装扮，否则下发当前装扮
        Boolean ignoreCostume = apolloConfig.getScheduleIgnoreCostume();

        final int userId = param.getUserId();
        final List<UserRole> userRoles = userRoleRepository.queryUserRoleFromCache(userId);
        List<Integer> userRoleIds = userRoles.stream().map(UserRole::getRoleId).collect(Collectors.toList());
        List<Role> roleListByIds = roleRepository.getRoleListByIds(userRoleIds);

        Map<Integer, Integer> roleDefaultCostumeMap = roleListByIds.stream().collect(Collectors.toMap(Role::getId, Role::getDefaultCostumeId));
        Map<Integer, Integer> roleCurrentCostumeMap = Maps.newHashMapWithExpectedSize(userRoles.size());
        for (UserRole userRole : userRoles) {
            roleCurrentCostumeMap.put(userRole.getRoleId(), userRole.getEffectiveCostumeId());
        }

        final List<UserRoleOngoingSchedule> ongoingSchedules = userScheduleComponent.queryOnlineRoleSchedules(userId);
        Set<Integer> ongoingScheduleRoleIdSet = ongoingSchedules.stream().map(UserRoleOngoingSchedule::getRoleId).collect(Collectors.toSet());

        List<Integer> costumeIds = Lists.newArrayListWithExpectedSize(userRoles.size());
        Map<Integer, Integer> roleCostumeRelationMap = Maps.newHashMapWithExpectedSize(userRoles.size());
        for (UserRole userRole : userRoles) {
            Integer roleId = userRole.getRoleId();
            if (ongoingScheduleRoleIdSet.contains(roleId) && ignoreCostume) {
                roleCostumeRelationMap.put(roleId, roleDefaultCostumeMap.get(roleId));
                costumeIds.add(roleDefaultCostumeMap.get(roleId));
            } else {
                roleCostumeRelationMap.put(roleId, roleCurrentCostumeMap.get(roleId));
                costumeIds.add(roleCurrentCostumeMap.get(roleId));
            }
        }

        final Map<Integer, Costume> costumeMap = costumeRepository.queryByIds(costumeIds);

        List<RoleCostumeListModel.RoleCostumeModel> roleCostumeListModels = Lists.newArrayListWithExpectedSize(userRoles.size());
        for (UserRole userRole : userRoles) {
            RoleCostumeListModel.RoleCostumeModel roleCostumeModel = new RoleCostumeListModel.RoleCostumeModel();
            Integer roleId = userRole.getRoleId();
            roleCostumeModel.setRoleId(roleId);
            Integer costumeId = roleCostumeRelationMap.get(roleId);
            roleCostumeModel.setCostumeId(costumeId);
            final Costume costume = costumeMap.get(costumeId);
            if (costume != null) {
                roleCostumeModel.setSpineMaterial(costume.getConfig().getActionSpineMaterial());
            }
            roleCostumeListModels.add(roleCostumeModel);
        }

        RoleCostumeListModel roleCostumeListModel = new RoleCostumeListModel();
        roleCostumeListModel.setRoleCostumeModels(roleCostumeListModels);
        roleCostumeListModel.setDefaultCostume(ignoreCostume);
        return RpcResult.success(roleCostumeListModel);
    }

    @Override
    public RpcResult<List<CostumeBlindBoxActivity>> queryBlindBoxActivityByGroupId(BlindBoxActivityParam param) {
        int roleGroupId = param.getRoleGroupId();
        List<CostumeBlindBoxActivity> activities = costumeBlindBoxActivityRepository.queryByGroupIdFromCache(roleGroupId)
                .stream()
                .filter(activity -> activity.getStatus() == BlindBoxActivityStatus.ON_SHELF.getCode())
                .filter(this::activityInValidity)
                .collect(Collectors.toList());
        return RpcResult.success(activities);
    }

    @Override
    public RpcResult<BlindBoxActivityModel> queryBlindBoxActivityByActivityId(ActivityPrizeParam param) {
        final CostumeBlindBoxActivity activity = costumeBlindBoxActivityRepository.queryByActivityIdFromCache(param.getActivityId());
        if (activity == null) {
            log.warn("queryBlindBoxActivityByActivityId activity not found, activityId:{}", param.getActivityId());
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮盲盒活动不存在");
        }
        if (!activityInValidity(activity)) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮盲盒活动不在有效期");
        }
        final List<UserCostume> userCostumes = userCostumeRepository.queryByUserIdFromCache(param.getUserId());
        if (CollectionUtils.isEmpty(userCostumes)) {
            log.warn("No costumes found for user: {}", param.getUserId());
        }
        List<Integer> userCostumeIds = Optional.ofNullable(userCostumes)
                .orElse(Collections.emptyList())
                .stream()
                .map(UserCostume::getCostumeId)
                .collect(Collectors.toList());
        final List<Furniture> furnitureList = furnitureRepository.queryAllFromCache();
        if (CollectionUtils.isEmpty(furnitureList)) {
            log.warn("No furniture found");
        }
        List<Integer> interactiveFurnitureIds = Optional.ofNullable(furnitureList)
                .orElse(Collections.emptyList())
                .stream()
                .filter(furniture -> furniture.getType() == FurnitureType.INTERACTIVE.getCode())
                .map(Furniture::getId)
                .collect(Collectors.toList());
        BlindBoxActivityModel blindBoxActivityModel = BlindBoxActivityModel.valueOf(activity);
        blindBoxActivityModel.setInteractiveFurnitureIds(interactiveFurnitureIds);
        blindBoxActivityModel.setUserCostumeIds(userCostumeIds);
        return RpcResult.success(blindBoxActivityModel);
    }

    @Override
    public RpcResult<UserBlindBoxActivityInfoModel> queryBlindBoxActivityRewardRecordV2(ActivityPrizeParam param) {
        CostumeBlindBoxActivity activity = costumeBlindBoxActivityRepository.queryByActivityIdFromCache(param.getActivityId());
        if (activity == null) {
            log.warn("queryBlindBoxActivityByActivityId activity not found, activityId:{}", param.getActivityId());
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮盲盒活动不存在");
        }
        if (!activityInValidity(activity)) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮盲盒活动不在有效期");
        }
        UserBlindBoxActivityInfoModel userBlindBoxActivityInfoModel = new UserBlindBoxActivityInfoModel();
        userBlindBoxActivityInfoModel.setBlindBoxActivityModel(BlindBoxActivityModel.valueOf(activity));
        List<CostumeBlindBoxActivityRewardRecord> records = costumeBlindBoxActivityRepository.queryUserOnActivityRecord(param.getUserId(),
                param.getActivityId());
        if (CollectionUtils.isEmpty(records)) {
            log.warn("queryBlindBoxActivityRewardRecordByUserId records is empty, userId:{}", param.getUserId());
        }
        List<BlindBoxActivityRecordModel> recordModels = Lists.newArrayListWithExpectedSize(records.size());
        for (CostumeBlindBoxActivityRewardRecord record : records) {
            recordModels.add(BlindBoxActivityRecordModel.valueOf(record));
        }
        userBlindBoxActivityInfoModel.setBlindBoxActivityRecordModels(recordModels);
        return RpcResult.success(userBlindBoxActivityInfoModel);
    }

    @Override
    public RpcResult<List<BlindBoxActivityRecordModel>> queryBlindBoxActivityRewardRecordByUserId(ActivityPrizeParam param) {
        return null;
    }

    @Override
    public RpcResult<List<BlindBoxActivityRecordModel>> queryBlindBoxActivityRewardRecord(ActivityPrizeParam param) {
        List<CostumeBlindBoxActivityRewardRecord> records = costumeBlindBoxActivityRepository.queryUserOnActivityRecord(param.getUserId(),
                param.getActivityId());
        if (CollectionUtils.isEmpty(records)) {
            log.warn("queryBlindBoxActivityRewardRecordByUserId records is empty, userId:{}", param.getUserId());
            return RpcResult.success(Collections.emptyList());
        }
        List<BlindBoxActivityRecordModel> recordModels = Lists.newArrayListWithExpectedSize(records.size());
        for (CostumeBlindBoxActivityRewardRecord record : records) {
            recordModels.add(BlindBoxActivityRecordModel.valueOf(record));
        }
        return RpcResult.success(recordModels);
    }

    @Override
    public RpcResult<ActivityCostumePreviewModel> getActivityCostumePreview(BlindBoxActivityParam param) {
        int activityId = param.getActivityId();
        int userId = param.getUserId();

        ActivityCostumePreviewModel costumePreviewModel = new ActivityCostumePreviewModel();

        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.COSTUME_BLIND_BOX_CONFIG);
        CostumeBlindBoxConfig costumeBlindBoxConfig = JsonUtils.findObject(keyValueConfig.getValue(), CostumeBlindBoxConfig.class);
        costumePreviewModel.setRuleDescription(costumeBlindBoxConfig.getRuleDescription());

        CostumeBlindBoxActivity activity = costumeBlindBoxActivityRepository.queryByActivityIdFromCache(activityId);
        if (activity == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "活动不存在");
        }

        RoleGroupCostumeBlindBoxConfig groupBlindBoxConfig = roleGroupBlindBoxConfigDao.findByGroupId(activity.getRoleGroupId());
        if (groupBlindBoxConfig == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组装扮概率未配置");
        }
        Set<Integer> nonLotteryCostumeIds = Optional.ofNullable(groupBlindBoxConfig.getCostumeBlindBoxConfigs())
                .orElseGet(Collections::emptyList)
                .stream()
                .filter(config -> !config.isCanLottery())
                .map(RoleGroupCostumeBlindBoxConfig.Config::getCostumeId)
                .collect(Collectors.toSet());

        List<Integer> activityUpCostumeIds = getActivityUpCostumeIds(activity);
        List<Integer> crossCostumeIds = getCrossCostumeIdsByGroupId(activity.getRoleGroupId());
        Map<Integer, Costume> costumeMap = costumeRepository.queryByIds(crossCostumeIds);

        Map<Integer, List<CostumePartRelation>> costumePartRelationMap = costumePartRelationRepository.selectByCostumeIds(crossCostumeIds);
        Map<Integer, CostumePart> costumePartMap = costumePartRepository.selectByIds(
                costumePartRelationMap.values().stream().flatMap(List::stream).map(CostumePartRelation::getCostumePartId).collect(Collectors.toSet()));

        Set<Integer> userCostumePartSet = userCostumePartRepository.selectByUserId(userId)
                .stream()
                .map(UserCostumePart::getCostumePartId)
                .collect(Collectors.toSet());

        List<ActivityCostumePreviewModel.ActivityCostume> activityCostumes = crossCostumeIds.stream()
                .map(costumeId -> {
                    Costume costume = costumeMap.get(costumeId);
                    if (nonLotteryCostumeIds.contains(costumeId) || costume == null || costume.getStatus() != CostumeStatus.ON_SHELF.getCode()) {
                        return null;
                    }
                    ActivityCostumePreviewModel.ActivityCostume activityCostume = new ActivityCostumePreviewModel.ActivityCostume();
                    activityCostume.setId(costumeId);
                    activityCostume.setName(costume.getName());
                    activityCostume.setLevel(costume.getLevel());
                    Costume.Config config = costume.getConfig();
                    if (config != null) {
                        activityCostume.setThumbnail(config.getThumbnail());
                        activityCostume.setActionSpineMaterial(config.getActionSpineMaterial());
                    }

                    List<Integer> costumePartIds = costumePartRelationMap.get(costumeId)
                            .stream()
                            .sorted(Comparator.comparingInt(CostumePartRelation::getOrderNum))
                            .map(CostumePartRelation::getCostumePartId)
                            .collect(Collectors.toList());

                    List<ActivityCostumePreviewModel.ActivityCostume.ActivityCostumePart> activityCostumeParts = costumePartIds.stream().map(costumePartId -> {
                        CostumePart costumePart = costumePartMap.get(costumePartId);
                        if (costumePart == null) {
                            return null;
                        }
                        ActivityCostumePreviewModel.ActivityCostume.ActivityCostumePart activityCostumePart = new ActivityCostumePreviewModel.ActivityCostume.ActivityCostumePart();
                        activityCostumePart.setId(costumePartId);
                        activityCostumePart.setName(costumePart.getName());
                        activityCostumePart.setCostumePartImage(costumePart.getConfig().getConsumePartImage());
                        activityCostumePart.setHasOwned(userCostumePartSet.contains(costumePartId));
                        return activityCostumePart;
                    }).filter(Objects::nonNull).collect(Collectors.toList());

                    activityCostume.setCostumeParts(activityCostumeParts);
                    return activityCostume;
                })
                .filter(Objects::nonNull)
                .sorted(Comparator.comparingInt(activityCostume -> Objects.requireNonNull(activityUpCostumeIds).contains(activityCostume.getId()) ? 0 : 1))
                .collect(Collectors.toList());
        costumePreviewModel.setCostumes(activityCostumes);
        costumePreviewModel.setUpCostumeIds(activityUpCostumeIds);
        costumePreviewModel.setUpContents(getActivityUpContents(activity));
        costumePreviewModel.setStartAt(activity.getStartAt());
        costumePreviewModel.setEndAt(activity.getEndAt());
        return RpcResult.success(costumePreviewModel);
    }

    @Override
    public RpcResult<ActivityCostumePreviewModel> getActivityCostumePreviewV2(BlindBoxActivityParam param) {
        int activityId = param.getActivityId();
        int userId = param.getUserId();
        ActivityCostumePreviewModel costumePreviewModel = new ActivityCostumePreviewModel();

        CostumeBlindBoxActivity activity = costumeBlindBoxActivityRepository.queryByActivityIdFromCache(activityId);
        if (activity == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "活动不存在");
        }

        RoleGroupCostumeBlindBoxConfig groupBlindBoxConfig = roleGroupBlindBoxConfigDao.findByGroupId(activity.getRoleGroupId());
        if (groupBlindBoxConfig == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组装扮概率未配置");
        }
        Set<Integer> nonLotteryCostumeIds = Optional.ofNullable(groupBlindBoxConfig.getCostumeBlindBoxConfigs())
                .orElseGet(Collections::emptyList)
                .stream()
                .filter(config -> !config.isCanLottery())
                .map(RoleGroupCostumeBlindBoxConfig.Config::getCostumeId)
                .collect(Collectors.toSet());

        // 获取命中盲盒活动概率规则
        BlindBoxRuleModel ruleModel = getButtons(
                new BlindBoxRuleParam().setType(RewardOrderType.BLIND_BOX_ACTIVITY.getCode()).setActivityId(activityId).setRequestInfo(param.getRequestInfo()));
        String ruleDescription = Optional.ofNullable(ruleModel.getRuleDescription()).orElse(StringUtils.EMPTY);
        List<Integer> activityUpCostumeIds = Optional.ofNullable(ruleModel.getUpCostumeIds()).orElse(Collections.emptyList());

        List<Integer> crossCostumeIds = getCrossCostumeIdsByGroupId(activity.getRoleGroupId());
        Map<Integer, Costume> costumeMap = costumeRepository.queryByIds(crossCostumeIds);

        Map<Integer, List<CostumePartRelation>> costumePartRelationMap = costumePartRelationRepository.selectByCostumeIds(crossCostumeIds);
        Map<Integer, CostumePart> costumePartMap = costumePartRepository.selectByIds(
                costumePartRelationMap.values().stream().flatMap(List::stream).map(CostumePartRelation::getCostumePartId).collect(Collectors.toSet()));

        Set<Integer> userCostumePartSet = userCostumePartRepository.selectByUserId(userId)
                .stream()
                .map(UserCostumePart::getCostumePartId)
                .collect(Collectors.toSet());

        List<ActivityCostumePreviewModel.ActivityCostume> activityCostumes = crossCostumeIds.stream()
                .map(costumeId -> {
                    Costume costume = costumeMap.get(costumeId);
                    if (nonLotteryCostumeIds.contains(costumeId) || costume == null || costume.getStatus() != CostumeStatus.ON_SHELF.getCode()) {
                        return null;
                    }
                    ActivityCostumePreviewModel.ActivityCostume activityCostume = new ActivityCostumePreviewModel.ActivityCostume();
                    activityCostume.setId(costumeId);
                    activityCostume.setName(costume.getName());
                    activityCostume.setLevel(costume.getLevel());
                    Costume.Config config = costume.getConfig();
                    if (config != null) {
                        activityCostume.setThumbnail(config.getThumbnail());
                        activityCostume.setActionSpineMaterial(config.getActionSpineMaterial());
                    }

                    List<Integer> costumePartIds = costumePartRelationMap.get(costumeId)
                            .stream()
                            .sorted(Comparator.comparingInt(CostumePartRelation::getOrderNum))
                            .map(CostumePartRelation::getCostumePartId)
                            .collect(Collectors.toList());

                    List<ActivityCostumePreviewModel.ActivityCostume.ActivityCostumePart> activityCostumeParts = costumePartIds.stream().map(costumePartId -> {
                        CostumePart costumePart = costumePartMap.get(costumePartId);
                        if (costumePart == null) {
                            return null;
                        }
                        ActivityCostumePreviewModel.ActivityCostume.ActivityCostumePart activityCostumePart = new ActivityCostumePreviewModel.ActivityCostume.ActivityCostumePart();
                        activityCostumePart.setId(costumePartId);
                        activityCostumePart.setName(costumePart.getName());
                        activityCostumePart.setCostumePartImage(costumePart.getConfig().getConsumePartImage());
                        activityCostumePart.setHasOwned(userCostumePartSet.contains(costumePartId));
                        return activityCostumePart;
                    }).filter(Objects::nonNull).collect(Collectors.toList());

                    activityCostume.setCostumeParts(activityCostumeParts);
                    return activityCostume;
                })
                .filter(Objects::nonNull)
                .sorted(Comparator.comparingInt(activityCostume -> Objects.requireNonNull(activityUpCostumeIds).contains(activityCostume.getId()) ? 0 : 1))
                .collect(Collectors.toList());
        costumePreviewModel.setCostumes(activityCostumes);
        costumePreviewModel.setUpCostumeIds(activityUpCostumeIds);
        costumePreviewModel.setUpContents(getActivityUpContents(activity));
        costumePreviewModel.setRuleDescription(ruleDescription);
        costumePreviewModel.setStartAt(activity.getStartAt());
        costumePreviewModel.setEndAt(activity.getEndAt());
        return RpcResult.success(costumePreviewModel);
    }

    @Override
    public RpcResult<ActivityDetailModel> getBlindBoxActivityDetail(BlindBoxActivityParam param) {
        int activityId = param.getActivityId();
        int userId = param.getUserId();
        boolean mock = param.isMock();

        ActivityDetailModel activityDetailModel = new ActivityDetailModel();

        CostumeBlindBoxActivity activity = costumeBlindBoxActivityRepository.queryByActivityIdFromCache(activityId);
        if (activity == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "活动不存在");
        }

        if (!mock && (!activityInValidity(activity) || activity.getStatus() == ActivityStatus.NOT_ONLINE.getCode())) {
            return RpcResult.success();
        }

        CostumeBlindBoxActivity.Config config = activity.getConfig();
        if (config == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "活动配置不存在");
        }
        List<CostumeBlindBoxActivity.Reward> rewards = config.getRewards()
                .stream()
                .sorted(Comparator.comparingInt(CostumeBlindBoxActivity.Reward::getNeedNum))
                .collect(Collectors.toList());
        activityDetailModel.setActivity(activity);

        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupIdFromCache(activity.getRoleGroupId())
                .stream()
                .sorted(Comparator.comparingInt(RoleGroupRelation::getOrderNum))
                .collect(Collectors.toList());
        List<Integer> roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toList());
        Map<Integer, List<RoleCostumeRelation>> roleCostumeRelationMap = roleCostumeRelationRepository.queryByRoleIdFromCache(roleIds);
        List<Integer> costumeIds = roleCostumeRelationMap.values()
                .stream()
                .flatMap(List::stream)
                .map(RoleCostumeRelation::getCostumeId)
                .collect(Collectors.toList());
        Map<Integer, Costume> costumeMap = costumeRepository.queryByIds(costumeIds);

        List<ActivityDetailModel.SimpleRoleCostume> costumePreview = roleIds.stream().map(roleId -> {
            ActivityDetailModel.SimpleRoleCostume simpleRoleCostume = new ActivityDetailModel.SimpleRoleCostume().setRoleId(roleId);
            List<ActivityDetailModel.SimpleCostume> costumes = roleCostumeRelationMap.get(roleId).stream().map(roleCostumeRelation -> {
                Integer costumeId = roleCostumeRelation.getCostumeId();
                Costume costume = costumeMap.get(costumeId);
                if (costume == null || costume.getStatus() != CostumeStatus.ON_SHELF.getCode()) {
                    return null;
                }
                return new ActivityDetailModel.SimpleCostume().setCostumeId(costumeId).setImageInfo(costume.getConfig().getThumbnail());
            }).filter(Objects::nonNull).collect(Collectors.toList());
            simpleRoleCostume.setCostumes(costumes);
            return simpleRoleCostume;
        }).collect(Collectors.toList());

        activityDetailModel.setCostumePreview(costumePreview);
        activityDetailModel.setCostumeMap(costumeMap);

        ActivityDetailModel.PrizeInfo prizeInfo = new ActivityDetailModel.PrizeInfo().setDesc(config.getDrawCopywriting());
        int lotteryCount = 0;
        List<RewardOrder> rewardOrders = rewardOrderRepository.queryCurrentBlindBoxActivityOrder(userId, activityId);
        for (RewardOrder rewardOrder : rewardOrders) {
            if (StringUtils.isBlank(rewardOrder.getGiftId())) {
                lotteryCount += 1;
            } else {
                String extraInfoStr = rewardOrder.getExtraInfo();
                RewardOrderBlindBoxExtraInfo extraInfo = JsonUtils.findObject(extraInfoStr, RewardOrderBlindBoxExtraInfo.class);
                if (extraInfo != null) {
                    lotteryCount += extraInfo.getLotteryRecordBids().size();
                }
            }
        }

        List<Integer> unlockRewardLevel = Lists.newArrayList();
        for (CostumeBlindBoxActivity.Reward reward : rewards) {
            int cnt = reward.getNeedNum();
            prizeInfo.setImage(reward.getRewardImage());
            if (cnt > lotteryCount) {
                String desc = String.format("还差%s抽获得\n「%s」", cnt - lotteryCount, reward.getName());
                prizeInfo.setDesc(desc);
                break;
            }
            unlockRewardLevel.add(cnt);
        }

        if (lotteryCount == 0) {
            prizeInfo.setImage(config.getDrawRewardBtn());
            prizeInfo.setDesc(config.getDrawCopywriting());
        }

        int usedFreeCount = rewardOrderRepository.countFreeByActivityIdAndUserId(userId, activityId, RewardOrderType.BLIND_BOX_ACTIVITY.getCode());
        activityDetailModel.setNextReward(prizeInfo);
        activityDetailModel.setUsedFreeCount(usedFreeCount);

        Set<Integer> collectedRewardLevel = costumeBlindBoxActivityRepository.queryRewardRecordByUserIdAndActivityId(userId, activityId)
                .stream()
                .map(CostumeBlindBoxActivityRewardRecord::getLevelIndex)
                .collect(Collectors.toSet());
        activityDetailModel.setShowRedDot(!collectedRewardLevel.containsAll(unlockRewardLevel));

        activityDetailModel.setButtons(getButtons());
        return RpcResult.success(activityDetailModel);
    }

    @Override
    public RpcResult<ActivityDetailModel> getBlindBoxActivityDetailV2(BlindBoxActivityParam param) {
        int activityId = param.getActivityId();
        int userId = param.getUserId();
        boolean mock = param.isMock();

        ActivityDetailModel activityDetailModel = new ActivityDetailModel();

        CostumeBlindBoxActivity activity = costumeBlindBoxActivityRepository.queryByActivityIdFromCache(activityId);
        if (activity == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "活动不存在");
        }

        if (!mock && (!activityInValidity(activity) || activity.getStatus() == ActivityStatus.NOT_ONLINE.getCode())) {
            return RpcResult.success();
        }

        CostumeBlindBoxActivity.Config config = activity.getConfig();
        if (config == null) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "活动配置不存在");
        }
        List<CostumeBlindBoxActivity.Reward> rewards = config.getRewards()
                .stream()
                .sorted(Comparator.comparingInt(CostumeBlindBoxActivity.Reward::getNeedNum))
                .collect(Collectors.toList());
        activityDetailModel.setActivity(activity);

        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupIdFromCache(activity.getRoleGroupId())
                .stream()
                .sorted(Comparator.comparingInt(RoleGroupRelation::getOrderNum))
                .collect(Collectors.toList());
        List<Integer> roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toList());
        Map<Integer, List<RoleCostumeRelation>> roleCostumeRelationMap = roleCostumeRelationRepository.queryByRoleIdFromCache(roleIds);
        List<Integer> costumeIds = roleCostumeRelationMap.values()
                .stream()
                .flatMap(List::stream)
                .map(RoleCostumeRelation::getCostumeId)
                .collect(Collectors.toList());
        Map<Integer, Costume> costumeMap = costumeRepository.queryByIds(costumeIds);

        List<ActivityDetailModel.SimpleRoleCostume> costumePreview = roleIds.stream().map(roleId -> {
            ActivityDetailModel.SimpleRoleCostume simpleRoleCostume = new ActivityDetailModel.SimpleRoleCostume().setRoleId(roleId);
            List<ActivityDetailModel.SimpleCostume> costumes = roleCostumeRelationMap.get(roleId).stream().map(roleCostumeRelation -> {
                Integer costumeId = roleCostumeRelation.getCostumeId();
                Costume costume = costumeMap.get(costumeId);
                if (costume == null || costume.getStatus() != CostumeStatus.ON_SHELF.getCode()) {
                    return null;
                }
                return new ActivityDetailModel.SimpleCostume().setCostumeId(costumeId).setImageInfo(costume.getConfig().getThumbnail());
            }).filter(Objects::nonNull).collect(Collectors.toList());
            simpleRoleCostume.setCostumes(costumes);
            return simpleRoleCostume;
        }).collect(Collectors.toList());

        activityDetailModel.setCostumePreview(costumePreview);
        activityDetailModel.setCostumeMap(costumeMap);

        ActivityDetailModel.PrizeInfo prizeInfo = new ActivityDetailModel.PrizeInfo().setDesc(config.getDrawCopywriting());
        int lotteryCount = 0;
        List<RewardOrder> rewardOrders = rewardOrderRepository.queryCurrentBlindBoxActivityOrder(userId, activityId);
        for (RewardOrder rewardOrder : rewardOrders) {
            if (StringUtils.isBlank(rewardOrder.getGiftId())) {
                lotteryCount += 1;
            } else {
                String extraInfoStr = rewardOrder.getExtraInfo();
                RewardOrderBlindBoxExtraInfo extraInfo = JsonUtils.findObject(extraInfoStr, RewardOrderBlindBoxExtraInfo.class);
                if (extraInfo != null) {
                    lotteryCount += extraInfo.getLotteryRecordBids().size();
                }
            }
        }

        List<Integer> unlockRewardLevel = Lists.newArrayList();
        for (CostumeBlindBoxActivity.Reward reward : rewards) {
            int cnt = reward.getNeedNum();
            prizeInfo.setImage(reward.getRewardImage());
            if (cnt > lotteryCount) {
                String desc = String.format("还差%s抽获得\n「%s」", cnt - lotteryCount, reward.getName());
                prizeInfo.setDesc(desc);
                break;
            }
            unlockRewardLevel.add(cnt);
        }

        if (lotteryCount == 0) {
            prizeInfo.setImage(config.getDrawRewardBtn());
            prizeInfo.setDesc(config.getDrawCopywriting());
        }

        int usedFreeCount = rewardOrderRepository.countFreeByActivityIdAndUserId(userId, activityId, RewardOrderType.BLIND_BOX_ACTIVITY.getCode());
        activityDetailModel.setNextReward(prizeInfo);
        activityDetailModel.setUsedFreeCount(usedFreeCount);

        Set<Integer> collectedRewardLevel = costumeBlindBoxActivityRepository.queryRewardRecordByUserIdAndActivityId(userId, activityId)
                .stream()
                .map(CostumeBlindBoxActivityRewardRecord::getLevelIndex)
                .collect(Collectors.toSet());
        activityDetailModel.setShowRedDot(!collectedRewardLevel.containsAll(unlockRewardLevel));
        // 盲盒活动button
        BlindBoxRuleModel ruleModel = getButtons(
                new BlindBoxRuleParam().setType(RewardOrderType.BLIND_BOX_ACTIVITY.getCode()).setActivityId(activityId).setRequestInfo(param.getRequestInfo()));
        activityDetailModel.setButtons(ruleModel.getButtons());
        return RpcResult.success(activityDetailModel);
    }

    @Override
    public RpcResult<Void> insertUserBlindBoxActivityRecord(BlindBoxActivityRecordParam blindBoxActivityRecordParam) {
        CostumeBlindBoxActivityRewardRecord record = getCostumeBlindBoxActivityRewardRecord(blindBoxActivityRecordParam);
        int result = costumeBlindBoxActivityRepository.insertUserBlindBoxActivityRecord(record);
        if (result <= 0) {
            log.error("insertUserBlindBoxActivityRecord error, result={}, blindBoxActivityRecordParam={}", result, blindBoxActivityRecordParam);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "写入用户盲盒活动记录失败");
        }
        // 当前活动内领奖时的抽数
        List<RewardOrder> rewardOrders = rewardOrderRepository.queryCurrentBlindBoxActivityOrder(blindBoxActivityRecordParam.getUserId(),
                blindBoxActivityRecordParam.getActivityId());
        if (CollectionUtils.isEmpty(rewardOrders)) {
            log.error("trackActivityPrizeData error, rewardOrders is empty, trackActivityPrizeParam={}", blindBoxActivityRecordParam);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "当前活动内领奖时的抽数为空");
        }
        // 遍历领奖记录，累加lotteryCount
        int lotteryCountSum = rewardOrders.stream()
                .map(RewardOrder::getExtraInfo)
                .map(extraInfoStr -> JsonUtils.findObject(extraInfoStr, RewardOrderBlindBoxExtraInfo.class))
                .filter(Objects::nonNull)
                .mapToInt(extraInfo -> extraInfo.getLotteryRecordBids().size())
                .sum();
        // 装扮盲盒活动上报
        Map<String, Object> properties = new HashMap<>(8);
        properties.put("PrizeJialiaoCount", lotteryCountSum);
        properties.put("PrizeName", blindBoxActivityRecordParam.getPrizeName());
        properties.put("ActivityID", blindBoxActivityRecordParam.getActivityId());
        properties.put("ActivityName", blindBoxActivityRecordParam.getActivityName());
        String selectedPrize = blindBoxActivityRecordParam.getSelectedPrize();
        properties.put("SelectedPrize", StringUtils.isNotBlank(selectedPrize) ? selectedPrize : "");
        saComponent.uploadEventData(blindBoxActivityRecordParam.getUserId(), SaComponent.CLOTHES_ACTIVITY_PRIZE, properties);
        return RpcResult.success();
    }

    @Override
    public RpcResult<Void> costumeExposure(CostumeExposureParam costumeExposureParam) {
        int userId = costumeExposureParam.getUserId();
        List<Integer> costumeIds = costumeExposureParam.getCostumeIds();
        redDotComponent.batchClearBroadcastEvent(userId, RedDotEventType.ADD_COSTUME, costumeIds);
        redDotComponent.clearBroadcastEvent(userId, RedDotEventType.ROLE_GROUP_ADD_COSTUME, costumeExposureParam.getRoleGroupId());
        userCostumeRepository.batchUpdateByUserIdCostumeId(userId, costumeIds, false);
        return RpcResult.success();
    }

    @Override
    public RpcResult<List<Integer>> queryUserCostumeIdList(UserCostumeParam param) {
        final List<UserCostume> userCostumes = userCostumeRepository.queryByUserIdFromCache(param.getUserId());
        if (CollectionUtils.isEmpty(userCostumes)) {
            log.info("No costumes found for user: {}", param.getUserId());
            return RpcResult.success(Collections.emptyList());
        }
        return RpcResult.success(userCostumes.stream().map(UserCostume::getCostumeId).collect(Collectors.toList()));
    }

    private boolean activityInValidity(CostumeBlindBoxActivity activity) {
        long now = System.currentTimeMillis();
        return activity.getStartAt() <= now && activity.getEndAt() >= now;
    }

    private BlindBoxRuleModel getButtons(BlindBoxRuleParam param) {
        BlindBoxRuleModel ruleModel = new BlindBoxRuleModel();
        int userId = param.getRequestInfo().getUserId();
        Integer ruleId = getRuleId(param);
        if (ruleId == null) {
            log.error("getButtons ruleId is null, userId:{}, param:{}", userId, param);
            return ruleModel;
        }
        BlindBoxProbRuleConfig blindBoxProbRuleConfig = blindBoxProbRuleConfigRepository.queryByIdFromCache(ruleId);
        if (blindBoxProbRuleConfig == null) {
            log.error("getButtons error, blindBoxProbRuleConfig is null, userId:{}, ruleId:{}", userId, ruleId);
            return ruleModel;
        }
        List<BlindBoxProbRuleConfig.GearConfig> levelConfigList = blindBoxProbRuleConfig.getConfig().getGearConfigs();
        if (CollectionUtils.isEmpty(levelConfigList)) {
            log.error("getButtons error, levelConfigList is empty, userId:{}, ruleId:{}", userId, ruleId);
            return ruleModel;
        }
        List<BlindBoxModel.Button> buttons = levelConfigList.stream().map(gearConfig -> {
            BlindBoxModel.Button button = new BlindBoxModel.Button();
            button.setTitle(String.format(BLIND_BOX_BUTTON_TITLE_TEXT, gearConfig.getLotteryNum()));
            button.setAmount(gearConfig.getAmount());
            button.setGiftId(gearConfig.getGiftId());
            return button;
        }).collect(Collectors.toList());
        buttons.get(buttons.size() - 1).setCornerMark(blindBoxProbRuleConfig.getConfig().getCornerMark());
        List<Integer> upCostumeIds = Optional.ofNullable(blindBoxProbRuleConfig.getConfig().getUpCostumeRateList())
                .map(list -> list.stream().map(BlindBoxProbRuleConfig.UpCostumeRate::getCostumeId).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        return ruleModel.setButtons(buttons).setRuleDescription(blindBoxProbRuleConfig.getConfig().getRuleDescription()).setUpCostumeIds(upCostumeIds);
    }

    /** 获取盲盒ruleId */
    private Integer getRuleId(BlindBoxRuleParam param) {
        int userId = param.getRequestInfo().getUserId();
        if (RewardOrderType.BLIND_BOX_ACTIVITY.getCode() == param.getType()) {
            CostumeBlindBoxActivity activity = costumeBlindBoxActivityRepository.queryByActivityIdFromCache(param.getActivityId());
            if (activity == null || activity.getConfig() == null) {
                log.error("getButtons error, activity or config is null, userId={}, activityId={}", userId, param.getActivityId());
                return null;
            }
            return getRuleIdFromActivity(activity, param);
        } else if (RewardOrderType.COSTUME_BLIND_BOX.getCode() == param.getType()) {
            RoleGroupCostumeBlindBoxConfig config = roleGroupBlindBoxConfigDao.findByGroupId(param.getGroupId());
            if (config == null) {
                log.error("getButtons error, config is null, userId={}, groupId:{}", userId, param.getGroupId());
                return null;
            }
            return getRuleIdFromConfig(config, param);
        }
        return null;
    }

    private Integer getRuleIdFromActivity(CostumeBlindBoxActivity activity, BlindBoxRuleParam param) {
        CostumeBlindBoxActivity.Config config = activity.getConfig();
        CostumeBlindBoxActivity.ExptRule exptRules = config.getExptRules();
        return getRuleIdFromExptRules(ExperimentRuleModel.valueOf(exptRules), param, config.getDefaultRuleId());
    }

    private Integer getRuleIdFromConfig(RoleGroupCostumeBlindBoxConfig config, BlindBoxRuleParam param) {
        RoleGroupCostumeBlindBoxConfig.ExptRule exptRules = config.getExptRules();
        return getRuleIdFromExptRules(ExperimentRuleModel.valueOf(exptRules), param, config.getDefaultRuleId());
    }

    private Integer getRuleIdFromExptRules(ExperimentRuleModel exptRules, BlindBoxRuleParam param, Integer defaultRuleId) {
        if (exptRules.isExptOpen()) {
            String exptFlag = exptRules.getExptFlag();
            Pair<Boolean, String> abTestHit = abTestComponent.isAbTestHit(AbTestComponent.buildAbTestRequest(param.getRequestInfo()), exptFlag);
            Map<String, Integer> groupRuleMap = getGroupRuleMap(exptRules);
            return groupRuleMap.get(abTestHit.getLeft() ? abTestHit.getRight() : BASE_GROUP);
        } else {
            return defaultRuleId;
        }
    }

    private Map<String, Integer> getGroupRuleMap(ExperimentRuleModel exptRules) {
        return exptRules.getExptGroups()
                .stream()
                .collect(Collectors.toMap(ExperimentRuleModel.ExperimentGroupModel::getName, ExperimentRuleModel.ExperimentGroupModel::getRuleId));
    }

    @NotNull
    private static CostumeBlindBoxActivityRewardRecord getCostumeBlindBoxActivityRewardRecord(BlindBoxActivityRecordParam blindBoxActivityRecordParam) {
        CostumeBlindBoxActivityRewardRecord record = new CostumeBlindBoxActivityRewardRecord();
        record.setUserId(blindBoxActivityRecordParam.getUserId());
        record.setActivityId(blindBoxActivityRecordParam.getActivityId());
        record.setLevelIndex(blindBoxActivityRecordParam.getLevelIndex());
        record.setBid(blindBoxActivityRecordParam.getBid());
        record.setPrizeId(blindBoxActivityRecordParam.getPrizeId());
        record.setNum(blindBoxActivityRecordParam.getNum());
        record.setStatus(blindBoxActivityRecordParam.getStatus());
        return record;
    }

    private List<Integer> getCrossCostumeIdsByGroupId(int roleGroupId) {
        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupIdFromCache(roleGroupId)
                .stream()
                .sorted(Comparator.comparingInt(RoleGroupRelation::getOrderNum))
                .collect(Collectors.toList());
        List<Integer> roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toList());
        Map<Integer, List<RoleCostumeRelation>> roleCostumeRelationMap = roleCostumeRelationRepository.queryByRoleIdFromCache(roleIds);

        List<Integer> roleCostumeA = roleCostumeRelationMap.get(roleIds.get(0)).stream().map(RoleCostumeRelation::getCostumeId).collect(Collectors.toList());
        List<Integer> roleCostumeB = roleCostumeRelationMap.get(roleIds.get(1)).stream().map(RoleCostumeRelation::getCostumeId).collect(Collectors.toList());
        int mx = Math.max(roleCostumeA.size(), roleCostumeB.size());
        List<Integer> crossCostumeIds = Lists.newArrayListWithExpectedSize(roleCostumeA.size() + roleCostumeB.size());
        for (int i = 0; i < mx; i++) {
            if (i < roleCostumeA.size()) {
                crossCostumeIds.add(roleCostumeA.get(i));
            }
            if (i < roleCostumeB.size()) {
                crossCostumeIds.add(roleCostumeB.get(i));
            }
        }
        return crossCostumeIds;
    }

    private List<BlindBoxModel.Button> getButtons() {
        final KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.COSTUME_BLIND_BOX_CONFIG);
        if (keyValueConfig == null) {
            return Collections.emptyList();
        }
        CostumeBlindBoxConfig config = JsonUtils.findObject(keyValueConfig.getValue(), CostumeBlindBoxConfig.class);
        List<BlindBoxModel.Button> buttons = new ArrayList<>();
        for (CostumeBlindBoxConfig.GearConfig gearConfig : config.getGearConfigs()) {
            BlindBoxModel.Button button = new BlindBoxModel.Button();
            button.setTitle(String.format(BLIND_BOX_BUTTON_TITLE_TEXT, gearConfig.getLotteryNum()));
            button.setAmount(gearConfig.getAmount());
            button.setGiftId(gearConfig.getGiftId());
            buttons.add(button);
        }
        buttons.get(buttons.size() - 1).setCornerMark(config.getCornerMark());
        return buttons;
    }

    private List<String> getBlindBoxCompleteAtmosphere(List<CostumeCarouselInfo> costumeCarouselInfos) {
        List<String> completeAtmosphere = new ArrayList<>();
        for (CostumeCarouselInfo costumeCarouselInfo : costumeCarouselInfos) {
            completeAtmosphere.add(String.format(BLIND_BOX_COMPLETE_ATMOSPHERE_TEXT, costumeCarouselInfo.getUserName(), costumeCarouselInfo.getCostumeName()));
        }
        return completeAtmosphere;
    }

    private List<BlindBoxModel.LotteryShow> getLotteryShows(List<Integer> roleIds, int groupId) {
        Boolean groupCostumeBlindBox = apolloConfig.getGroupCostumeBlindBox();
        Map<Integer, Boolean> canLotteryMap = Maps.newHashMap();
        final Map<Integer, List<RoleCostumeRelation>> roleCostumeRelationsMap = roleCostumeRelationRepository.queryByRoleIdFromCache(roleIds);
        final List<RoleCostumeRelation> roleCostumeRelations = roleCostumeRelationsMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
        if (groupCostumeBlindBox) {
            RoleGroupCostumeBlindBoxConfig groupCostumeBlindBoxConfig = roleGroupBlindBoxConfigDao.findByGroupId(groupId);
            if (groupCostumeBlindBoxConfig == null) {
                log.warn("group costume blind box config is null, groupId:{}", groupId);
                return Collections.emptyList();
            }
            List<RoleGroupCostumeBlindBoxConfig.Config> configs = groupCostumeBlindBoxConfig.getCostumeBlindBoxConfigs();
            for (RoleGroupCostumeBlindBoxConfig.Config config : configs) {
                canLotteryMap.put(config.getCostumeId(), config.isCanLottery());
            }
        } else {
            for (RoleCostumeRelation roleCostumeRelation : roleCostumeRelations) {
                canLotteryMap.put(roleCostumeRelation.getCostumeId(), roleCostumeRelation.getConfig().isCanLottery());
            }
        }
        final Map<Integer, RoleCostumeRelation> roleCostumeRelationMap = roleCostumeRelations.stream()
                .collect(Collectors.toMap(RoleCostumeRelation::getCostumeId, Function.identity()));
        final Set<Integer> costumeIds = roleCostumeRelations.stream().map(RoleCostumeRelation::getCostumeId).collect(Collectors.toSet());
        final List<CostumeModel> costumeModels = costumeComponent.getCostumeModels(costumeIds)
                .stream()
                .filter(costumeModel -> costumeModel.getStatus() == CostumeStatus.ON_SHELF.getCode())
                .collect(Collectors.toList());
        final Map<Integer, List<CostumePartRelation>> costumePartRelationMap = costumePartRelationRepository.selectByCostumeIds(costumeIds);
        final Map<Integer, CostumePart> costumePartMap = costumePartRepository.selectByIds(
                costumePartRelationMap.values().stream().flatMap(List::stream).map(CostumePartRelation::getCostumePartId).collect(Collectors.toSet()));
        List<BlindBoxModel.LotteryShow> lotteryShows = Lists.newArrayListWithExpectedSize(costumeModels.size());
        for (CostumeModel costumeModel : costumeModels) {
            BlindBoxModel.LotteryShow lotteryShow = new BlindBoxModel.LotteryShow();
            BlindBoxModel.Costume costume = new BlindBoxModel.Costume().setId(costumeModel.getId())
                    .setName(costumeModel.getName())
                    .setImage(costumeModel.getThumbnail())
                    .setLevel(costumeModel.getLevel())
                    .setOrderNum(roleCostumeRelationMap.get(costumeModel.getId()).getOrderNum())
                    .setCreatedAt(costumeModel.getCreatedAt())
                    .setCostumeParts(Lists.newArrayListWithExpectedSize(costumeModel.getConsumePartIds().size()));
            costumeModel.getConsumePartIds().forEach(costumePartId -> {
                CostumePart costumePart = costumePartMap.get(costumePartId);
                if (costumePart != null) {
                    BlindBoxModel.CostumePart costumePartModel = new BlindBoxModel.CostumePart().setId(costumePart.getId())
                            .setName(costumePart.getName())
                            .setImage(costumePart.getConfig().getConsumePartImage());
                    costume.getCostumeParts().add(costumePartModel);
                }
            });
            lotteryShow.setCostume(costume);
            lotteryShows.add(lotteryShow);
        }
        return lotteryShows.stream()
                .filter(lotteryShow -> canLotteryMap.getOrDefault(lotteryShow.getCostume().getId(), false))
                .sorted(Comparator.comparing(BlindBoxModel.LotteryShow::getCostume, Comparator.comparing(BlindBoxModel.Costume::getOrderNum))
                        .thenComparing(BlindBoxModel.LotteryShow::getCostume, Comparator.comparing(BlindBoxModel.Costume::getCreatedAt).reversed()))
                .collect(Collectors.toList());

    }

    // 概率up装扮id
    private List<Integer> getActivityUpCostumeIds(CostumeBlindBoxActivity activity) {
        return Optional.ofNullable(activity)
                .map(CostumeBlindBoxActivity::getConfig)
                .map(CostumeBlindBoxActivity.Config::getUpCostume)
                .map(CostumeBlindBoxActivity.UpCostume::getUpCostumeIds)
                .orElseGet(() -> {
                    log.warn("装扮盲盒活动:{}未配置概率UP装扮", activity.getId());
                    return Collections.emptyList();
                });
    }

    // 概率up装扮文案
    private List<CostumeBlindBoxActivity.Content> getActivityUpContents(CostumeBlindBoxActivity activity) {
        return Optional.ofNullable(activity.getConfig())
                .map(CostumeBlindBoxActivity.Config::getUpCostume)
                .map(CostumeBlindBoxActivity.UpCostume::getContents)
                .orElseGet(() -> {
                    log.warn("装扮盲盒活动:{}未配置概率UP装扮文案", activity.getId());
                    return Collections.emptyList();
                });
    }

    private List<Integer> mergeLists(List<Integer> listA, List<Integer> listB) {
        Stream<Integer> streamA = (listA != null) ? listA.stream() : Stream.empty();
        Stream<Integer> streamB = (listB != null) ? listB.stream() : Stream.empty();
        return Stream.concat(streamA, streamB).distinct().collect(Collectors.toList());
    }

    private BlindBoxDrawContext buildDrawContext(BlindBoxProbRuleConfig probRuleConfig, MockLotteryParam param) {
        BlindBoxDrawContext drawContext = new BlindBoxDrawContext();
        BlindBoxProbRuleConfig.Config config = probRuleConfig.getConfig();

        drawContext.setOneStarPartProbability(config.getOneStarPartProbability());
        drawContext.setTwoStarPartProbability(config.getTwoStarPartProbability());
        drawContext.setThreeStarPartProbability(config.getThreeStarPartProbability());
        drawContext.setFourStarPartProbability(config.getFourStarPartProbability());
        drawContext.setFiveStarPartProbability(config.getFiveStarPartProbability());
        drawContext.setNotAcquiredCostumePartProbability(config.getNotAcquiredCostumePartProbability());

        drawContext.setGearConfig(new BlindBoxDrawContext.GearConfig().setLotteryNum(param.getLotteryNum()));
        drawContext.setLotteryConfigs(param.getLotteryConfigs().stream().map(BlindBoxDrawContext.LotteryConfig::valueOf).collect(Collectors.toList()));

        drawContext.setGuaranteedConfig(
                new BlindBoxDrawContext.GuaranteedConfig().setLeastLevel(Optional.ofNullable(config.getTenDrawMinimumStar()).orElse(0)));
        drawContext.setUpConfigs(Optional.ofNullable(config.getUpCostumeRateList())
                .orElse(Lists.newArrayList())
                .stream()
                .map(BlindBoxDrawContext.UpConfig::valueOf)
                .collect(Collectors.toList()));
        drawContext.setTargetConfig(Optional.ofNullable(config.getTargetedCostumeOdds()).map(BlindBoxDrawContext.TargetConfig::valueOf).orElse(null));

        drawContext.setUserContext(
                new BlindBoxDrawContext.UserContext().setUserId(0).setOwnedCostumeIds(Sets.newHashSet()).setOwnedCostumePartIds(Sets.newHashSet()));
        return drawContext;
    }

    private BlindBoxDrawResultModel.DrawInfoModel convertToDrawInfoModel(BlindBoxDrawResult.DrawInfo drawInfo) {
        return new BlindBoxDrawResultModel.DrawInfoModel().setCostumeId(drawInfo.getCostumeId())
                .setCostumePartId(drawInfo.getCostumePartId())
                .setTargetAwardCostumeId(drawInfo.getTargetAwardCostumeId())
                .setLevel(drawInfo.getCostumeLevel().getLevel())
                .setCostumeProbabilities(String.valueOf(drawInfo.getCostumeProbabilities()))
                .setCostumeRanges(String.valueOf(drawInfo.getCostumeRanges()))
                .setCostumeRandom(drawInfo.getCostumeRandom())
                .setFallback(drawInfo.isFallback())
                .setGuaranteed(drawInfo.isGuaranteed())
                .setNonGuaranteedCount(drawInfo.getNonGuaranteedCount())
                .setCostumeLevelRanges(String.valueOf(drawInfo.getCostumeLevelRanges()));
    }

    private BlindBoxDrawResultModel buildDrawResultModel(BlindBoxDrawResult drawResult) {
        BlindBoxDrawResultModel drawResultModel = new BlindBoxDrawResultModel();
        drawResultModel.setDrawInfoModels(drawResult.getDrawInfos().stream().map(this::convertToDrawInfoModel).collect(Collectors.toList()));
        return drawResultModel;
    }
}

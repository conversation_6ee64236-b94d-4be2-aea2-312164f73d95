package com.kuaikan.role.game.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;

import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.game.common.util.gson.GsonUtils;
import com.kuaikan.idgenerator.sdk.BufferedIdGenerator;
import com.kuaikan.role.game.api.bean.Cabin;
import com.kuaikan.role.game.api.bean.Costume;
import com.kuaikan.role.game.api.bean.CourtyardConfig;
import com.kuaikan.role.game.api.bean.CourtyardRoleGroupRelation;
import com.kuaikan.role.game.api.bean.Furniture;
import com.kuaikan.role.game.api.bean.FurnitureActivityConfig;
import com.kuaikan.role.game.api.bean.FurnitureActivityFurnitureGroupRelation;
import com.kuaikan.role.game.api.bean.FurnitureGroup;
import com.kuaikan.role.game.api.bean.FurnitureGroupRelation;
import com.kuaikan.role.game.api.bean.FurnitureGroupRoleGroupRelation;
import com.kuaikan.role.game.api.bean.FurnitureStoryRelation;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.RoleGroup;
import com.kuaikan.role.game.api.bean.RoleGroup.Config;
import com.kuaikan.role.game.api.bean.RoleGroup.HomeInfo;
import com.kuaikan.role.game.api.bean.Scene;
import com.kuaikan.role.game.api.bean.SpineMaterial;
import com.kuaikan.role.game.api.bean.StoryRoleRelation;
import com.kuaikan.role.game.api.bean.UserCabin;
import com.kuaikan.role.game.api.bean.UserFurniture;
import com.kuaikan.role.game.api.bean.UserFurnitureCount;
import com.kuaikan.role.game.api.bean.UserFurnitureDecomposeOrder;
import com.kuaikan.role.game.api.bean.UserFurnitureGroup;
import com.kuaikan.role.game.api.bean.UserFurnitureGroupRelation;
import com.kuaikan.role.game.api.bean.UserFurniturePostmark;
import com.kuaikan.role.game.api.bean.UserFurniturePostmarkFlow;
import com.kuaikan.role.game.api.bean.UserHome;
import com.kuaikan.role.game.api.bean.UserPostmarkAssignRecord;
import com.kuaikan.role.game.api.bean.UserPostmarkAssignRecordFlow;
import com.kuaikan.role.game.api.bean.UserRole;
import com.kuaikan.role.game.api.bean.UserRoleGroupEmotionBond;
import com.kuaikan.role.game.api.bean.UserScene;
import com.kuaikan.role.game.api.bo.FurniturePostmarkConfig;
import com.kuaikan.role.game.api.common.RoleGameResponse;
import com.kuaikan.role.game.api.enums.CostumeStatus;
import com.kuaikan.role.game.api.enums.FlowType;
import com.kuaikan.role.game.api.enums.FurnitureAcquisitionWayType;
import com.kuaikan.role.game.api.enums.FurnitureChargeSource;
import com.kuaikan.role.game.api.enums.FurnitureGroupStatus;
import com.kuaikan.role.game.api.enums.FurnitureSize;
import com.kuaikan.role.game.api.enums.FurnitureType;
import com.kuaikan.role.game.api.enums.FurnitureTypeAndSize;
import com.kuaikan.role.game.api.enums.SceneType;
import com.kuaikan.role.game.api.enums.StoryRoleType;
import com.kuaikan.role.game.api.enums.UserCouponSourceType;
import com.kuaikan.role.game.api.enums.UserFurniturePostMarkFlowSource;
import com.kuaikan.role.game.api.model.CourtyardModel;
import com.kuaikan.role.game.api.model.FurnitureDetailModel;
import com.kuaikan.role.game.api.model.FurnitureGroupModel;
import com.kuaikan.role.game.api.model.FurnitureMapPointModel;
import com.kuaikan.role.game.api.model.FurnitureModel;
import com.kuaikan.role.game.api.model.HomeEntryModel;
import com.kuaikan.role.game.api.model.HomeModel;
import com.kuaikan.role.game.api.model.HomeOpenPopupWindowModel;
import com.kuaikan.role.game.api.model.HomeOpenPopupWindowModel.OpenConditionModel;
import com.kuaikan.role.game.api.model.HomeOpenResultModel;
import com.kuaikan.role.game.api.model.HomePageConfigModel;
import com.kuaikan.role.game.api.model.HomeRedDotModel;
import com.kuaikan.role.game.api.model.HomeSettingModel;
import com.kuaikan.role.game.api.model.NameImageCountModel;
import com.kuaikan.role.game.api.model.PreviewTabModel;
import com.kuaikan.role.game.api.model.PrizeSimpleModel;
import com.kuaikan.role.game.api.model.RoleCostumeConfigModel;
import com.kuaikan.role.game.api.model.SimpleFurnitureActivityModel;
import com.kuaikan.role.game.api.model.UserFurnitureGroupModel;
import com.kuaikan.role.game.api.model.UserFurnitureModel;
import com.kuaikan.role.game.api.rpc.param.DisassembleFurnitureRpcParam;
import com.kuaikan.role.game.api.rpc.param.EffectiveFurnitureGroupIdUpdateParam;
import com.kuaikan.role.game.api.rpc.param.FurnitureDetailParam;
import com.kuaikan.role.game.api.rpc.param.FurnitureGroupDetailParam;
import com.kuaikan.role.game.api.rpc.param.FurnitureSetTabsParam;
import com.kuaikan.role.game.api.rpc.param.FurnitureStoreParam;
import com.kuaikan.role.game.api.rpc.param.HomeOpenParam;
import com.kuaikan.role.game.api.rpc.param.HomeOpenPopupWindowParam;
import com.kuaikan.role.game.api.rpc.param.HomePageConfigParam;
import com.kuaikan.role.game.api.rpc.param.PlaceFurnitureParam;
import com.kuaikan.role.game.api.rpc.param.UserFurnitureGroupParam;
import com.kuaikan.role.game.api.rpc.param.UserFurnitureListAddParam;
import com.kuaikan.role.game.api.rpc.param.UserFurnitureQueryParam;
import com.kuaikan.role.game.api.rpc.result.UserFurniturePostmarkModel;
import com.kuaikan.role.game.api.service.HomeService;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;
import com.kuaikan.role.game.component.ClaimPrizeComponent;
import com.kuaikan.role.game.component.FurnitureComponent;
import com.kuaikan.role.game.component.HomeComponent;
import com.kuaikan.role.game.component.RedDotComponent;
import com.kuaikan.role.game.component.RoleGroupComponent;
import com.kuaikan.role.game.component.SaComponent;
import com.kuaikan.role.game.component.UserFurnitureComponent;
import com.kuaikan.role.game.component.UserFurniturePostmarkComponent;
import com.kuaikan.role.game.repository.CabinRepository;
import com.kuaikan.role.game.repository.CostumeRepository;
import com.kuaikan.role.game.repository.CourtyardRepository;
import com.kuaikan.role.game.repository.CourtyardRoleGroupRelationRepository;
import com.kuaikan.role.game.repository.FurnitureActivityConfigRepository;
import com.kuaikan.role.game.repository.FurnitureActivityFurnitureGroupRelationRepository;
import com.kuaikan.role.game.repository.FurnitureGroupRelationRepository;
import com.kuaikan.role.game.repository.FurnitureGroupRepository;
import com.kuaikan.role.game.repository.FurnitureGroupRoleGroupRelationRepository;
import com.kuaikan.role.game.repository.FurnitureRepository;
import com.kuaikan.role.game.repository.FurnitureStoryRelationRepository;
import com.kuaikan.role.game.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.repository.RoleCostumeRelationRepository;
import com.kuaikan.role.game.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.repository.RoleGroupRepository;
import com.kuaikan.role.game.repository.RoleRepository;
import com.kuaikan.role.game.repository.SceneRepository;
import com.kuaikan.role.game.repository.StoryRepository;
import com.kuaikan.role.game.repository.UserCabinRepository;
import com.kuaikan.role.game.repository.UserFurnitureDecomposeOrderRepository;
import com.kuaikan.role.game.repository.UserFurnitureGroupRelationRepository;
import com.kuaikan.role.game.repository.UserFurnitureGroupRepository;
import com.kuaikan.role.game.repository.UserFurniturePostmarkFlowRepository;
import com.kuaikan.role.game.repository.UserFurniturePostmarkRepository;
import com.kuaikan.role.game.repository.UserFurnitureRepository;
import com.kuaikan.role.game.repository.UserHomeRepository;
import com.kuaikan.role.game.repository.UserPostmarkAssignRecordFlowRepository;
import com.kuaikan.role.game.repository.UserPostmarkAssignRecordRepository;
import com.kuaikan.role.game.repository.UserRoleGroupEmotionBondRepository;
import com.kuaikan.role.game.repository.UserRoleRepository;
import com.kuaikan.role.game.repository.UserSceneRepository;

@DubboService(version = "1.0", group = "role-game")
@Slf4j
public class HomeServiceImpl implements HomeService {

    @Resource
    private RoleGroupRepository roleGroupRepository;
    @Resource
    private FurnitureGroupRepository furnitureGroupRepository;
    @Resource
    private FurnitureRepository furnitureRepository;
    @Resource
    private FurnitureGroupRelationRepository furnitureGroupRelationRepository;
    @Resource
    private SceneRepository sceneRepository;
    @Resource
    private UserRoleGroupEmotionBondRepository userRoleGroupEmotionBondRepository;
    @Resource
    private UserHomeRepository userHomeRepository;
    @Resource
    private CabinRepository cabinRepository;
    @Resource
    private UserFurnitureComponent userFurnitureComponent;
    @Resource
    private FurnitureGroupRoleGroupRelationRepository furnitureGroupRoleGroupRelationRepository;
    @Resource
    private UserSceneRepository userSceneRepository;
    @Resource
    private UserCabinRepository userCabinRepository;
    @Resource
    private UserFurnitureRepository userFurnitureRepository;
    @Resource
    private UserFurniturePostmarkRepository userFurniturePostmarkRepository;
    @Resource
    private UserFurnitureGroupRelationRepository userFurnitureGroupRelationRepository;
    @Resource
    private RoleGroupComponent roleGroupComponent;
    @Resource
    private FurnitureStoryRelationRepository furnitureStoryRelationRepository;
    @Resource
    private StoryRepository storyRepository;
    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;
    @Resource
    private UserFurnitureDecomposeOrderRepository userFurnitureDecomposeOrderRepository;
    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;
    @Resource
    private HomeComponent homeComponent;
    @Resource
    private UserFurniturePostmarkComponent userFurniturePostmarkComponent;
    @Resource
    private FurnitureComponent furnitureComponent;
    @Resource
    private RedDotComponent redDotComponent;
    @Resource
    private CourtyardRepository courtyardRepository;
    @Resource
    private CourtyardRoleGroupRelationRepository courtyardRoleGroupRelationRepository;
    @Resource
    private RoleRepository roleRepository;
    @Resource
    private UserRoleRepository userRoleRepository;
    @Resource
    private CostumeRepository costumeRepository;
    @Resource
    private RoleCostumeRelationRepository roleCostumeRelationRepository;

    @Resource
    private UserFurniturePostmarkFlowRepository userFurniturePostmarkFlowRepository;
    @Resource
    private UserPostmarkAssignRecordRepository userPostmarkAssignRecordRepository;
    @Resource
    private UserPostmarkAssignRecordFlowRepository userPostmarkAssignRecordFlowRepository;
    @Resource
    private FurnitureActivityConfigRepository furnitureActivityConfigRepository;
    @Resource
    private FurnitureActivityFurnitureGroupRelationRepository furnitureActivityFurnitureGroupRelationRepository;
    @Resource
    private ClaimPrizeComponent claimPrizeComponent;
    @Resource
    private UserFurnitureGroupRepository userFurnitureGroupRepository;
    @Resource
    private SaComponent saComponent;

    @Override
    public RpcResult<HomeEntryModel> getHomeEntry() {
        return RpcResult.success(homeComponent.getHomeEntry());
    }

    @Override
    public RpcResult<HomeSettingModel> getHomeSetting(Integer roleGroupId) {
        return RpcResult.success(homeComponent.getHomeSetting(roleGroupId));
    }

    @Override
    public RpcResult<HomeOpenPopupWindowModel> openPopupWindow(HomeOpenPopupWindowParam param) {
        int userId = param.getUserId();
        int roleGroupId = param.getRoleGroupId();
        RoleGroup roleGroup = roleGroupRepository.queryByIdFromCache(roleGroupId);
        if (roleGroup == null) {
            log.error("home openPopupWindow roleGroup is null, roleGroupId:{}", roleGroupId);
            return RpcResult.result(RoleGameResponse.ROLE_GROUP_NOT_FOUND);
        }

        log.debug("roleGroup config json: {}", JsonUtils.writeValueAsString(roleGroup.getConfig()));
        Config config = roleGroup.getConfig();
        HomeInfo homeInfo = config.getHomeInfo();
        if (homeInfo == null) {
            log.warn("home openPopupWindow homeInfo is null, roleGroupId:{}", roleGroupId);
            return RpcResult.result(RoleGameResponse.HOME_NOT_FOUND);
        }
        log.debug("openPopupWindow config:{}, param:{}", config, param);
        log.debug("homeInfo:{}, json:{}", homeInfo, JsonUtils.writeValueAsString(homeInfo));
        List<Furniture> furnitures = furnitureRepository.queryByIds(homeInfo.getFurnitureIds());
        HomeOpenPopupWindowModel model = new HomeOpenPopupWindowModel();
        HomeOpenPopupWindowModel.RewardInfoModel rewardInfoModel = new HomeOpenPopupWindowModel.RewardInfoModel();
        List<NameImageCountModel> furnitureList = furnitures.stream()
                .map(furniture -> new NameImageCountModel(furniture.getName(), furniture.getConfig().getIcon(), 1))
                .collect(Collectors.toList());
        rewardInfoModel.setFurnitureList(furnitureList);
        model.setRewardInfo(rewardInfoModel);
        OpenConditionModel openConditionModel = new OpenConditionModel();
        model.setOpenCondition(openConditionModel);
        UserRoleGroupEmotionBond userRoleGroupEmotionBond = userRoleGroupEmotionBondRepository.queryByUserIdAndGroupIdFromDB(userId, roleGroupId);
        int currBondLevel = userRoleGroupEmotionBond == null ? 0 : userRoleGroupEmotionBond.getLevel();
        if (currBondLevel > 0) {
            boolean hasAllRole = roleGroupComponent.hasAllRoleByGroupId(userId, roleGroupId);
            if (!hasAllRole) {
                currBondLevel = 0;
            }
        }
        openConditionModel.setCurrBondLevel(currBondLevel);
        openConditionModel.setNeedBondLevel(homeInfo.getBondLevel());
        return RpcResult.success(model);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<HomeOpenResultModel> open(HomeOpenParam param) {
        int roleGroupId = param.getRoleGroupId();
        int userId = param.getUserId();
        RoleGroup roleGroup = roleGroupRepository.queryByIdFromDB(roleGroupId);
        if (roleGroup == null) {
            log.error("home open roleGroup is null, roleGroupId:{}", roleGroupId);
            return RpcResult.result(RoleGameResponse.ROLE_GROUP_NOT_FOUND);
        }
        boolean hasAllRole = roleGroupComponent.hasAllRoleByGroupId(userId, roleGroupId);
        if (!hasAllRole) {
            log.error("home open hasAllRole is false, roleGroupId:{}", roleGroupId);
            return RpcResult.result(RoleGameResponse.ROLE_GROUP_NOT_FOUND);
        }
        Config config = roleGroup.getConfig();
        HomeInfo homeInfo = config.getHomeInfo();
        if (homeInfo == null) {
            log.error("home open homeInfo is null, roleGroupId:{}", roleGroupId);
            return RpcResult.result(RoleGameResponse.HOME_NOT_FOUND);
        }
        log.info("home open homeInfo:{}", homeInfo);
        int bondLevel = homeInfo.getBondLevel();
        UserRoleGroupEmotionBond userRoleGroupEmotionBond = userRoleGroupEmotionBondRepository.queryByUserIdAndGroupIdFromDB(userId, roleGroupId);
        if (userRoleGroupEmotionBond == null || userRoleGroupEmotionBond.getLevel() < bondLevel) {
            log.error("home open bondLevel is not enough, roleGroupId:{}", roleGroupId);
            return RpcResult.result(RoleGameResponse.HOME_BOND_LEVEL_NOT_ENOUGH);
        }
        UserHome userHome = userHomeRepository.queryByUserIdAndGroupId(param.getUserId(), roleGroupId);
        if (userHome != null) {
            return RpcResult.result(RoleGameResponse.HOME_ALREADY_OPEN);
        }
        String orderId = String.valueOf(BufferedIdGenerator.getId());

        Scene effectiveScene = sceneRepository.queryDefaultScene(roleGroupId, SceneType.HOME.getCode());

        if (effectiveScene == null) {
            log.error("home open effectiveScene is null, roleGroupId:{}", roleGroupId);
            return RpcResult.result(RoleGameResponse.SCENE_NOT_FOUND);
        }
        Integer effectiveSceneId = effectiveScene.getId();
        Cabin defaultCabin = cabinRepository.queryDefaultByRoleGroupId(roleGroupId);
        if (defaultCabin == null) {
            log.error("home open defaultCabin is null, roleGroupId:{}", roleGroupId);
            return RpcResult.result(RoleGameResponse.CABIN_NOT_FOUND);
        }
        UserCabin userCabin = userCabinRepository.queryByUserIdAndCabinId(userId, defaultCabin.getId());
        if (userCabin == null) {
            userCabin = new UserCabin().setUserId(userId).setCabinId(defaultCabin.getId());
            userCabinRepository.insert(userCabin);
        }
        List<Integer> furnitureIds = homeInfo.getFurnitureIds();
        if (CollectionUtil.isNotEmpty(furnitureIds)) {
            UserFurnitureListAddParam userFurnitureListAddParam = new UserFurnitureListAddParam().setUserId(param.getUserId())
                    .setSource(FurnitureChargeSource.OPEN_HOME.getCode())
                    .setThirdId(orderId)
                    .setFurnitureInfos(furnitureIds.stream()
                            .map(furnitureId -> new UserFurnitureListAddParam.FurnitureInfo().setFurnitureId(furnitureId).setNum(1))
                            .collect(Collectors.toList()));
            userFurnitureComponent.addUserFurnitureList(userFurnitureListAddParam);
        }
        UserScene userScene = userSceneRepository.queryByUserIdSceneId(userId, effectiveSceneId);
        if (userScene == null) {
            userSceneRepository.save(userId, effectiveSceneId, false);
        }
        Integer effectiveFurnitureGroupId = null;
        // 取第一个配置的家具对应的家具组为用户默认的家具组
        if (CollectionUtil.isNotEmpty(furnitureIds)) {
            Map<Integer, FurnitureGroupRelation> furnitureGroupRelationMap = furnitureGroupRelationRepository.queryFurnitureMapByFurnitureIds(furnitureIds);
            for (Integer furntureId : furnitureIds) {
                FurnitureGroupRelation furnitureGroupRelation = furnitureGroupRelationMap.get(furntureId);
                if (furnitureGroupRelation != null) {
                    effectiveFurnitureGroupId = furnitureGroupRelation.getFurnitureGroupId();
                    break;
                }
            }
            log.debug("home open effectiveFurnitureGroupId:{}, furnitureIds:{}, furnitureGroupRelationMap:{},userId:{},roleGroupId:{}",
                    effectiveFurnitureGroupId, furnitureIds, furnitureGroupRelationMap, userId, roleGroupId);
        }
        if (effectiveFurnitureGroupId == null) {
            FurnitureGroup furnitureGroup = furnitureGroupRepository.queryDefault(roleGroupId);
            if (furnitureGroup == null) {
                log.error("home open furnitureGroup is null, roleGroupId:{}", roleGroupId);
                return RpcResult.result(RoleGameResponse.FURNITURE_GROUP_NOT_FOUND);
            }
            effectiveFurnitureGroupId = furnitureGroup.getId();
        }
        userHome = new UserHome().setUserId(userId)
                .setRoleGroupId(roleGroupId)
                .setEffectiveSceneId(effectiveSceneId)
                .setEffectiveFurnitureGroupId(effectiveFurnitureGroupId)
                .setEffectiveCabinId(defaultCabin.getId());
        userHomeRepository.insert(userHome);
        log.debug("open homeInfo:{}, userHome:{}", homeInfo, userHome);
        List<Furniture> furnitures = furnitureRepository.queryByIds(furnitureIds);
        HomeOpenResultModel model = new HomeOpenResultModel();
        HomeOpenResultModel.RewardInfoModel rewardInfoModel = new HomeOpenResultModel.RewardInfoModel();
        List<NameImageCountModel> furnitureList = furnitures.stream()
                .map(furniture -> new NameImageCountModel(furniture.getName(), furniture.getConfig().getImage(), 1))
                .collect(Collectors.toList());
        rewardInfoModel.setFurnitureList(furnitureList);
        model.setRewardInfo(rewardInfoModel);
        return RpcResult.success(model);
    }

    @Override
    public RpcResult<FurnitureDetailModel> getFurnitureDetail(FurnitureDetailParam param) {
        int userId = param.getUserId();
        int furnitureId = param.getFurnitureId();

        UserFurnitureCount userFurnitureCount = userFurnitureRepository.queryUserFurnitureCountByUserIdAndFurnitureId(userId, furnitureId);
        Furniture furniture = furnitureRepository.queryById(furnitureId);
        if (furniture == null) {
            log.error("getFurnitureDetail furniture is null, furnitureId:{}", furnitureId);
            return RpcResult.result(RoleGameResponse.FURNITURE_NOT_FOUND);
        }
        List<FurnitureDetailModel.FurnitureAcquisitionWayModel> acquisitionWayModels = getAcquisitionWayModels(furniture);
        int arrangeCount = userFurnitureComponent.getUserFurnitureArrangeCount(userId, furnitureId);
        FurnitureDetailModel model = new FurnitureDetailModel();
        model.setArrangeCount(arrangeCount);
        model.setAvailableCount(userFurnitureCount == null ? -1 : (userFurnitureCount.getCount() - arrangeCount));
        model.setOwnedCount(userFurnitureCount == null ? -1 : userFurnitureCount.getCount());
        model.setName(furniture.getName());
        model.setId(furnitureId);
        model.setImage(furniture.getConfig().getIcon());
        Furniture.Config config = furniture.getConfig();
        if (config != null) {
            Furniture.FurnitureAcquisitionWayInfoModel acquisitionWayInfo = config.getAcquisitionWayInfo();
            if (acquisitionWayInfo != null) {
                model.setAcquisitionWayInfo(new FurnitureDetailModel.FurnitureAcquisitionWayInfoModel().setAcquisitionWays(acquisitionWayModels)
                        .setCornerMark(acquisitionWayInfo.getCornerMark()));
            }
        }
        model.setRelatedActivityWayInfo(getActivityWayInfoModel(furnitureId));
        return RpcResult.success(model);
    }

    private FurnitureDetailModel.FurnitureActivityWayInfoModel getActivityWayInfoModel(int furnitureId) {
        FurnitureDetailModel.FurnitureActivityWayInfoModel activityWayInfoModel = new FurnitureDetailModel.FurnitureActivityWayInfoModel();
        List<FurnitureActivityConfig> onlineActivityList = furnitureActivityConfigRepository.queryOnlineActivityList();
        if (CollectionUtils.isEmpty(onlineActivityList)) {
            return activityWayInfoModel;
        }
        FurnitureGroupRelation furnitureGroupRelation = furnitureGroupRelationRepository.queryByFurnitureId(furnitureId);
        if (furnitureGroupRelation == null) {
            return activityWayInfoModel;
        }
        List<FurnitureActivityFurnitureGroupRelation> furnitureGroupRelations = furnitureActivityFurnitureGroupRelationRepository.queryByFurnitureGroupId(
                furnitureGroupRelation.getFurnitureGroupId());
        if (CollectionUtils.isEmpty(furnitureGroupRelations)) {
            return activityWayInfoModel;
        }
        List<Integer> relatedActivityIds = furnitureGroupRelations.stream()
                .map(FurnitureActivityFurnitureGroupRelation::getActivityId)
                .collect(Collectors.toList());
        List<SimpleFurnitureActivityModel> relatedActivityWays = onlineActivityList.stream()
                .filter(activity -> relatedActivityIds.contains(activity.getId()))
                .map(SimpleFurnitureActivityModel::of)
                .collect(Collectors.toList());
        activityWayInfoModel.setRelatedActivityWays(relatedActivityWays);
        return activityWayInfoModel;
    }

    @Override
    public RpcResult<List<FurnitureGroupModel>> getFurnitureSetTabs(FurnitureSetTabsParam param) {
        Integer roleGroupId = param.getRoleGroupId();
        Integer furnitureGroupId = param.getFurnitureGroupId();
        if (ObjectUtil.isEmpty(roleGroupId)) {
            return RpcResult.result(RoleGameResponse.ROLE_ID_NOT_EMPTY.getCode(), RoleGameResponse.ROLE_ID_NOT_EMPTY.getMessage());
        }
        if (ObjectUtil.isEmpty(furnitureGroupId)) {
            return RpcResult.result(RoleGameResponse.SET_ID_CANNOT_BE_EMPTY.getCode(), RoleGameResponse.SET_ID_CANNOT_BE_EMPTY.getMessage());
        }
        List<FurnitureGroupRoleGroupRelation> furnitureGroupRoleGroupRelations = furnitureGroupRoleGroupRelationRepository.queryByRoleGroupId(roleGroupId);
        Set<Integer> furnitureGroupIds = furnitureGroupRoleGroupRelations.stream()
                .map(FurnitureGroupRoleGroupRelation::getFurnitureGroupId)
                .collect(Collectors.toSet());
        Map<Integer, List<FurnitureGroupRelation>> group2FurnitureIds = furnitureGroupRelationRepository.queryByGroupIds(furnitureGroupIds);
        Set<Integer> furnitureIds = group2FurnitureIds.values()
                .stream()
                .flatMap(List::stream)
                .map(FurnitureGroupRelation::getFurnitureId)
                .collect(Collectors.toSet());
        List<FurnitureGroup> furnitureGroups = furnitureGroupRepository.getByIds(furnitureGroupIds);
        furnitureGroups = furnitureGroups.stream()
                .filter(furnitureGroup -> Objects.equals(furnitureGroup.getStatus(), FurnitureGroupStatus.UP_FOR_LISTING.getCode()))
                .collect(Collectors.toList());
        List<FurnitureModel> furnitureModelList = furnitureComponent.getFurnitureModelList(roleGroupId, furnitureIds);

        furnitureGroups.sort(Comparator.comparing(FurnitureGroup::getOrderNum, Comparator.nullsLast(Comparator.naturalOrder())));
        List<FurnitureGroupModel> result = new ArrayList<>();

        // 添加预告tab的逻辑
        Map<Integer, PreviewTabModel> previewModelMap = new HashMap<>();
        // 1. 计算非空套组数量
        long nonEmptyGroupCount = furnitureGroups.stream().filter(group -> {
            List<FurnitureGroupRelation> relations = group2FurnitureIds.get(group.getId());
            return relations != null && !relations.isEmpty();
        }).count();
        // 只在非空套组数量小于等于2时继续处理
        if (nonEmptyGroupCount <= 2) {
            // 2. 获取所有套组的限定关系
            Map<Integer, List<FurnitureGroupRoleGroupRelation>> groupLimitedRelationsMap = furnitureGroupRoleGroupRelationRepository.getByFurnitureGroupIds(
                            furnitureGroups.stream().map(FurnitureGroup::getId).collect(Collectors.toSet()))
                    .stream()
                    .collect(Collectors.groupingBy(FurnitureGroupRoleGroupRelation::getFurnitureGroupId));
            // 检查是否存在限定套组且有家具
            boolean hasLimitedGroupWithFurniture = furnitureGroups.stream().anyMatch(group -> {
                List<FurnitureGroupRoleGroupRelation> groupRelations = groupLimitedRelationsMap.get(group.getId());
                boolean isCurrentGroupLimited = groupRelations != null && groupRelations.size() == 1 && groupRelations.get(0)
                        .getRoleGroupId()
                        .equals(roleGroupId);
                if (!isCurrentGroupLimited) {
                    return false;
                }
                List<FurnitureGroupRelation> relations = group2FurnitureIds.get(group.getId());
                return relations != null && !relations.isEmpty();
            });
            // 如果不存在限定套组且有家具，则继续处理预告tab
            if (!hasLimitedGroupWithFurniture) {
                List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupIdFromCache(roleGroupId);
                if (!roleGroupRelations.isEmpty()) {
                    // 获取角色名称
                    List<Integer> roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toList());
                    Map<Integer, Role> roleMap = roleRepository.queryByIdsFromCache(roleIds);
                    List<String> roleNames = roleIds.stream().map(roleMap::get).filter(Objects::nonNull).map(Role::getName).collect(Collectors.toList());
                    // 3. 查找限定套组且无家具的，设置为预告tab
                    for (FurnitureGroup group : furnitureGroups) {
                        List<FurnitureGroupRoleGroupRelation> groupRelations = groupLimitedRelationsMap.get(group.getId());
                        boolean isCurrentGroupLimited = groupRelations != null && groupRelations.size() == 1 && groupRelations.get(0)
                                .getRoleGroupId()
                                .equals(roleGroupId);
                        // 如果不是限定套组，跳过
                        if (!isCurrentGroupLimited) {
                            continue;
                        }
                        // 检查是否为空套组
                        List<FurnitureGroupRelation> relations = group2FurnitureIds.get(group.getId());
                        if (relations != null && !relations.isEmpty()) {
                            continue;
                        }
                        // 找到符合条件的套组，创建预览tab
                        PreviewTabModel previewModel = new PreviewTabModel();
                        previewModel.setFurnitureGroupId(group.getId());
                        previewModel.setPreviewText(String.format("%s和%s的专属家具正在制作中，敬请期待", roleNames.get(0), roleNames.get(1)));
                        previewModelMap.put(group.getId(), previewModel);
                    }
                }
            }
        }

        // 处理所有家具套组
        List<FurnitureGroupModel> normalTabs = new ArrayList<>();
        List<FurnitureGroupModel> previewTabs = new ArrayList<>();
        // 先处理所有家具套组
        for (FurnitureGroup furnitureGroup : furnitureGroups) {
            // 获取该套组下的家具ID列表
            Set<Integer> groupFurnitureIds = group2FurnitureIds.getOrDefault(furnitureGroup.getId(), new ArrayList<>())
                    .stream()
                    .map(FurnitureGroupRelation::getFurnitureId)
                    .collect(Collectors.toSet());
            // 如果是预告tab，加入预告tab列表
            PreviewTabModel previewModel = previewModelMap.get(furnitureGroup.getId());
            if (previewModel != null) {
                FurnitureGroupModel furnitureGroupModel = FurnitureGroupModel.valueOf(furnitureGroup);
                furnitureGroupModel.setIsPreviewTab(true);
                furnitureGroupModel.setPreviewText(previewModel.getPreviewText());
                previewTabs.add(furnitureGroupModel);
                continue;
            }
            // 如果不是预告tab，并且没有家具，则跳过
            if (groupFurnitureIds.isEmpty()) {
                continue;
            }
            // 处理有家具的普通tab
            FurnitureGroupModel furnitureGroupModel = FurnitureGroupModel.valueOf(furnitureGroup);
            List<FurnitureModel> furnitureModels = furnitureModelList.stream()
                    .filter(furnitureModel -> groupFurnitureIds.contains(furnitureModel.getId()))
                    .collect(Collectors.toList());
            furnitureGroupModel.setFurnitureModels(furnitureModels);
            normalTabs.add(furnitureGroupModel);
        }

        // 先添加普通tab，再添加预告tab
        result.addAll(normalTabs);
        result.addAll(previewTabs);
        return RpcResult.success(result);
    }

    /**
     * 后台配置了让系统显示所有处于“已上架”状态的家具。(家具)
     *
     * @return
     */
    @Override
    public RpcResult<List<FurnitureDetailModel>> getFurnitureDetailModels() {
        List<Furniture> furnitures = furnitureRepository.getFurnitureDetailModels();
        List<FurnitureDetailModel> furnitureDetailModels = furnitures.stream()
                .map(furniture -> FurnitureDetailModel.of(furniture))
                .collect(Collectors.toList());
        return RpcResult.success(furnitureDetailModels);
    }

    @Override
    public RpcResult<UserFurnitureGroupModel> getUserFurnitureGroupModel(UserFurnitureGroupParam userFurnitureGroupParam) {
        Integer userId = userFurnitureGroupParam.getUserId();
        Integer furnitureGroupId = userFurnitureGroupParam.getFurnitureGroupId();
        Integer roleGroupId = userFurnitureGroupParam.getRoleGroupId();
        if (ObjectUtil.isEmpty(userId) || ObjectUtil.isEmpty(furnitureGroupId)) {
            log.error("getUserFurnitureGroupModel userId or furnitureGroupId is null, userId:{},furnitureGroupId:{}", userId, furnitureGroupId);
            return RpcResult.success(null);
        }

        List<FurnitureGroupRelation> furnitureGroupRelations = furnitureGroupRelationRepository.queryByGroupId(furnitureGroupId);
        List<Integer> furnitureIdsByRelation = furnitureGroupRelations.stream()
                .map(furnitureGroupRelation -> furnitureGroupRelation.getFurnitureId())
                .collect(Collectors.toList());
        List<Furniture> furnitures = furnitureRepository.queryByIds(furnitureIdsByRelation);
        List<Integer> furnitureIds = furnitures.stream().map(furniture -> furniture.getId()).collect(Collectors.toList());
        UserFurnitureGroupModel model = new UserFurnitureGroupModel();
        model.setUserId(userId);
        model.setFurnitureGroupId(furnitureGroupId);
        List<FurnitureMapPointModel> furnitureMapPointModels = furnitures.stream()
                .map(furniture -> FurnitureMapPointModel.of(furniture.getId(), furniture.getConfig() == null ? null : furniture.getConfig().getBaseGrid()))
                .collect(Collectors.toList());
        model.setFurnitureMapPointModels(furnitureMapPointModels);
        // 查询家具套组级别，此角色是否有摆放
        List<UserFurnitureGroupRelation> userFurnitureGroupRelationsWithRoleGroupId = userFurnitureGroupRelationRepository.queryByUserIdAndFurnitureGroupIdAndRoleGroupId(
                userId, furnitureGroupId, roleGroupId);
        Map<Integer, List<UserFurnitureGroupRelation>> userFurnitureGroupRelationMapWithRoleGroupId = userFurnitureGroupRelationsWithRoleGroupId.stream()
                .collect(Collectors.groupingBy(UserFurnitureGroupRelation::getFurnitureId));
        // 查询家具套组级别，无关角色组
        List<UserFurnitureGroupRelation> userFurnitureGroupRelations = userFurnitureGroupRelationRepository.queryByUserIdAndFurnitureGroupId(userId,
                furnitureGroupId);
        Map<Integer, List<UserFurnitureGroupRelation>> userFurnitureGroupRelationMap = userFurnitureGroupRelations.stream()
                .collect(Collectors.groupingBy(UserFurnitureGroupRelation::getFurnitureId));
        List<UserFurnitureCount> userFurtureCounts = userFurnitureRepository.queryUserFurnitureCountByUserIdAndFurnitureIds(userId, furnitureIds);
        if (CollectionUtil.isNotEmpty(userFurtureCounts)) {
            List<UserFurnitureModel> userFurnitureModels = new ArrayList<>();
            for (UserFurnitureCount userFurnitureCount : userFurtureCounts) {
                List<UserFurnitureGroupRelation> userFurnitureGroupRelationsByFurnitureId = userFurnitureGroupRelationMap.get(
                        userFurnitureCount.getFurnitureId());
                List<UserFurnitureGroupRelation> userFurnitureGroupRelationsByFurnitureIdWithRoleGroupId = userFurnitureGroupRelationMapWithRoleGroupId.get(
                        userFurnitureCount.getFurnitureId());
                boolean hasArranged = CollectionUtils.isNotEmpty(userFurnitureGroupRelationsByFurnitureIdWithRoleGroupId);
                int arrangedCount = CollectionUtils.isEmpty(userFurnitureGroupRelationsByFurnitureId) ? 0 : userFurnitureGroupRelationsByFurnitureId.size();
                UserFurnitureModel userFurnitureModel = UserFurnitureModel.of(userFurnitureCount, arrangedCount, hasArranged);
                userFurnitureModels.add(userFurnitureModel);
                log.debug(
                        "getUserFurnitureGroupModel userId:{}, furnitureGroupId:{},furnitureId:{},arrangedCount:{},hasArranged:{},userFurnitureGroupRelationsByFurnitureId:{},userFurnitureGroupRelationsByFurnitureIdWithRoleGroupId:{}",
                        userId, furnitureGroupId, userFurnitureCount.getFurnitureId(), arrangedCount, hasArranged, userFurnitureGroupRelationsByFurnitureId,
                        userFurnitureGroupRelationsByFurnitureIdWithRoleGroupId);
            }
            model.setUserFurnitureList(userFurnitureModels);
        }
        log.info(
                "getUserFurnitureGroupModel model:{},userFurtureCounts:{},userId:{},furnitureGroupId:{},furnitureIds:{},userFurnitureGroupRelationsWithRoleGroupId:{},userFurnitureGroupRelationMapWithRoleGroupId:{}",
                model, userFurtureCounts, userId, furnitureGroupId, furnitureIds, userFurnitureGroupRelationsWithRoleGroupId,
                userFurnitureGroupRelationMapWithRoleGroupId);
        return RpcResult.success(model);
    }

    private List<FurnitureDetailModel.FurnitureAcquisitionWayModel> getAcquisitionWayModels(Furniture furniture) {
        FurnitureDetailModel.FurnitureAcquisitionWayInfoModel acquisitionWayInfoModel = new FurnitureDetailModel.FurnitureAcquisitionWayInfoModel();

        List<FurnitureDetailModel.FurnitureAcquisitionWayModel> acquisitionWayModels = new ArrayList<>();
        Furniture.Config furnitureConfig = furniture.getConfig();
        if (ObjectUtil.isNotEmpty(furnitureConfig)) {
            Furniture.FurnitureAcquisitionWayInfoModel acquisitionWays = furnitureConfig.getAcquisitionWayInfo();
            if (acquisitionWays != null) {
                acquisitionWayInfoModel.setCornerMark(acquisitionWays.getCornerMark());
                long now = System.currentTimeMillis();
                if (CollectionUtil.isNotEmpty(acquisitionWays.getAcquisitionWays())) {
                    for (Furniture.FurnitureAcquisitionWay acquisitionWay : acquisitionWays.getAcquisitionWays()) {
                        if (acquisitionWay.getType() != FurnitureAcquisitionWayType.QUEST.getCode()) {
                            continue;
                        }
                        if (acquisitionWay.getStartEffectiveTime() != null
                                && acquisitionWay.getStartEffectiveTime() != 0L
                                && acquisitionWay.getStartEffectiveTime() > now) {
                            continue;
                        }
                        if (acquisitionWay.getEndEffectiveTime() != null
                                && acquisitionWay.getEndEffectiveTime() != 0L
                                && acquisitionWay.getEndEffectiveTime() < now) {
                            continue;
                        }
                        FurnitureDetailModel.FurnitureAcquisitionWayModel acquisitionWayModel = FurnitureDetailModel.FurnitureAcquisitionWayModel.of(
                                acquisitionWay);
                        acquisitionWayModels.add(acquisitionWayModel);
                    }
                }
            }
        }
        acquisitionWayInfoModel.setAcquisitionWays(acquisitionWayModels);
        return acquisitionWayModels;
    }

    private FurnitureGroupModel getFurnitureGroupById(Integer furnitureGroupId) {
        List<FurnitureGroupRelation> furnitureGroupRelations = furnitureGroupRelationRepository.queryByGroupId(furnitureGroupId);
        if (CollectionUtil.isEmpty(furnitureGroupRelations)) {
            log.warn("getFurnitureGroupById furnitureGroupRelations is null, furnitureGroupId:{}", furnitureGroupId);
            return null;
        }
        FurnitureGroupModel model = new FurnitureGroupModel();
        FurnitureGroup furnitureGroup = furnitureGroupRepository.queryById(furnitureGroupId);
        model.setId(furnitureGroupId);
        model.setName(furnitureGroup.getName());
        model.setCollectAllPrizeId(furnitureGroup.getConfig() != null ? furnitureGroup.getConfig().getCollectAllPrizeId() : null);
        model.setThumbnail(furnitureGroup.getConfig() != null ? furnitureGroup.getConfig().getThumbnail() : null);
        model.setAtlasFileInfo(getFurnitureGroupAtlasByGroupId(furnitureGroupId));
        log.debug("getFurnitureGroupById model:{},furnitureGroupId:{}", model, furnitureGroupId);
        return model;
    }

    public RpcResult<FurnitureGroupModel.AtlasFileInfoModel> getFurnitureGroupAtlasByIdRpc(Integer furnitureGroupId) {
        return RpcResult.success(getFurnitureGroupAtlasByGroupId(furnitureGroupId));
    }

    @Override
    public RpcResult<PrizeSimpleModel> claimGroupCollectAllPrize(int userId, int furnitureGroupId) {
        PrizeSimpleModel prizeSimpleModel = userFurnitureComponent.assignCollectAllPrize(userId, furnitureGroupId);
        if (prizeSimpleModel == null) {
            log.warn("claimGroupCollectAllPrize failed userId:{},furnitureGroupId:{}", userId, furnitureGroupId);
            return RpcResult.result(ResponseCodeMsg.SYSTEM_ERROR);
        }
        return RpcResult.success(prizeSimpleModel);
    }

    public FurnitureGroupModel.AtlasFileInfoModel getFurnitureGroupAtlasByGroupId(Integer furnitureGroupId) {
        if (Objects.isNull(furnitureGroupId)) {
            return null;
        }

        FurnitureGroup furnitureGroup = furnitureGroupRepository.queryById(furnitureGroupId);
        if (Objects.isNull(furnitureGroup)) {
            return null;
        }

        if (furnitureGroup.getConfig() != null && Objects.nonNull(furnitureGroup.getConfig().getAtlasFileInfo())) {
            return FurnitureGroupModel.AtlasFileInfoModel.ofAtlas(furnitureGroup.getConfig().getAtlasFileInfo());
        }

        return null;
    }

    @Override
    public RpcResult<FurnitureGroupModel> getFurnitureGroupDetail(FurnitureGroupDetailParam param) {
        FurnitureGroup furnitureGroup = furnitureGroupRepository.queryByIdFromCache(param.getFurnitureGroupId());
        if (furnitureGroup == null) {
            log.error("getFurnitureGroupDetail furnitureGroup is null, furnitureGroupId:{}", param.getFurnitureGroupId());
            return RpcResult.result(RoleGameResponse.FURNITURE_GROUP_NOT_FOUND);
        }
        List<FurnitureGroupRelation> furnitureGroupRelations = furnitureGroupRelationRepository.queryByGroupId(param.getFurnitureGroupId());
        if (CollectionUtils.isEmpty(furnitureGroupRelations)) {
            log.warn("getFurnitureGroupDetail furnitureGroupRelations is empty, furnitureGroupId:{}", param.getFurnitureGroupId());
            return RpcResult.result(RoleGameResponse.FURNITURE_GROUP_NOT_FOUND);
        }
        List<Integer> furnitureIds = furnitureGroupRelations.stream().map(FurnitureGroupRelation::getFurnitureId).collect(Collectors.toList());
        List<FurnitureModel> furnitureModelList = furnitureComponent.getFurnitureModelList(param.getRoleGroupId(), furnitureIds);
        // 从userFurniture中判断这些家具是否都存在了
        List<UserFurniture> userFurnitureList = userFurnitureRepository.queryByUserIdAndFurnitureIds(param.getUserId(), furnitureIds);
        Map<Integer, Long> collectFurnitureNumMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userFurnitureList)) {
            collectFurnitureNumMap = userFurnitureList.stream().collect(Collectors.groupingBy(UserFurniture::getFurnitureId, Collectors.counting()));
        }
        List<UserFurnitureGroupRelation> relations = userFurnitureGroupRelationRepository.queryByUserId(param.getUserId());

        FurnitureGroupModel furnitureGroupModel = FurnitureGroupModel.valueOf(furnitureGroup);
        if (CollectionUtils.isEmpty(furnitureGroupRelations)) {
            log.debug("过滤掉无家具的家具组, furnitureGroupId:{}", furnitureGroup.getId());
            return RpcResult.success(furnitureGroupModel);
        }
        UserFurnitureGroup userFurnitureGroup = userFurnitureGroupRepository.queryByUserIdAndFurnitureGroupId(param.getUserId(), param.getFurnitureGroupId());
        Boolean isAssignCollectAllPrize = Optional.ofNullable(userFurnitureGroup)
                .map(UserFurnitureGroup::getConfig)
                .map(UserFurnitureGroup.Config::isAssignCollectAllPrize)
                .orElse(false);
        int totalFurnitureNum = furnitureGroupRelations.size();
        furnitureGroupModel.setTotalFurnitureNum(totalFurnitureNum);
        Integer collectFurnitureNum = collectFurnitureNumMap.size();
        furnitureGroupModel.setCollectFurnitureNum(collectFurnitureNum);
        Integer collectAllPrizeId = furnitureGroup.getConfig().getCollectAllPrizeId();
        if (collectAllPrizeId != null && collectAllPrizeId > 0) {
            PrizeSimpleModel prizeSimpleModel = claimPrizeComponent.getPrizeSimpleModel(collectAllPrizeId);
            furnitureGroupModel.setCollectAllPrize(prizeSimpleModel);
            furnitureGroupModel.setCollectAll(totalFurnitureNum == collectFurnitureNum);
            furnitureGroupModel.setCollectAllPrizeReceive(isAssignCollectAllPrize);
        }
        List<UserFurnitureModel> userFurnitureModels = getUserFurnitureModelsByUserIdAndFurnitureIds(param.getUserId(), furnitureIds, relations);
        furnitureGroupModel.setFurnitureModels(furnitureModelList);
        furnitureGroupModel.setUserFurnitureModels(userFurnitureModels);
        return RpcResult.success(furnitureGroupModel);
    }

    public List<UserFurnitureModel> getUserFurnitureModelsByUserIdAndFurnitureIds(Integer userId, List<Integer> furnitureIds,
                                                                                  List<UserFurnitureGroupRelation> userFurnitureGroupRelations) {
        // 查询家具套组级别，无关角色组
        Map<Integer, List<UserFurnitureGroupRelation>> userFurnitureGroupRelationMap = userFurnitureGroupRelations.stream()
                .collect(Collectors.groupingBy(UserFurnitureGroupRelation::getFurnitureId));
        List<UserFurnitureCount> userFurnitureCountList = userFurnitureRepository.queryUserFurnitureCountByUserIdAndFurnitureIds(userId, furnitureIds);
        List<UserFurnitureModel> userFurnitureModels = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(userFurnitureCountList)) {
            for (UserFurnitureCount userFurnitureCount : userFurnitureCountList) {
                List<UserFurnitureGroupRelation> userFurnitureGroupRelationsByFurnitureId = userFurnitureGroupRelationMap.get(
                        userFurnitureCount.getFurnitureId());
                int arrangedCount = CollectionUtils.isEmpty(userFurnitureGroupRelationsByFurnitureId) ? 0 : userFurnitureGroupRelationsByFurnitureId.size();
                UserFurnitureModel userFurnitureModel = UserFurnitureModel.of(userFurnitureCount, arrangedCount, false);
                userFurnitureModels.add(userFurnitureModel);
            }
        }
        return userFurnitureModels;
    }
    

    @Override
    public RpcResult<UserFurniturePostmarkModel> getUserFurniturePostmarkAccount(int userId) {
        // 两个余额来源（永久 + 限时)
        UserFurniturePostmark userFurniturePostMark = userFurniturePostmarkRepository.queryByUserId(userId);
        if (ObjectUtil.isEmpty(userFurniturePostMark)) {
            userFurniturePostMark = new UserFurniturePostmark().setUserId(userId).setBalance(0);
            userFurniturePostmarkRepository.insert(userFurniturePostMark);
        }
        // 查询有效期内的邮戳数量
        long now = System.currentTimeMillis();
        List<UserPostmarkAssignRecord> userPostmarkAssignRecords = userPostmarkAssignRecordRepository.queryByUserId(userId);
        int totalNum = userPostmarkAssignRecords.stream().filter(record -> record.getExpiredAt() > now).mapToInt(UserPostmarkAssignRecord::getRemainNum).sum();
        UserFurniturePostmarkModel model = new UserFurniturePostmarkModel();
        model.setBalance(userFurniturePostMark.getBalance() + totalNum);
        return RpcResult.success(model);
    }

    @Transactional
    public RpcResult<Void> deductUserFurniturePostmark(int userId, int num, String orderId) {
        log.info("deductUserFurniturePostmark called with userId={}, num={}", userId, num);

        if (num <= 0) {
            log.warn("deductUserFurniturePostmark param invalid, userId={}, num={}", userId, num);
            return RpcResult.result(ResponseCodeMsg.INVALID_PARAM.getCode(), "扣减数量必须大于0");
        }
        long now = System.currentTimeMillis();

        List<UserPostmarkAssignRecord> assignRecords = userPostmarkAssignRecordRepository.queryValidRecordsByUserId(userId, now);
        // 优先快过期的
        assignRecords.sort(Comparator.comparingLong(UserPostmarkAssignRecord::getExpiredAt));
        log.info("deductUserFurniturePostmark valid assign records size={}, userId={}", assignRecords.size(), userId);

        int remain = num;
        // 遍历限时记录进行扣减
        for (UserPostmarkAssignRecord record : assignRecords) {
            if (remain == 0) {
                break;
            }

            int available = record.getNum();
            int deduct = Math.min(available, remain);
            if (deduct > 0) {
                // 更新该记录的剩余数量
                int affectedRows = userPostmarkAssignRecordRepository.updateNumById(userId, record.getId(), available, available - deduct);
                log.info("deductUserFurniturePostmark try deduct assignRecordId={}, deduct={}, success={}, remain={}", record.getId(), deduct, affectedRows > 0,
                        remain - deduct);
                if (affectedRows <= 0) {
                    log.warn("deductUserFurniturePostmark failed optimistic lock, userId={}, recordId={}, oldNum={}, deduct={}", userId, record.getId(),
                            available, deduct);
                    return RpcResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "扣减有效期邮戳失败");
                }
                UserPostmarkAssignRecordFlow userPostmarkAssignRecordFlow = new UserPostmarkAssignRecordFlow();
                userPostmarkAssignRecordFlow.setUserId(userId);
                userPostmarkAssignRecordFlow.setCouponId(record.getCouponId());
                userPostmarkAssignRecordFlow.setNum(deduct);
                userPostmarkAssignRecordFlow.setBid(BufferedIdGenerator.getId());
                userPostmarkAssignRecordFlow.setSource(UserCouponSourceType.MALL_CONSUME.getCode());
                userPostmarkAssignRecordFlow.setThirdId(orderId);
                userPostmarkAssignRecordFlow.setType(FlowType.SUBTRACT.getCode());
                userPostmarkAssignRecordFlowRepository.insert(userPostmarkAssignRecordFlow);
                remain -= deduct;
            }
        }

        // 如果还有剩余，尝试从永久余额中扣减
        if (remain > 0) {
            UserFurniturePostmark postmark = userFurniturePostmarkRepository.queryByUserId(userId);
            if (postmark == null || postmark.getBalance() < remain) {
                log.warn("deductUserFurniturePostmark insufficient total balance, userId={}, remain={}, permanentBalance={}", userId, remain,
                        postmark != null ? postmark.getBalance() : null);
                return RpcResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "可用邮戳数量不足");
            }

            int newBalance = postmark.getBalance() - remain;
            int affectedRows = userFurniturePostmarkRepository.updateBalance(userId, newBalance, postmark.getBalance());
            log.info("deductUserFurniturePostmark deduct permanent, userId={}, deduct={}, oldBalance={}, newBalance={}, success={}", userId, remain,
                    postmark.getBalance(), newBalance, affectedRows > 0);

            if (affectedRows <= 0) {
                log.error("deductUserFurniturePostmark failed to deduct permanent balance, userId={}, remain={}", userId, remain);
                return RpcResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "扣减永久邮戳失败");
            }
            // 扣减流水
            UserFurniturePostmarkFlow userFurniturePostmarkFlow = new UserFurniturePostmarkFlow();
            userFurniturePostmarkFlow.setUserId(userId);
            userFurniturePostmarkFlow.setNum(remain);
            userFurniturePostmarkFlow.setBeforeBalance(postmark.getBalance());
            userFurniturePostmarkFlow.setAfterBalance(newBalance);
            userFurniturePostmarkFlow.setBid(BufferedIdGenerator.getId());
            userFurniturePostmarkFlow.setSource(UserCouponSourceType.MALL_CONSUME.getCode());
            userFurniturePostmarkFlow.setThirdId(orderId);
            userFurniturePostmarkFlow.setType(FlowType.SUBTRACT.getCode());
            userFurniturePostmarkFlowRepository.insert(userFurniturePostmarkFlow);
        }

        log.info("deductUserFurniturePostmark success, userId={}, totalDeduct={}", userId, num);

        return RpcResult.success();
    }

    @Override
    public RpcResult<List<UserFurnitureModel>> getUserFurnitureModelsByUserIdAndFurnitureIds(UserFurnitureQueryParam userFurnitureQueryParam) {
        Integer userId = userFurnitureQueryParam.getUserId();
        List<Integer> furnitureIds = userFurnitureQueryParam.getFurnitureIds();
        if (ObjectUtil.isEmpty(userId) || CollectionUtil.isEmpty(furnitureIds)) {
            return RpcResult.success(null);
        }
        List<UserFurnitureGroupRelation> userFurnitureGroupRelations = userFurnitureGroupRelationRepository.queryByUserIdAndFurnitureIds(userId, furnitureIds);
        // 查询家具套组级别，无关角色组
        Map<Integer, List<UserFurnitureGroupRelation>> userFurnitureGroupRelationMap = userFurnitureGroupRelations.stream()
                .collect(Collectors.groupingBy(UserFurnitureGroupRelation::getFurnitureId));
        List<UserFurnitureCount> userFurnitureCountList = userFurnitureRepository.queryUserFurnitureCountByUserIdAndFurnitureIds(userId, furnitureIds);
        List<UserFurnitureModel> userFurnitureModels = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(userFurnitureCountList)) {
            for (UserFurnitureCount userFurnitureCount : userFurnitureCountList) {
                List<UserFurnitureGroupRelation> userFurnitureGroupRelationsByFurnitureId = userFurnitureGroupRelationMap.get(
                        userFurnitureCount.getFurnitureId());
                int arrangedCount = CollectionUtils.isEmpty(userFurnitureGroupRelationsByFurnitureId) ? 0 : userFurnitureGroupRelationsByFurnitureId.size();
                UserFurnitureModel userFurnitureModel = UserFurnitureModel.of(userFurnitureCount, arrangedCount, false);
                userFurnitureModels.add(userFurnitureModel);
            }
        }
        return RpcResult.success(userFurnitureModels);
    }

    /**
     * 当用户进入家园二级页时
     * <p>
     * 1）若用户此前已有配置，直接返回用户已配置的套组信息，如果用户配置了多个套组，则显示用户最近保存的一套
     * 2）若用户此前没有配置，返回默认套组，默认套组是后台配置的，关联了该角色组的家具套组排序id最小的，
     * 不涉及到小屋和角色组的关联，小屋是独立于套组的，二者是解耦的
     *
     * @param param@return
     */
    @Override
    public RpcResult<HomePageConfigModel> getHomePageConfig(HomePageConfigParam param) {
        Integer userId = param.getUserId();
        Integer roleGroupId = param.getRoleGroupId();
        if (ObjectUtil.isEmpty(roleGroupId)) {
            return RpcResult.result(RoleGameResponse.ROLE_ID_NOT_EMPTY.getCode(), RoleGameResponse.ROLE_ID_NOT_EMPTY.getMessage());
        }
        HomePageConfigModel homePageConfigModel = new HomePageConfigModel();
        HomeModel homeModel = homeComponent.queryHomeInfo(userId, roleGroupId);
        FurnitureGroupModel userFurnitureGroupModel = getUserFurnitureGroupByUserId(userId, roleGroupId);
        //用户配置的套组
        homePageConfigModel.setFurnitureGroupModel(userFurnitureGroupModel);
        homePageConfigModel.setScene(homeModel.getSceneModel());
        homePageConfigModel.setCabin(homeModel.getCabinModel());
        boolean hasNewFurnitureRedDot = redDotComponent.hasAddNewFurnitureEvent(userId, roleGroupId) || redDotComponent.hasObtainFurnitureEvent(userId,
                roleGroupId);
        boolean hasFurnitureGroupCollectRedDot = userFurnitureComponent.hasFurnitureGroupCollectRedDot(userId, roleGroupId);
        homePageConfigModel.setHomeRedDotModel(
                new HomeRedDotModel().setFurnitureRedDot(hasNewFurnitureRedDot).setFurnitureIconRedDot(hasFurnitureGroupCollectRedDot));

        List<RoleCostumeConfigModel> list = getCostume(userId, roleGroupId);
        homePageConfigModel.setRoleCostumeList(list);

        return RpcResult.success(homePageConfigModel);
    }

    private List<RoleCostumeConfigModel> getCostume(Integer userId, Integer roleGroupId) {
        List<RoleCostumeConfigModel> resultList = new ArrayList<>();

        Map<Integer, List<RoleGroupRelation>> groupIdsFromCache = roleGroupRelationRepository.queryByGroupIdsFromCache(Collections.singleton(roleGroupId));

        if (CollectionUtil.isNotEmpty(groupIdsFromCache) && CollectionUtils.isNotEmpty(groupIdsFromCache.get(roleGroupId))) {

            Set<Integer> roleIds = groupIdsFromCache.get(roleGroupId).stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toSet());

            List<UserRole> userRoles = userRoleRepository.queryUserRoleFromCache(userId)
                    .stream()
                    .filter(e -> roleIds.contains(e.getRoleId()))
                    .collect(Collectors.toList());

            for (UserRole userRole : userRoles) {
                Integer effectiveCostumeId = userRole.getEffectiveCostumeId();
                if (Objects.isNull(effectiveCostumeId)) {
                    continue;
                }

                Costume costume = costumeRepository.queryByIdFromCache(effectiveCostumeId);

                if (Objects.nonNull(costume) && CostumeStatus.ON_SHELF.getCode() == costume.getStatus()) {
                    Costume.Config config = costume.getConfig();
                    if (Objects.nonNull(config)) {
                        SpineMaterial actionSpineMaterial = config.getActionSpineSmallMaterial();
                        if (Objects.nonNull(actionSpineMaterial)) {
                            String atlasSmallFileKey = actionSpineMaterial.getAtlasFileKey();
                            List<String> pngSmallFileKeys = java.util.Collections.singletonList(actionSpineMaterial.getPngFileKey());
                            String jsonSmallFileKey = actionSpineMaterial.getJsonFileKey();

                            resultList.add(new RoleCostumeConfigModel().setRoleId(userRole.getRoleId())
                                    .setCostumeId(effectiveCostumeId)
                                    .setAtlasSmallFileKey(atlasSmallFileKey)
                                    .setPngSmallFileKeys(pngSmallFileKeys)
                                    .setJsonFileKey(jsonSmallFileKey));
                        }
                    }
                }
            }

        }

        return resultList;
    }

    public FurnitureGroupModel getUserFurnitureGroupByUserId(Integer userId, Integer roleGroupId) {
        if (ObjectUtil.isEmpty(userId)) {
            log.warn("getUserFurnitureGroupByUserId userId is null, userId:{}", userId);
            return null;
        }
        UserHome userHome = userHomeRepository.queryByUserIdAndGroupId(userId, roleGroupId);
        if (ObjectUtil.isEmpty(userHome)) {
            log.warn("getUserFurnitureGroupByUserId userHome is null, userId:{}", userId);
            return null;
        }
        Integer correctFurnitureGroupId = homeComponent.correctCurrentFurnitureGroup(userHome, roleGroupId);
        log.debug("getUserFurnitureGroupByUserId userHome:{}", userHome);
        int furnitureGroupId = correctFurnitureGroupId == null ? userHome.getEffectiveFurnitureGroupId() : correctFurnitureGroupId;
        FurnitureGroupModel furnitureGroupModel = getFurnitureGroupById(furnitureGroupId);
        if (ObjectUtil.isEmpty(furnitureGroupModel)) {
            log.warn("getUserFurnitureGroupByUserId furnitureGroupModel is null, userId:{}", userId);
            FurnitureGroup furnitureGroup = furnitureGroupRepository.queryById(furnitureGroupId);
            furnitureGroupModel = new FurnitureGroupModel().setId(furnitureGroupId).setName(furnitureGroup.getName()).setFurnitureModels(new ArrayList<>());
        }
        log.debug("getUserFurnitureGroupByUserId furnitureGroupModel:{}", furnitureGroupModel);
        Map<Integer, FurnitureModel> furnitureModelMap = Optional.ofNullable(furnitureGroupModel.getFurnitureModels())
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(FurnitureModel::getId, Function.identity()));
        List<UserFurnitureGroupRelation> userFurnitureGroupRelations = userFurnitureGroupRelationRepository.queryByUserIdAndFurnitureGroupIdAndRoleGroupId(
                userId, furnitureGroupId, roleGroupId);
        log.debug("getUserFurnitureGroupByUserId userFurnitureGroupRelations:{}, furnitureGroupId:{}, roleGroupId:{}, userId:{}", userFurnitureGroupRelations,
                furnitureGroupId, roleGroupId, userId);

        if (CollectionUtil.isNotEmpty(userFurnitureGroupRelations)) {
            List<Integer> furnitureIds = userFurnitureGroupRelations.stream().map(UserFurnitureGroupRelation::getFurnitureId).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(furnitureIds)) {
                Set<Integer> haveFurnitureIdSet = furnitureGroupRelationRepository.queryByGroupId(furnitureGroupId)
                        .stream()
                        .map(FurnitureGroupRelation::getFurnitureId)
                        .collect(Collectors.toSet());
                List<FurnitureModel> furnitureModels = furnitureComponent.getFurnitureModelList(roleGroupId, furnitureIds)
                        .stream()
                        .filter(e -> haveFurnitureIdSet.contains(e.getId()))
                        .collect(Collectors.toList());
                if (furnitureGroupModel != null) {
                    furnitureGroupModel.setFurnitureModels(furnitureModels);
                }

                // 如果家具组没有用户已部置的家具，删除掉用户布置的家具
                if (!haveFurnitureIdSet.containsAll(furnitureIds)) {
                    furnitureIds.removeAll(haveFurnitureIdSet);
                    userFurnitureGroupRelationRepository.deletes(userId, furnitureGroupId, furnitureIds, roleGroupId);
                }

            }
            log.debug("getUserFurnitureGroupByUserId furnitureGroupModel:{},furnitureIds:{}, userId:{}", furnitureGroupModel, furnitureIds, userId);
        }
        log.info("getUserFurnitureGroupByUserId furnitureGroupModel:{}, userId:{}", furnitureGroupModel, userId);
        return furnitureGroupModel;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<Void> placeFurniture(PlaceFurnitureParam params) {
        Integer userId = params.getUserId();
        Integer furnitureGroupId = params.getFurnitureGroupId();
        List<Integer> furnitureIds = params.getFurnitureIds();
        Integer roleGroupId = params.getRoleGroupId();
        if (ObjectUtil.isEmpty(userId) || ObjectUtil.isEmpty(furnitureGroupId) || CollectionUtil.isEmpty(furnitureIds)) {
            log.error("布置全套失败 params is null, userId:{},pareams:{}", userId, JSONObject.toJSONString(params));
            return RpcResult.result(ResponseCodeMsg.SYSTEM_ERROR);
        }
        List<UserFurniture> userFurnitures = userFurnitureRepository.queryByUserIdAndFurnitureIds(userId, furnitureIds);
        if (CollectionUtil.isEmpty(userFurnitures)) {
            log.error("布置全套失败 userFurnitures is null, userId:{},pareams:{}", userId, JSONObject.toJSONString(params));
            return RpcResult.result(ResponseCodeMsg.SYSTEM_ERROR);
        }
        // 已摆放的家具
        List<UserFurnitureGroupRelation> userFurnitureGroupRelations = userFurnitureGroupRelationRepository.queryByUserIdAndFurnitureIds(userId, furnitureIds);
        Set<Long> usedFurnitureBid = userFurnitureGroupRelations.stream().map(relation -> relation.getBid()).collect(Collectors.toSet());
        Map<Integer, UserFurniture> needArrangeUserFurnitureMap = new HashMap<>();
        for (UserFurniture userFurniture : userFurnitures) {
            if (needArrangeUserFurnitureMap.containsKey(userFurniture.getFurnitureId())) {
                continue;
            }
            if (!usedFurnitureBid.contains(userFurniture.getBid())) {
                needArrangeUserFurnitureMap.put(userFurniture.getFurnitureId(), userFurniture);
                log.debug("placeFurniture needArrangeUserFurnitureMap:{}, userFurniture:{}, usedFurnitureBid:{}", needArrangeUserFurnitureMap, userFurniture,
                        usedFurnitureBid);
                usedFurnitureBid.add(userFurniture.getBid());
            }
        }
        if (CollectionUtils.size(needArrangeUserFurnitureMap) != CollectionUtils.size(furnitureIds)) {
            log.error(
                "布置全套失败 needArrangeUserFurnitureMap is null, userId:{},params:{},usedFurnitureBid:{},needArrangeUserFurnitureMap:{},userFurnitures:{},userFurnitureGroupRelations:{}",
                userId, params, usedFurnitureBid, needArrangeUserFurnitureMap, userFurnitures, userFurnitureGroupRelations);
            int lackFurnitureCount = CollectionUtils.size(furnitureIds) - CollectionUtils.size(needArrangeUserFurnitureMap);
            return RpcResult.result(RoleGameResponse.HOME_FURNITURE_GROUP_NOT_FOUND.getCode(),
                    String.format(RoleGameResponse.HOME_FURNITURE_GROUP_NOT_FOUND.getMessage(), lackFurnitureCount));
        }

        Collection<UserFurniture> needArrangeUserFurnitures = needArrangeUserFurnitureMap.values();
        List<UserFurnitureGroupRelation> needArrangRelations = new ArrayList<>();
        for (UserFurniture userFurniture : needArrangeUserFurnitures) {
            UserFurnitureGroupRelation userFurnitureGroupRelation = new UserFurnitureGroupRelation();
            userFurnitureGroupRelation.setBid(userFurniture.getBid());
            userFurnitureGroupRelation.setConfig(new UserFurnitureGroupRelation.Config());
            userFurnitureGroupRelation.setFurnitureGroupId(furnitureGroupId);
            userFurnitureGroupRelation.setFurnitureId(userFurniture.getFurnitureId());
            userFurnitureGroupRelation.setRoleGroupId(roleGroupId);
            userFurnitureGroupRelation.setUserId(userId);
            needArrangRelations.add(userFurnitureGroupRelation);
        }
        userFurnitureGroupRelationRepository.inserts(needArrangRelations);
        return RpcResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<Void> storeFurniture(FurnitureStoreParam furnitureStoreParam) {
        int userId = furnitureStoreParam.getUserId();
        int furnitureGroupId = furnitureStoreParam.getFurnitureGroupId();
        List<Integer> furnitureIds = furnitureStoreParam.getFurnitureIds();
        int roleGroupId = furnitureStoreParam.getRoleGroupId();
        if (ObjectUtil.isEmpty(furnitureGroupId) || CollectionUtil.isEmpty(furnitureIds)) {
            log.error("收起家具失败 params is null, userId:{},params:{}", userId, JSONObject.toJSONString(furnitureStoreParam));
            return RpcResult.result(ResponseCodeMsg.SYSTEM_ERROR);
        }
        userFurnitureComponent.storeFurniture(userId, furnitureIds, furnitureGroupId, roleGroupId);
        return RpcResult.success();
    }

    @Override
    public RpcResult<Integer> updateEffectiveFurnitureGroupId(EffectiveFurnitureGroupIdUpdateParam effectiveFurnitureGroupIdUpdateParam) {
        Integer userId = effectiveFurnitureGroupIdUpdateParam.getUserId();
        Integer furnitureGroupId = effectiveFurnitureGroupIdUpdateParam.getFurnitureGroupId();
        Integer roleGroupId = effectiveFurnitureGroupIdUpdateParam.getRoleGroupId();
        if (ObjectUtil.isEmpty(userId) || ObjectUtil.isEmpty(furnitureGroupId)) {
            log.error("updateUserHomeFurnitureGroupId userId or furnitureGroupId is null, userId:{},furnitureGroupId:{}", userId, furnitureGroupId);
            return RpcResult.result(ResponseCodeMsg.SYSTEM_ERROR);
        }
        UserHome userHome = userHomeRepository.queryByUserIdAndGroupId(userId, roleGroupId);
        if (ObjectUtil.isEmpty(userHome)) {
            log.error("updateUserHomeFurnitureGroupId userHome is null, userId:{},furnitureGroupId:{}", userId, furnitureGroupId);
            return RpcResult.result(ResponseCodeMsg.SYSTEM_ERROR);
        }
        int updateEffectiveFurnitureGroupIdCount = userHomeRepository.updateEffectiveFurnitureGroupId(userHome.getId(), userId, furnitureGroupId);
        return RpcResult.success(updateEffectiveFurnitureGroupIdCount);
    }

    @Override
    public RpcResult<List<FurnitureDetailModel>> getCanDisassembleFurniture(int userId) {
        List<FurnitureDetailModel> furnitureDetailModels = userFurnitureComponent.getFurnitureDetailModels(userId);
        return RpcResult.success(furnitureDetailModels);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<Void> disassembleFurniture(DisassembleFurnitureRpcParam disassembleFurnitureRpcParam) {
        List<FurnitureDetailModel> furnitureDetailModels = userFurnitureComponent.getFurnitureDetailModels(disassembleFurnitureRpcParam.getUserId());
        Map<Integer, Integer> furnitureCanDisassembleNumMap = furnitureDetailModels.stream()
                .collect(Collectors.toMap(FurnitureDetailModel::getId, FurnitureDetailModel::getCanDisassembleNum));
        Set<Integer> furnitureIds = disassembleFurnitureRpcParam.getDisassembleFurnitureNum().keySet();
        List<Furniture> furnitures = furnitureRepository.queryByIdsFromCache(furnitureIds);
        Map<Integer, Furniture> furnitureMap = furnitures.stream().collect(Collectors.toMap(Furniture::getId, Function.identity()));
        //校验要拆解的数量是否足够
        for (Map.Entry<Integer, Integer> entry : disassembleFurnitureRpcParam.getDisassembleFurnitureNum().entrySet()) {
            int furnitureId = entry.getKey();
            int disassembleNum = entry.getValue();

            if (furnitureCanDisassembleNumMap.get(furnitureId) < disassembleNum) {
                log.warn("Disassemble furniture not enough, furnitureId:{}, disassembleNum:{}, userId:{}", furnitureId, disassembleNum,
                        disassembleFurnitureRpcParam.getUserId());
                return RpcResult.result(RoleGameResponse.DISASSEMBLE_FURNITURE_NOT_ENOUGH);
            }
        }

        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.FURNITURE_POSTMARK_CONFIG);
        if (keyValueConfig == null) {
            log.warn("Disassemble furniture keyValueConfig is null, userId:{}", disassembleFurnitureRpcParam.getUserId());
            return RpcResult.result(RoleGameResponse.DISASSEMBLE_FURNITURE_CONFIG_NOT_EXIT);
        }
        List<FurniturePostmarkConfig> furniturePostmarkConfigs = GsonUtils.tryParseList(keyValueConfig.getValue(), FurniturePostmarkConfig.class);
        //计算出分解能得到的邮戳数量 key:家具类型和规格id value:邮戳数量
        Map<Integer, Integer> furnitureDisassembleNumMap = furniturePostmarkConfigs.stream()
                .collect(Collectors.toMap(FurniturePostmarkConfig::getFurnitureTypeAndSize, FurniturePostmarkConfig::getDisassemblePostmarkNum));

        Map<Integer, Integer> disassembleFurnitureNumMap = disassembleFurnitureRpcParam.getDisassembleFurnitureNum();
        //根据拆解数量乘以拆解能得到的邮戳数量
        Map<Integer, Integer> furnitureDisassemblePostmarkNumMap = new HashMap<>();
        for (Map.Entry<Integer, Integer> entry : disassembleFurnitureNumMap.entrySet()) {
            Integer furnitureId = entry.getKey();
            Furniture furniture = furnitureMap.get(furnitureId);
            if (furniture == null) {
                log.warn("Disassemble furniture furniture is null, furnitureId:{}, userId:{}", furnitureId, disassembleFurnitureRpcParam.getUserId());
                continue;
            }
            FurnitureType furnitureType = FurnitureType.getByCode(furniture.getType());
            FurnitureSize furnitureSize = FurnitureSize.getByCode(furniture.getSize());
            FurnitureTypeAndSize furnitureTypeAndSize = FurnitureTypeAndSize.getByFurnitureTypeAndSize(furnitureType, furnitureSize);
            if (furnitureTypeAndSize == null) {
                log.warn("Disassemble furniture furnitureTypeAndSize is null, furnitureId:{}, userId:{}, param:{}, furnitureType:{}, furnitureSize:{}",
                        furnitureId, disassembleFurnitureRpcParam.getUserId(), disassembleFurnitureRpcParam, furnitureType, furnitureSize);
                continue;
            }
            furnitureDisassemblePostmarkNumMap.put(furnitureTypeAndSize.getCode(),
                    furnitureDisassembleNumMap.get(furnitureTypeAndSize.getCode()) * entry.getValue());
        }

        //计算总邮戳数量
        int totalPostmarkNum = furnitureDisassemblePostmarkNumMap.values().stream().mapToInt(Integer::intValue).sum();

        UserFurnitureDecomposeOrder.Config config = new UserFurnitureDecomposeOrder.Config();
        config.setFurniturePostmarkConfigs(furniturePostmarkConfigs);
        config.setFurnitureDisassemblePostmarkNumMap(furnitureDisassemblePostmarkNumMap);
        config.setTotalPostmarkNum(totalPostmarkNum);
        config.setDisassembleFurnitureRpcParam(disassembleFurnitureRpcParam);

        long bid = BufferedIdGenerator.getId();
        UserFurnitureDecomposeOrder userFurnitureDecomposeOrder = new UserFurnitureDecomposeOrder();
        userFurnitureDecomposeOrder.setUserId(disassembleFurnitureRpcParam.getUserId());
        userFurnitureDecomposeOrder.setBid(bid);
        userFurnitureDecomposeOrder.setComposeConfig(config);
        userFurnitureDecomposeOrderRepository.insert(userFurnitureDecomposeOrder);
        userFurniturePostmarkComponent.chargeFurniturePostmark(disassembleFurnitureRpcParam.getUserId(), String.valueOf(bid), totalPostmarkNum,
                UserFurniturePostMarkFlowSource.DECOMPOSE.getCode());
        // SA埋点数据上报
        reportDismantleFurnitureEvent(disassembleFurnitureRpcParam, furnitureMap, totalPostmarkNum);
        return RpcResult.success();
    }

    @Override
    public RpcResult<List<FurnitureGroupModel>> getAllFurnitureGroupList(int userId) {
        List<FurnitureGroup> furnitureGroups = furnitureGroupRepository.queryAllOnShelf();
        Set<Integer> groupIds = furnitureGroups.stream().map(FurnitureGroup::getId).collect(Collectors.toSet());
        Map<Integer, List<FurnitureGroupRelation>> groupFurnitureListMap = furnitureGroupRelationRepository.queryByGroupIds(groupIds);
        List<UserFurnitureGroupRelation> relations = userFurnitureGroupRelationRepository.queryByUserId(userId);
        // 按照furnitureGroupId分组，并统计每个分组下furnitureId去重的数量
        Map<Integer, Integer> furnitureGroupCollectFurnitureNumMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(relations)) {
            furnitureGroupCollectFurnitureNumMap = relations.stream()
                    .collect(Collectors.groupingBy(UserFurnitureGroupRelation::getFurnitureGroupId,
                            Collectors.mapping(UserFurnitureGroupRelation::getFurnitureId, Collectors.collectingAndThen(Collectors.toSet(), Set::size))));
        }

        List<FurnitureGroupModel> result = new ArrayList<>();
        List<Long> collectAllPrizeIds = furnitureGroups.stream()
                .map(item -> item.getConfig().getCollectAllPrizeId())
                .filter(Objects::nonNull)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        Map<Long, PrizeSimpleModel> prizeSimpleModelMap = claimPrizeComponent.getPrizeSimpleModelMap(collectAllPrizeIds);
        Map<Integer, UserFurnitureGroup> userFurnitureGroupMap = userFurnitureGroupRepository.queryByUserIdAndFurnitureGroupIds(userId, groupIds);
        for (FurnitureGroup furnitureGroup : furnitureGroups) {
            FurnitureGroupModel furnitureGroupModel = FurnitureGroupModel.valueOf(furnitureGroup);
            List<FurnitureGroupRelation> furnitureGroupRelations = groupFurnitureListMap.get(furnitureGroup.getId());
            if (CollectionUtils.isEmpty(furnitureGroupRelations)) {
                log.debug("过滤掉无家具的家具组, furnitureGroupId:{}", furnitureGroup.getId());
                continue;
            }
            int totalFurnitureNum = furnitureGroupRelations.size();
            furnitureGroupModel.setTotalFurnitureNum(totalFurnitureNum);
            Integer collectFurnitureNum = furnitureGroupCollectFurnitureNumMap.getOrDefault(furnitureGroup.getId(), 0);
            furnitureGroupModel.setCollectFurnitureNum(collectFurnitureNum);
            Integer collectAllPrizeId = furnitureGroup.getConfig().getCollectAllPrizeId();
            if (collectAllPrizeId != null) {
                furnitureGroupModel.setCollectAllPrizeId(collectAllPrizeId);
                furnitureGroupModel.setCollectAll(totalFurnitureNum == collectFurnitureNum);
                furnitureGroupModel.setCollectAllPrize(prizeSimpleModelMap.get(Long.valueOf(collectAllPrizeId)));
                UserFurnitureGroup userFurnitureGroup = userFurnitureGroupMap.get(furnitureGroup.getId());
                furnitureGroupModel.setCollectAllPrizeReceive(userFurnitureGroup != null && userFurnitureGroup.getConfig().isAssignCollectAllPrize());
            }
            result.add(furnitureGroupModel);
        }
        return RpcResult.success(result);
    }

    @Override
    public RpcResult<Map<Integer, FurnitureModel.StoryTypeModel>> getStoryTypeMap(List<Integer> furnitureIds, Integer roleGroupId) {
        if (CollectionUtil.isEmpty(furnitureIds)) {
            return RpcResult.success(new HashMap<>());
        }
        List<FurnitureStoryRelation> furnitureStoryRelations = furnitureStoryRelationRepository.queryByFurnitureIds(furnitureIds);
        if (CollectionUtil.isEmpty(furnitureStoryRelations)) {
            return RpcResult.success(new HashMap<>());
        }
        List<Integer> storyIds = furnitureStoryRelations.stream().map(FurnitureStoryRelation::getStoryId).collect(Collectors.toList());
        Map<Integer, List<StoryRoleRelation>> storyIdToStoryRoleRelationMap = storyRepository.queryRelationByStoryIds(storyIds)
                .stream()
                .collect(Collectors.groupingBy(StoryRoleRelation::getStoryId));
        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupIdFromCache(roleGroupId);
        Map<Integer, Integer> roleIdToRoleGroupIdMap = roleGroupRelations.stream()
                .collect(Collectors.toMap(roleGroupRelation -> roleGroupRelation.getRoleId(), roleGroupRelation -> roleGroupRelation.getRoleGroupId()));
        Map<Integer, FurnitureModel.StoryTypeModel> storyTypeMap = new HashMap<>();
        for (FurnitureStoryRelation furnitureStoryRelation : furnitureStoryRelations) {
            List<StoryRoleRelation> storyRoleRelations = storyIdToStoryRoleRelationMap.get(furnitureStoryRelation.getStoryId());
            if (CollectionUtil.isEmpty(storyRoleRelations)) {
                continue;
            }
            for (StoryRoleRelation storyRoleRelation : storyRoleRelations) {
                Integer roleId = storyRoleRelation.getRoleId();
                if (roleIdToRoleGroupIdMap.containsKey(roleId)) {
                    FurnitureModel.StoryTypeModel storyTypeModel = new FurnitureModel.StoryTypeModel();
                    storyTypeModel.setStoryId(storyRoleRelation.getStoryId());
                    int size = CollectionUtils.size(storyRoleRelations);
                    int storyType;
                    if (size == 1) {
                        storyType = StoryRoleType.SOLO.getCode();
                    } else if (size == 2) {
                        storyType = StoryRoleType.DOUBLE.getCode();
                    } else {
                        storyType = StoryRoleType.UNKNOWN.getCode();
                    }
                    storyTypeModel.setStoryType(storyType);
                    storyTypeMap.put(furnitureStoryRelation.getFurnitureId(), storyTypeModel);
                }
            }
        }
        log.debug("getStoryTypeMap storyTypeMap:{},furnitureIds:{},roleGroupId:{}", storyTypeMap, furnitureIds, roleGroupId);
        return RpcResult.success(storyTypeMap);
    }

    @Override
    public RpcResult<Pair<List<Integer>, List<Integer>>> getUserNewFurnitureTag(UserFurnitureQueryParam userFurnitureQueryParam) {
        Integer userId = userFurnitureQueryParam.getUserId();
        List<Integer> furnitureIds = userFurnitureQueryParam.getFurnitureIds();
        if (ObjectUtil.isEmpty(userId) || CollectionUtil.isEmpty(furnitureIds)) {
            return RpcResult.success(Pair.of(new ArrayList<>(), new ArrayList<>()));
        }
        List<UserFurniture> userFurnitures = userFurnitureRepository.queryByUserIdAndFurnitureIds(userId, furnitureIds);
        List<Integer> userNewFurnitureIds = Optional.ofNullable(userFurnitures)
                .orElse(new ArrayList<>())
                .stream()
                .filter(UserFurniture::isShowRedDot)
                .map(UserFurniture::getFurnitureId)
                .collect(Collectors.toList());
        List<Integer> addNewFurnitureIds = Optional.ofNullable(redDotComponent.newlyFurnitureListIds(userId))
                .orElse(new ArrayList<>())
                .stream()
                .filter(furnitureId -> !userNewFurnitureIds.contains(furnitureId))
                .collect(Collectors.toList());
        log.info("getUserNewFurnitureTag userFurnitures:{},userNewFurnitureIds:{}, addNewFurnitureIds:{}, userId:{}", userFurnitures, userNewFurnitureIds,
                addNewFurnitureIds, userId);
        return RpcResult.success(Pair.of(userNewFurnitureIds, addNewFurnitureIds));
    }

    @Override
    public RpcResult<Void> clearFurnitureRedDot(int userId, int furnitureId) {
        redDotComponent.clearAddNewFurnitureEvent(userId, furnitureId);
        userFurnitureComponent.clearRedDot(userId, furnitureId);
        return RpcResult.success();
    }

    @Override
    public RpcResult<CourtyardModel> getCourtyardList(Integer roleGroupId) {
        if (roleGroupId == null) {
            return RpcResult.result(RoleGameResponse.ROLE_ID_NOT_EMPTY.getCode(), RoleGameResponse.ROLE_ID_NOT_EMPTY.getMessage());
        }
        CourtyardModel courtyardModel;
        List<CourtyardRoleGroupRelation> courtyardRoleGroupRelations = courtyardRoleGroupRelationRepository.queryByRoleGroupId(roleGroupId);
        if (CollectionUtil.isEmpty(courtyardRoleGroupRelations)) {
            log.warn("getCourtyardList courtyardRoleGroupRelations is null, roleGroupId:{}", roleGroupId);
            return RpcResult.result(RoleGameResponse.COURTYARD_NOT_FOUND.getCode(), RoleGameResponse.COURTYARD_NOT_FOUND.getMessage());
        }
        List<Integer> courtyardIds = courtyardRoleGroupRelations.stream().map(CourtyardRoleGroupRelation::getCourtyardId).collect(Collectors.toList());
        List<CourtyardConfig> courtyards = courtyardRepository.queryByIdsFromCache(courtyardIds);
        // 按照createAt排序，时间早的在前面
        courtyards.sort(Comparator.comparing(CourtyardConfig::getCreatedAt));
        if (CollectionUtil.isEmpty(courtyards)) {
            log.warn("getCourtyardList courtyards is null, roleGroupId:{}", roleGroupId);
            return RpcResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), ResponseCodeMsg.SYSTEM_ERROR.getMessage());
        }
        courtyardModel = CourtyardModel.of(courtyards.get(0));
        return RpcResult.success(courtyardModel);
    }

    @Override
    public RpcResult<List<String>> getBroadcastConfig() {
        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.BROADCAST_CONFIG);
        if (keyValueConfig == null) {
            return RpcResult.success(Collections.emptyList());
        }
        List<String> broadcastList = JsonUtils.findList(keyValueConfig.getValue(), String.class);
        return RpcResult.success(broadcastList);
    }

    /**
     * 上报拆解家具SA埋点数据
     * @param disassembleFurnitureRpcParam 拆解家具参数
     * @param furnitureMap 家具映射
     * @param totalPostmarkNum 总邮戳数量
     */
    private void reportDismantleFurnitureEvent(DisassembleFurnitureRpcParam disassembleFurnitureRpcParam, 
                                             Map<Integer, Furniture> furnitureMap, 
                                             int totalPostmarkNum) {
        try {
            Map<Integer, Integer> disassembleFurnitureNumMap = disassembleFurnitureRpcParam.getDisassembleFurnitureNum();
            
            // 拆解家具数量
            int disassembledNum = disassembleFurnitureNumMap.values().stream().mapToInt(Integer::intValue).sum();
            
            // 拆解家具ID列表
            List<Integer> disFurIdList = new ArrayList<>(disassembleFurnitureNumMap.keySet());
            
            // 拆解家具名称列表
            List<String> disFurNameList = disFurIdList.stream()
                    .map(furnitureMap::get)
                    .filter(Objects::nonNull)
                    .map(Furniture::getName)
                    .collect(Collectors.toList());
            
            // 是否包含互动家具
            boolean includesIntFur = disFurIdList.stream()
                    .map(furnitureMap::get)
                    .filter(Objects::nonNull)
                    .anyMatch(furniture -> furniture.getType() == FurnitureType.INTERACTIVE.getCode());
            
            // 构建埋点数据
            Map<String, Object> properties = new HashMap<>();
            properties.put("DisassembledNum", disassembledNum);
            properties.put("DisFurIdList", disFurIdList);
            properties.put("DisFurNameList", disFurNameList);
            properties.put("IncludesIntFur", includesIntFur);
            properties.put("PostmarksNum", totalPostmarkNum);
            
            // 上报埋点数据
            saComponent.uploadEventData(disassembleFurnitureRpcParam.getUserId(), SaComponent.DISMANTLE_FURNITURE, properties);
            
            log.info("reportDismantleFurnitureEvent success, userId:{}, properties:{}", 
                    disassembleFurnitureRpcParam.getUserId(), properties);
        } catch (Exception e) {
            log.error("reportDismantleFurnitureEvent error, userId:{}", 
                    disassembleFurnitureRpcParam.getUserId(), e);
        }
    }

}

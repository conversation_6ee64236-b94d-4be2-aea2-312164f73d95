package com.kuaikan.role.game.component;

import static com.kuaikan.role.game.api.bean.AvgChapter.HIDE_OPTIONS;
import static com.kuaikan.role.game.api.bean.AvgChapter.HIDE_OPTIONS_CN;
import static com.kuaikan.role.game.api.bean.AvgChapter.INSERT_BACK;

import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.mq.producer.KkMqProducer;
import com.kuaikan.role.game.api.bean.AvgProject;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.common.message.Message;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.api.bean.AvgChapter;
import com.kuaikan.role.game.api.bean.AvgDir;
import com.kuaikan.role.game.api.bean.AvgFileAliasRelation;
import com.kuaikan.role.game.api.bean.AvgGyroscope;
import com.kuaikan.role.game.api.bean.AvgHotZone;
import com.kuaikan.role.game.api.bean.AvgOriginFile;
import com.kuaikan.role.game.api.bean.CommonAudio;
import com.kuaikan.role.game.api.bean.CommonVideo;
import com.kuaikan.role.game.api.bean.UserAvgChapterRecord;
import com.kuaikan.role.game.api.constant.MqConstant;
import com.kuaikan.role.game.api.enums.AvgFileType;
import com.kuaikan.role.game.api.enums.AvgResourceType;
import com.kuaikan.role.game.api.enums.AvgTextUnlockCondition;
import com.kuaikan.role.game.api.enums.AvgTextValueCondition;
import com.kuaikan.role.game.api.model.mq.AvgRecordMessage;
import com.kuaikan.role.game.api.rpc.param.AvgRecordParam;
import com.kuaikan.role.game.api.rpc.param.avg.AvgTextUpdateParam;
import com.kuaikan.role.game.api.rpc.param.avg.AvgTextUpdateParam;
import com.kuaikan.role.game.api.model.mq.AvgRecordMessage;
import com.kuaikan.role.game.api.rpc.param.AvgRecordParam;
import com.kuaikan.role.game.api.rpc.result.AvgChapterModel;
import com.kuaikan.role.game.api.rpc.result.AvgChapterRecordModel;
import com.kuaikan.role.game.api.util.AvgOrderUtils;
import com.kuaikan.role.game.config.ApolloConfig;
import com.kuaikan.role.game.repository.AvgFileAliasRelationRepository;
import com.kuaikan.role.game.repository.AvgRepository;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class AvgComponentV2 {

    private static final Integer TEXT_PAGE_LIMIT = 2000;

    @Resource
    private AvgRepository avgRepository;
    @Resource
    private AvgFileAliasRelationRepository avgFileAliasRelationRepository;
    @Resource
    private ApolloConfig apolloConfig;
    @Resource
    private KkMqProducer avgRecordProducer;

    public Map<Integer, AvgChapterModel> batchQueryAvgChapter(Collection<Integer> chapterIds, boolean needQueryResources) {
        return batchQueryAvgChapter(chapterIds, new HashMap<>(), needQueryResources);
    }

    public Map<Integer, AvgChapterModel> batchQueryAvgChapter(Collection<Integer> chapterIds, Map<Integer, List<String>> chapter2TextIdsMap,
                                                              boolean needQueryResources) {
        ChapterQueryResults queryResults = queryDatabaseResourcesByPage(chapterIds, needQueryResources, new HashMap<>(), chapter2TextIdsMap, -1);
        List<AvgChapter> allChapters = queryResults.getAllChapters();

        return buildChapterModels(allChapters, queryResults);
    }

    public Map<Integer, AvgChapterModel> batchQueryAvgChapterBasicInfo(Collection<Integer> chapterIds) {
        List<AvgChapter> avgTextChapters = new ArrayList<>(avgRepository.queryByChapterIdsFromCache(chapterIds).values());
        if (CollectionUtils.isEmpty(avgTextChapters)) {
            return Collections.emptyMap();
        }
        return avgTextChapters.stream().map(avgChapter -> {
            AvgChapterModel avgChapterModel = new AvgChapterModel().setProjectId(avgChapter.getProjectId())
                    .setChapterId(avgChapter.getChapterId())
                    .setChapterName(avgChapter.getChapterName());
            return avgChapterModel;
        }).collect(Collectors.toMap(AvgChapterModel::getChapterId, Function.identity(), (o1, o2) -> o1));
    }

    public Map<Integer, AvgChapterModel> batchQueryAvgChapterByPage(Collection<Integer> chapterIds, boolean needQueryResources,
                                                                    Map<Integer, String> chapterCurrentTextIdMap, int textLimit) {
        ChapterQueryResults queryResults = queryDatabaseResourcesByPage(chapterIds, needQueryResources, chapterCurrentTextIdMap, new HashMap<>(), textLimit);
        List<AvgChapter> allChapters = queryResults.getAllChapters();
        return buildChapterModels(allChapters, queryResults);
    }

    public AvgChapterModel previewTextDynamic(AvgTextUpdateParam param) {
        Integer chapterId = param.getChapterId();
        if (chapterId == null) {
            log.warn("previewTextDynamic: chapterId is null");
            return null;
        }
        AvgChapter avgTextChapter = avgRepository.queryByChapterIdsFromCache(Collections.singleton(chapterId)).get(chapterId);
        if (avgTextChapter == null) {
            log.warn("previewTextDynamic: avgTextChapter is null for chapterId: {}", chapterId);
            return null;
        }
        AvgChapter.Text text = AvgTextUpdateParam.paramToText(param, null);
        avgTextChapter.setTextList(Collections.singletonList(text));
        List<AvgChapter> previewChapters = Lists.newArrayList(avgTextChapter);
        ChapterQueryResults chapterQueryResults = getChapterQueryResults(previewChapters);
        return buildChapterModels(previewChapters, chapterQueryResults).get(chapterId);
    }

    private ChapterQueryResults queryDatabaseResourcesByPage(Collection<Integer> chapterIds, boolean needQueryResources,
                                                             Map<Integer, String> chapterCurrentTextIdMap, Map<Integer, List<String>> chapter2TextIdsMap,
                                                             int textLimit) {
        // 查询文本章节和插入章节
        long startTime = System.currentTimeMillis();
        List<AvgChapter> avgTextChapters = new ArrayList<>(avgRepository.queryByChapterIdsFromCache(chapterIds).values());
        Set<Integer> allInsertChapterIds = avgTextChapters.stream()
                .map(AvgChapter::getTextList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .map(AvgChapter.Text::getInsertChapter)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        long queryTextChaptersEndTime = System.currentTimeMillis();
        long queryTextChaptersDuration = queryTextChaptersEndTime - startTime;
        log.debug("queryByChapterIdsFromCache 查询文本章节耗时：{} 毫秒", queryTextChaptersDuration);
        List<AvgChapter> allInsertChapterList = new ArrayList<>(avgRepository.queryByChapterIdsFromCache(allInsertChapterIds).values());

        long queryInsertChaptersEndTime = System.currentTimeMillis();
        long queryInsertChaptersDuration = queryInsertChaptersEndTime - queryTextChaptersEndTime;
        log.debug("queryByChapterIdsFromCache 查询插入章节耗时：{} 毫秒", queryInsertChaptersDuration);

        List<AvgChapter> allChapters = mergeChapter(avgTextChapters, allInsertChapterList);

        for (AvgChapter allChapter : allChapters) {
            List<AvgChapter.Text> textList = allChapter.getTextList();
            if (CollectionUtils.isEmpty(textList)) {
                allChapter.setTextList(new ArrayList<>());
                allChapter.setHadNextText(false);
                continue;
            }
            List<AvgChapter.Text> filteredTextList = new ArrayList<>();
            List<String> allowedTextIds = chapter2TextIdsMap.getOrDefault(allChapter.getChapterId(), new ArrayList<>());
            if (CollectionUtils.isNotEmpty(allowedTextIds)) {
                // 如果指定了 chapter2TextIdsMap，则只保留指定的文本
                for (AvgChapter.Text text : textList) {
                    if (allowedTextIds.contains(text.getTextId())) {
                        filteredTextList.add(text);
                    }
                }
            } else {
                // 如果没有指定 chapter2TextIdsMap，则保留所有文本
                filteredTextList.addAll(textList);
            }

            Map<String, Integer> chapterIndexMap = new HashMap<>();
            for (int i = 0; i < filteredTextList.size(); i++) {
                chapterIndexMap.put(filteredTextList.get(i).getTextId(), i);
            }

            if (textLimit == -1) {
                // 不分页
                allChapter.setTextList(filteredTextList);
                continue;
            }

            String currentTextId = Optional.ofNullable(chapterCurrentTextIdMap).orElse(new HashMap<>()).get(allChapter.getChapterId());
            int subIndex = 0;
            if (StringUtils.isNotBlank(currentTextId)) {
                subIndex = chapterIndexMap.getOrDefault(currentTextId, 0);
            }
            int limit = TEXT_PAGE_LIMIT;
            if (textLimit != 0) {
                limit = textLimit;
            }

            // 修复下标越界问题
            int endIndex = Math.min(subIndex + limit, filteredTextList.size());
            if (endIndex > subIndex) {
                List<AvgChapter.Text> texts = filteredTextList.subList(subIndex, endIndex);
                allChapter.setTextList(texts);
                allChapter.setHadNextText(endIndex < filteredTextList.size());
            } else {
                allChapter.setTextList(new ArrayList<>());
                allChapter.setHadNextText(false);
            }
        }
        if (!needQueryResources) {
            long endTime = System.currentTimeMillis();
            log.debug("queryDatabaseResourcesByPage 分页查询avg章节不带资源耗时：{} 毫秒", endTime - startTime);
            return new ChapterQueryResults().setAllChapters(allChapters);
        }
        long endTime = System.currentTimeMillis();
        ChapterQueryResults chapterQueryResults = getChapterQueryResults(allChapters);
        log.debug("queryDatabaseResourcesByPage 分页查询avg章节带资源耗时：{} 毫秒", endTime - startTime);
        return chapterQueryResults;
    }

    private List<AvgChapter> mergeChapter(List<AvgChapter> avgTextChapters, List<AvgChapter> allInsertChapterList) {
        Map<Integer, AvgChapter> avgInsertChapterMap = allInsertChapterList.stream().collect(Collectors.toMap(AvgChapter::getChapterId, Function.identity()));
        for (AvgChapter avgChapter : avgTextChapters) {
            List<AvgChapter.Text> textList = avgChapter.getTextList();
            if (textList == null || textList.isEmpty()) {
                continue;
            }
            // 创建一个可修改的副本
            List<AvgChapter.Text> modifiedTextList = new ArrayList<>(textList);
            // 从后往前遍历文本列表
            for (int i = modifiedTextList.size() - 1; i >= 0; i--) {
                AvgChapter.Text textModel = modifiedTextList.get(i);
                Integer insertChapter = textModel.getInsertChapter();
                if (insertChapter == null) {
                    continue; // 如果没有插入章节，跳过当前文本块
                }

                AvgChapter insertAvgChapter = avgInsertChapterMap.get(insertChapter);
                if (insertAvgChapter == null) {
                    continue; // 如果插入章节模型不存在，跳过当前文本块
                }

                List<AvgChapter.Text> insertChapterTextModels = JsonUtils.findList(JsonUtils.writeValueAsString(insertAvgChapter.getTextList()),
                        AvgChapter.Text.class);
                if (insertChapterTextModels == null || insertChapterTextModels.isEmpty()) {
                    continue; // 如果插入章节的文本列表为空，跳过当前文本块
                }

                // 处理插入章节的文本模型
                processAndInsertTextModels(textModel, insertChapterTextModels, modifiedTextList, i);
            }
            // 更新文本列表
            avgChapter.setTextList(modifiedTextList);
        }
        return avgTextChapters;
    }

    private void processAndInsertTextModels(AvgChapter.Text parentTextModel, List<AvgChapter.Text> insertTextModels, List<AvgChapter.Text> modifiedTextList,
                                            int insertIndex) {
        String parentTextId = parentTextModel.getTextId();
        List<String> parentNextIds = parentTextModel.getNextIds();

        if (parentNextIds == null) {
            parentNextIds = new ArrayList<>();
        }

        String childFirstTextId = null;

        for (AvgChapter.Text insertTextModel : insertTextModels) {
            String originalTextId = insertTextModel.getTextId();
            String newTextId = parentTextId + "-" + originalTextId;
            insertTextModel.setTextId(newTextId);

            if (childFirstTextId == null) {
                childFirstTextId = newTextId;
                parentTextModel.setNextIds(Lists.newArrayList(childFirstTextId));
            }

            // 调整下一个ID
            adjustNextIds(insertTextModel, parentTextId, parentNextIds);

            // 调整对话选项
            adjustDialogueOptions(insertTextModel, parentTextId);

            // 处理插入章节的返回逻辑
            handleInsertChapterBack(insertTextModel, parentNextIds);

            // 处理循环下一个文本ID
            handleLoopNextTextId(insertTextModel, parentTextId);
        }

        // 将处理后的插入文本模型插入到列表中
        modifiedTextList.addAll(insertIndex + 1, insertTextModels);
    }

    private void handleInsertChapterBack(AvgChapter.Text text, List<String> parentNextIds) {
        if (INSERT_BACK.equals(text.getInsertChapterBack())) {
            // todo insertChapterBackId 应该没有用了
            text.setInsertChapterBackId(parentNextIds.get(0));
            text.setNextIds(parentNextIds);
        }
    }

    private void handleLoopNextTextId(AvgChapter.Text insertChapterTextModel, String parentTextId) {
        String loopNextTextId = insertChapterTextModel.getLoopNextTextId();
        if (StringUtils.isNotBlank(loopNextTextId)) {
            insertChapterTextModel.setLoopNextTextId(parentTextId + "-" + loopNextTextId);
        }
    }

    private ChapterQueryResults queryDatabaseResources(Collection<Integer> chapterIds, Map<Integer, List<String>> chapter2TextIdsMap,
                                                       boolean needQueryResources) {
        long startTime = System.currentTimeMillis();

        // 查询文本章节和插入章节
        List<AvgChapter> avgTextChapters = new ArrayList<>(avgRepository.queryByChapterIdsFromCache(chapterIds).values());
        Map<Integer, Map<String, AvgChapter.Text>> chapter2TextIdMap = avgTextChapters.stream()
                .collect(Collectors.toMap(AvgChapter::getChapterId,
                        value -> value.getTextList().stream().collect(Collectors.toMap(AvgChapter.Text::getTextId, v -> v))));

        Map<Integer, List<String>> insertChapter2TextIdsMap = new HashMap<>();
        for (Map.Entry<Integer, List<String>> chapter2TextIdsMapEntry : chapter2TextIdsMap.entrySet()) {
            Integer textChapterId = chapter2TextIdsMapEntry.getKey();
            List<String> chapterTextList = chapter2TextIdsMapEntry.getValue();
            List<Pair<String, String>> insertChapterText = chapterTextList.stream()
                    .filter(item -> item.contains("-"))
                    .map(item -> Pair.of(item.split("-")[0], item.split("-")[1]))
                    .collect(Collectors.toList());
            for (Pair<String, String> parentId2TextIdEntry : insertChapterText) {
                String textChapterTextId = parentId2TextIdEntry.getLeft();
                Map<String, AvgChapter.Text> textChapterTextMap = chapter2TextIdMap.get(textChapterId);
                Optional<Integer> insertChapterId = Optional.ofNullable(textChapterTextMap.get(textChapterTextId)).map(AvgChapter.Text::getInsertChapter);
                if (insertChapterId.isPresent()) {
                    insertChapter2TextIdsMap.putIfAbsent(insertChapterId.get(), new ArrayList<>());
                    insertChapter2TextIdsMap.get(insertChapterId.get()).add(parentId2TextIdEntry.getRight());
                }
            }
        }

        long queryTextChaptersEndTime = System.currentTimeMillis();
        long queryTextChaptersDuration = queryTextChaptersEndTime - startTime;
        log.debug("queryByChapterIds 查询文本章节耗时：{} 毫秒", queryTextChaptersDuration);
        Set<Integer> insertChapterIds = insertChapter2TextIdsMap.keySet();

        Set<Integer> allInsertChapterIds = CollectionUtils.isNotEmpty(insertChapterIds)
                ? insertChapterIds
                : avgTextChapters.stream()
                        .map(AvgChapter::getTextList)
                        .flatMap(List::stream)
                        .map(AvgChapter.Text::getInsertChapter)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
        List<AvgChapter> allInsertChapterList = new ArrayList<>(avgRepository.queryByChapterIdsFromCache(allInsertChapterIds).values());

        Map<Integer, Map<String, AvgChapter.Text>> insertChapter2TextIdMap = allInsertChapterList.stream()
                .collect(Collectors.toMap(AvgChapter::getChapterId,
                        value -> value.getTextList().stream().collect(Collectors.toMap(AvgChapter.Text::getTextId, v -> v))));

        long queryInsertChaptersEndTime = System.currentTimeMillis();
        long queryInsertChaptersDuration = queryInsertChaptersEndTime - queryTextChaptersEndTime;
        log.debug("queryByChapterIds 查询插入章节耗时：{} 毫秒", queryInsertChaptersDuration);

        //处理text过滤
        List<AvgChapter> allChapters = new ArrayList<>();
        if (MapUtils.isNotEmpty(chapter2TextIdsMap)) {
            for (AvgChapter avgTextChapter : avgTextChapters) {
                AvgChapter copyChapter = JsonUtils.findObject(JsonUtils.writeValueAsString(avgTextChapter), AvgChapter.class);
                List<String> chapterTextIds = chapter2TextIdsMap.get(avgTextChapter.getChapterId());
                Map<String, AvgChapter.Text> chapterTextMap = chapter2TextIdMap.get(avgTextChapter.getChapterId());
                List<AvgChapter.Text> textList = chapterTextIds.stream().map(chapterTextMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                copyChapter.setTextList(textList);
                allChapters.add(copyChapter);
            }
            for (AvgChapter insertAvgChapter : allInsertChapterList) {
                AvgChapter copyInsertChapter = JsonUtils.findObject(JsonUtils.writeValueAsString(insertAvgChapter), AvgChapter.class);
                List<String> chapterTextIds = insertChapter2TextIdsMap.get(insertAvgChapter.getChapterId());
                Map<String, AvgChapter.Text> chapterTextMap = insertChapter2TextIdMap.get(insertAvgChapter.getChapterId());
                List<AvgChapter.Text> textList = chapterTextIds.stream().map(chapterTextMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                copyInsertChapter.setTextList(textList);
                allChapters.add(copyInsertChapter);
            }
        } else {
            allChapters.addAll(avgTextChapters);
            allChapters.addAll(allInsertChapterList);
        }

        if (!needQueryResources) {
            log.debug("queryDatabaseResources 方法执行耗时：{} 毫秒", System.currentTimeMillis() - startTime);
            return new ChapterQueryResults().setAllChapters(allChapters);
        }
        return getChapterQueryResults(allChapters);
    }

    @NotNull
    private ChapterQueryResults getChapterQueryResults(List<AvgChapter> allChapters) {
        long startTime = System.currentTimeMillis();

        Set<String> projectIds = allChapters.stream().map(AvgChapter::getProjectId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<String, AvgProject> projectIdToAvgProjectMap = avgRepository.queryAvgProjectsByIds(projectIds);

        // 查询目录文件内容
        List<AvgChapter.DirFileContents> allDirFileContentList = allChapters.stream().map(AvgChapter::getDirFileContents).collect(Collectors.toList());
        Set<String> hotZoneNames = collectNames(allDirFileContentList, AvgChapter.DirFileContents::getHotZoneNames);
        Set<String> fileNames = collectNames(allDirFileContentList, AvgChapter.DirFileContents::getFileNames);
        Set<String> dynamicRoleDirNames = collectNames(allDirFileContentList, AvgChapter.DirFileContents::getDynamicRoleDirNames);
        Set<String> spineNames = collectNames(allDirFileContentList, AvgChapter.DirFileContents::getSpineNames);
        Set<String> qSpineNames = collectNames(allDirFileContentList, AvgChapter.DirFileContents::getQSpineNames);
        Set<String> gyroscopeNames = collectNames(allDirFileContentList, AvgChapter.DirFileContents::getGyroscopeNames);
        Set<Integer> styleIds = allChapters.stream().map(AvgChapter::getStyleId).collect(Collectors.toSet());
        Set<Integer> projectStyleIds = projectIdToAvgProjectMap.values()
                .stream()
                .map(AvgProject::getStyleId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        styleIds.addAll(projectStyleIds);

        List<String> allAliasList = Stream.of(hotZoneNames, fileNames, dynamicRoleDirNames, spineNames, qSpineNames, gyroscopeNames)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Set::stream)
                .distinct()
                .collect(Collectors.toList());

        long findByNamesFromCacheStartTime = System.currentTimeMillis();
        Map<String, AvgFileAliasRelation> allAliasRelationsMap = avgFileAliasRelationRepository.findByNamesFromCache(allAliasList);
        long findByNamesFromCacheEndTime = System.currentTimeMillis();
        long findByNamesFromCacheDuration = findByNamesFromCacheEndTime - findByNamesFromCacheStartTime;
        log.debug("findByNamesFromCache查询原始文件耗时：{} 毫秒", findByNamesFromCacheDuration);

        // 按照type分组
        Map<Integer, List<AvgFileAliasRelation>> aliasGroupMap = allAliasRelationsMap.values()
                .stream()
                .collect(Collectors.groupingBy(AvgFileAliasRelation::getType));
        // 文件
        Map<String, AvgOriginFile> avgOriginFileMap = queryOriginFileMaps(aliasGroupMap);
        // 热区
        Map<String, AvgHotZone> avgHotZoneMap = queryAvgHotZoneMap(aliasGroupMap);
        // 目录
        Map<String, AvgDir> allAvgDirMap = queryAvgDirMap(aliasGroupMap);
        // 陀螺仪
        Map<String, AvgGyroscope> avgGyroscopeMap = queryAvgGyroscopeMap(aliasGroupMap);

        List<Integer> allDirIds = collectAllDirIds(allAvgDirMap, styleIds);
        long queryParentId2FileListStartTime = System.currentTimeMillis();
        Map<Integer, List<AvgOriginFile>> parentId2FileList = avgRepository.queryAvgOriginFileByParentIdsFromCache(allDirIds);
        long queryParentId2FileListEndTime = System.currentTimeMillis();
        long queryParentId2FileListDuration = queryParentId2FileListEndTime - queryParentId2FileListStartTime;
        log.debug("queryAvgOriginFileByParentIdsFromCache 查询父级文件列表耗时：{} 毫秒", queryParentId2FileListDuration);

        addGyroscopeVideosToOriginFileMap(avgGyroscopeMap, avgOriginFileMap);

        long getCommonVideoMapStartTime = System.currentTimeMillis();
        Map<String, CommonVideo> commonVideoMap = getHighlightVideoMap(avgOriginFileMap);
        log.debug("getHighlightVideoMap 获取高亮视频映射耗时：{} 毫秒", System.currentTimeMillis() - getCommonVideoMapStartTime);

        long getCommonAudioMapStartTime = System.currentTimeMillis();
        Map<String, CommonAudio> commonAudioMap = getCommonAudioMap(avgOriginFileMap);
        log.debug("getCommonAudioMap 获取公共音频映射耗时：{} 毫秒", System.currentTimeMillis() - getCommonAudioMapStartTime);

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        log.debug("getChapterQueryResults 方法执行耗时：{} 毫秒", duration);

        Map<String, AvgDir> dynamicRoleName2DirIdMap = allAvgDirMap.entrySet()
                .stream()
                .filter(e -> dynamicRoleDirNames.contains(e.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        Map<String, AvgDir> spineNameMap = allAvgDirMap.entrySet()
                .stream()
                .filter(e -> spineNames.contains(e.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        Map<String, AvgDir> qSpineNameMap = allAvgDirMap.entrySet()
                .stream()
                .filter(e -> qSpineNames.contains(e.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        return new ChapterQueryResults(avgOriginFileMap, avgHotZoneMap, avgGyroscopeMap, dynamicRoleName2DirIdMap, spineNameMap, qSpineNameMap, parentId2FileList,
            commonVideoMap, commonAudioMap, allChapters, projectIdToAvgProjectMap);
    }

    private <T> Set<String> collectNames(List<AvgChapter.DirFileContents> allDirFileContentList, Function<AvgChapter.DirFileContents, Set<T>> nameExtractor) {
        return allDirFileContentList.stream().map(nameExtractor).flatMap(Set::stream).map(Object::toString).collect(Collectors.toSet());
    }

    // origin file alias map
    public Map<String, AvgOriginFile> queryOriginFileMaps(Map<Integer, List<AvgFileAliasRelation>> aliasGroupMap) {
        Map<String, AvgFileAliasRelation> fileAliasRelations = Optional.ofNullable(aliasGroupMap.get(AvgResourceType.FILE.getCode()))
                .orElseGet(Collections::emptyList)
                .stream()
                .collect(Collectors.toMap(AvgFileAliasRelation::getName, Function.identity()));
        Set<Integer> originFileIds = fileAliasRelations.values().stream().map(AvgFileAliasRelation::getFileId).collect(Collectors.toSet());
        Map<Integer, AvgOriginFile> originFileIdsMap = Optional.ofNullable(avgRepository.queryAvgOriginFileByIdsFromCache(originFileIds))
                .orElseGet(Collections::emptyMap);
        return fileAliasRelations.entrySet()
                .stream()
                .filter(entry -> originFileIdsMap.containsKey(entry.getValue().getFileId()))
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> originFileIdsMap.get(entry.getValue().getFileId())));
    }

    // hot zone alias map
    public Map<String, AvgHotZone> queryAvgHotZoneMap(Map<Integer, List<AvgFileAliasRelation>> aliasGroupMap) {
        Map<String, AvgFileAliasRelation> hotZoneAliasRelations = Optional.ofNullable(aliasGroupMap.get(AvgResourceType.HOT_ZONE.getCode()))
                .orElseGet(Collections::emptyList)
                .stream()
                .collect(Collectors.toMap(AvgFileAliasRelation::getName, Function.identity()));
        Set<Integer> hotZoneIds = hotZoneAliasRelations.values().stream().map(AvgFileAliasRelation::getFileId).collect(Collectors.toSet());
        Map<Integer, AvgHotZone> avgHotZoneIdsMap = Optional.ofNullable(avgRepository.queryAvgHotZoneByIdsFromCache(hotZoneIds))
                .orElseGet(Collections::emptyMap);
        return hotZoneAliasRelations.entrySet()
                .stream()
                .filter(entry -> avgHotZoneIdsMap.containsKey(entry.getValue().getFileId()))
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> avgHotZoneIdsMap.get(entry.getValue().getFileId())));
    }

    public Map<String, AvgGyroscope> queryAvgGyroscopeMap(Map<Integer, List<AvgFileAliasRelation>> aliasGroupMap) {
        Map<String, AvgFileAliasRelation> gyroscopeAliasRelations = Optional.ofNullable(aliasGroupMap.get(AvgResourceType.GYROSCOPE.getCode()))
                .orElseGet(Collections::emptyList)
                .stream()
                .collect(Collectors.toMap(AvgFileAliasRelation::getName, Function.identity()));
        Set<Integer> gyroscopeIds = gyroscopeAliasRelations.values().stream().map(AvgFileAliasRelation::getFileId).collect(Collectors.toSet());
        Map<Integer, AvgGyroscope> avgGyroscopeIdsMap = Optional.ofNullable(avgRepository.queryGyroscopeByIdsFromCache(gyroscopeIds))
                .orElseGet(Collections::emptyMap);
        return gyroscopeAliasRelations.entrySet()
                .stream()
                .filter(entry -> avgGyroscopeIdsMap.containsKey(entry.getValue().getFileId()))
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> avgGyroscopeIdsMap.get(entry.getValue().getFileId())));
    }

    // avg dir alias map
    public Map<String, AvgDir> queryAvgDirMap(Map<Integer, List<AvgFileAliasRelation>> aliasGroupMap) {
        Map<String, AvgFileAliasRelation> avgDirRelations = Optional.ofNullable(aliasGroupMap.get(AvgResourceType.DIR.getCode()))
                .orElseGet(Collections::emptyList)
                .stream()
                .collect(Collectors.toMap(AvgFileAliasRelation::getName, Function.identity()));
        Set<Integer> avgDirIds = avgDirRelations.values().stream().map(AvgFileAliasRelation::getFileId).collect(Collectors.toSet());
        Map<Integer, AvgDir> avgDirIdsMap = Optional.ofNullable(avgRepository.queryAvgDirByIdsFromCache(avgDirIds)).orElseGet(Collections::emptyMap);
        return avgDirRelations.entrySet()
                .stream()
                .filter(entry -> avgDirIdsMap.containsKey(entry.getValue().getFileId()))
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> avgDirIdsMap.get(entry.getValue().getFileId())));
    }

    private List<Integer> collectAllDirIds(Map<String, AvgDir> avgDirMap, Set<Integer> styleIds) {
        List<Integer> allDirIds = new ArrayList<>();
        allDirIds.addAll(avgDirMap.values().stream().map(AvgDir::getId).collect(Collectors.toList()));
        allDirIds.addAll(styleIds);
        return allDirIds;
    }

    private Map<Integer, AvgChapterModel> buildChapterModels(List<AvgChapter> allChapters, ChapterQueryResults queryResults) {
        Map<Integer, AvgChapterModel> avgTextChapterModelMap = new HashMap<>();
        Map<String, AvgOriginFile> avgOriginFileMap = queryResults.getAvgOriginFileMap();
        Map<String, AvgHotZone> avgHotZoneMap = queryResults.getAvgHotZoneMap();
        Map<String, AvgGyroscope> avgGyroscopeMap = queryResults.getAvgGyroscopeMap();
        Map<String, AvgDir> dynamicRoleName2DirIdMap = queryResults.getDynamicRoleName2DirIdMap();
        Map<String, AvgDir> spineNameMap = queryResults.getSpineNameMap();
        Map<String, AvgDir> qSpineNameMap = queryResults.getqSpineNameMap();
        Map<Integer, List<AvgOriginFile>> parentId2FileList = queryResults.getParentId2FileList();
        Map<String, CommonVideo> commonVideoMap = queryResults.getCommonVideoMap();
        Map<String, CommonAudio> commonAudioMap = queryResults.getCommonAudioMap();
        Map<String, AvgProject> projectIdToAvgProjectMap = queryResults.getProjectIdToAvgProjectMap();

        // 合并 spineNameMap 和 qSpineNameMap，避免每次循环都合并
        Map<String, AvgDir> mergedSpineNameMap = new HashMap<>(spineNameMap);
        mergedSpineNameMap.putAll(qSpineNameMap);

        for (AvgChapter avgChapter : allChapters) {
            // 获取过滤后的目录文件内容
            AvgChapter.DirFileContents dirFileContents = avgChapter.getDirFileContents();
            Set<String> chapterHotZoneNames = dirFileContents.getHotZoneNames();
            Set<String> chapterGyroscopeNames = dirFileContents.getGyroscopeNames();
            Set<String> chapterFileNames = dirFileContents.getFileNames();
            Set<String> chapterDynamicRoleDirNames = dirFileContents.getDynamicRoleDirNames();
            Set<String> chapterSpineNames = dirFileContents.getSpineNames();
            Set<String> chapterQSpineNames = dirFileContents.getQSpineNames();

            // 合并脊柱名称
            Set<String> chapterAllSpineNames = new HashSet<>();
            chapterAllSpineNames.addAll(chapterSpineNames);
            chapterAllSpineNames.addAll(chapterQSpineNames);

            // 过滤章节相关的热点区域
            Map<String, AvgHotZone> chapterHotZoneMap = filterChapterHotZones(avgHotZoneMap, chapterHotZoneNames);

            // 过滤章节相关的陀螺仪
            Map<String, AvgGyroscope> chapterAvgGyroscopeMap = filterChapterGyroscope(avgGyroscopeMap, chapterGyroscopeNames);
            Set<String> gyroscopeVideoNames = chapterAvgGyroscopeMap.values()
                    .stream()
                    .filter(ele -> ele.getConfig() != null && CollectionUtils.isNotEmpty(ele.getConfig().getGyroscopes()))
                    .flatMap(config -> config.getConfig().getGyroscopes().stream())
                    .map(AvgGyroscope.GyroscopeItem::getImmediateVideo)
                    .collect(Collectors.toSet());
            // 过滤陀螺仪相关的视频文件
            Map<String, AvgOriginFile> gyroscopeVideoFileMap = filterChapterFiles(avgOriginFileMap, gyroscopeVideoNames);

            // 过滤章节相关的文件
            Map<String, AvgOriginFile> chapterFileMap = filterChapterFiles(avgOriginFileMap, chapterFileNames);

            // 过滤章节相关的动态角色目录
            Map<String, AvgDir> chapterDynamicRoleName2DirIdMap = filterChapterDirs(dynamicRoleName2DirIdMap, chapterDynamicRoleDirNames);

            // 过滤章节相关的脊柱目录
            Map<String, AvgDir> chapterSpineNameMap = filterChapterDirs(mergedSpineNameMap, chapterAllSpineNames);

            // 收集章节相关的目录ID
            List<Integer> chapterDirIds = collectChapterDirIds(chapterDynamicRoleName2DirIdMap, chapterSpineNameMap, avgChapter);

            // 过滤章节相关的父级文件列表
            Map<Integer, List<AvgOriginFile>> chapterParentId2FileList = filterChapterParentFiles(parentId2FileList, chapterDirIds);

            // 命令重的参数，需要替换成的url
            Map<String, String> linkUrlMap = Maps.newHashMapWithExpectedSize(2);
            linkUrlMap.put(AvgOrderUtils.CARD_BATTLE_PACKAGE_URL_KEY, apolloConfig.getCardBattlePackageUrl());
            linkUrlMap.put(AvgOrderUtils.CARD_BATTLE_EXPLORE_URL_KEY, apolloConfig.getCardBattleExploreUrl());

            // 构建章节模型
            AvgChapterModel avgChapterModel = AvgChapterModel.valueOf(avgChapter, chapterParentId2FileList, chapterDynamicRoleName2DirIdMap, chapterFileMap,
                    chapterHotZoneMap, chapterAvgGyroscopeMap, gyroscopeVideoFileMap, chapterSpineNameMap, commonVideoMap, commonAudioMap, linkUrlMap, projectIdToAvgProjectMap);

            avgTextChapterModelMap.put(avgChapter.getChapterId(), avgChapterModel);
        }
        return avgTextChapterModelMap;
    }

    private Map<String, AvgHotZone> filterChapterHotZones(Map<String, AvgHotZone> avgHotZoneMap, Set<String> chapterHotZoneNames) {
        return chapterHotZoneNames.stream().filter(avgHotZoneMap::containsKey).collect(Collectors.toMap(Function.identity(), avgHotZoneMap::get));
    }

    private Map<String, AvgGyroscope> filterChapterGyroscope(Map<String, AvgGyroscope> avgGyroscopeMap, Set<String> chapterGyroscopeNames) {
        return chapterGyroscopeNames.stream().filter(avgGyroscopeMap::containsKey).collect(Collectors.toMap(Function.identity(), avgGyroscopeMap::get));
    }

    private Map<String, AvgOriginFile> filterChapterFiles(Map<String, AvgOriginFile> avgOriginFileMap, Set<String> chapterFileNames) {
        return chapterFileNames.stream().filter(avgOriginFileMap::containsKey).collect(Collectors.toMap(Function.identity(), avgOriginFileMap::get));
    }

    private Map<String, AvgDir> filterChapterDirs(Map<String, AvgDir> dirIdMap, Set<String> chapterDirNames) {
        return chapterDirNames.stream().filter(dirIdMap::containsKey).collect(Collectors.toMap(Function.identity(), dirIdMap::get));
    }

    private List<Integer> collectChapterDirIds(Map<String, AvgDir> chapterDynamicRoleName2DirIdMap, Map<String, AvgDir> chapterSpineNameMap,
                                               AvgChapter avgChapter) {
        List<Integer> chapterDirIds = new ArrayList<>();
        chapterDirIds.addAll(chapterDynamicRoleName2DirIdMap.values().stream().map(AvgDir::getId).collect(Collectors.toList()));
        chapterDirIds.addAll(chapterSpineNameMap.values().stream().map(AvgDir::getId).collect(Collectors.toList()));
        chapterDirIds.add(avgChapter.getStyleId());
        return chapterDirIds;
    }

    private Map<Integer, List<AvgOriginFile>> filterChapterParentFiles(Map<Integer, List<AvgOriginFile>> parentId2FileList, List<Integer> chapterDirIds) {
        return parentId2FileList.entrySet()
                .stream()
                .filter(entry -> chapterDirIds.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private void processInsertChapterLogic(Map<Integer, AvgChapterModel> avgTextChapterModelMap, Map<Integer, AvgChapterModel> avgInsertChapterModelMap) {
        for (AvgChapterModel avgChapterModel : avgTextChapterModelMap.values()) {
            List<AvgChapterModel.TextModel> textList = avgChapterModel.getTextList();
            if (textList == null || textList.isEmpty()) {
                continue;
            }
            // 创建一个可修改的副本
            List<AvgChapterModel.TextModel> modifiedTextList = new ArrayList<>(textList);
            List<AvgChapterModel.SpineFileModel> allInsertChapterSpineFileModels = new ArrayList<>();

            // 从后往前遍历文本列表
            for (int i = modifiedTextList.size() - 1; i >= 0; i--) {
                AvgChapterModel.TextModel textModel = modifiedTextList.get(i);
                Integer insertChapter = textModel.getInsertChapter();
                if (insertChapter == null) {
                    continue; // 如果没有插入章节，跳过当前文本块
                }

                AvgChapterModel insertAvgChapterModel = avgInsertChapterModelMap.get(insertChapter);
                if (insertAvgChapterModel == null) {
                    continue; // 如果插入章节模型不存在，跳过当前文本块
                }

                List<AvgChapterModel.SpineFileModel> insertChapterSpineFileModels = insertAvgChapterModel.getSpineFiles();
                if (insertChapterSpineFileModels != null) {
                    allInsertChapterSpineFileModels.addAll(insertChapterSpineFileModels);
                }

                List<AvgChapterModel.TextModel> insertChapterTextModels = insertAvgChapterModel.getTextList();
                if (insertChapterTextModels == null || insertChapterTextModels.isEmpty()) {
                    continue; // 如果插入章节的文本列表为空，跳过当前文本块
                }

                // 处理插入章节的文本模型
                processAndInsertTextModels(textModel, insertChapterTextModels, modifiedTextList, i);
            }

            // 合并spine文件
            mergeSpineFiles(avgChapterModel, allInsertChapterSpineFileModels);

            // 更新文本列表
            avgChapterModel.setTextList(modifiedTextList);
        }
    }

    private void processAndInsertTextModels(AvgChapterModel.TextModel parentTextModel, List<AvgChapterModel.TextModel> insertTextModels,
                                            List<AvgChapterModel.TextModel> modifiedTextList, int insertIndex) {
        String parentTextId = parentTextModel.getTextId();
        List<String> parentNextIds = parentTextModel.getNextIds();

        if (parentNextIds == null) {
            parentNextIds = new ArrayList<>();
        }

        String childFirstTextId = null;

        for (AvgChapterModel.TextModel insertTextModel : insertTextModels) {
            String originalTextId = insertTextModel.getTextId();
            String newTextId = parentTextId + "-" + originalTextId;
            insertTextModel.setTextId(newTextId);

            if (childFirstTextId == null) {
                childFirstTextId = newTextId;
                parentTextModel.setNextIds(Lists.newArrayList(childFirstTextId));
            }

            // 调整下一个ID
            adjustNextIds(insertTextModel, parentTextId, parentNextIds);

            // 调整对话选项
            adjustDialogueOptions(insertTextModel, parentTextId);

            // 处理插入章节的返回逻辑
            handleInsertChapterBack(insertTextModel, parentNextIds);

            // 处理循环下一个文本ID
            handleLoopNextTextId(insertTextModel, parentTextId);
        }

        // 将处理后的插入文本模型插入到列表中
        modifiedTextList.addAll(insertIndex + 1, insertTextModels);
    }

    private void adjustNextIds(AvgChapter.Text insertChapterTextModel, String parentTextId, List<String> parentNextIds) {
        List<String> nextIds = insertChapterTextModel.getNextIds();
        int nextSize = CollectionUtils.size(nextIds);
        if (nextSize == 0) {
            insertChapterTextModel.setNextIds(parentNextIds);
        } else {
            List<String> newNextIds = new ArrayList<>();
            for (String nextId : nextIds) {
                newNextIds.add(parentTextId + "-" + nextId);
            }
            insertChapterTextModel.setNextIds(newNextIds);
        }
    }

    private void adjustNextIds(AvgChapterModel.TextModel insertChapterTextModel, String parentTextId, List<String> parentNextIds) {
        List<String> nextIds = insertChapterTextModel.getNextIds();
        int nextSize = CollectionUtils.size(nextIds);
        if (nextSize == 0) {
            insertChapterTextModel.setNextIds(parentNextIds);
        } else {
            List<String> newNextIds = new ArrayList<>();
            for (String nextId : nextIds) {
                newNextIds.add(parentTextId + "-" + nextId);
            }
            insertChapterTextModel.setNextIds(newNextIds);
        }
    }

    private void adjustDialogueOptions(AvgChapter.Text text, String parentTextId) {
        String dialogue = text.getDialogue();
        if (StringUtils.isNotBlank(dialogue) && (dialogue.contains("|") || dialogue.equals(HIDE_OPTIONS))) {
            if (dialogue.equals(HIDE_OPTIONS)) {
                int optionLength = CollectionUtils.size(text.getNextIds());
                dialogue = String.join("|", Collections.nCopies(optionLength, HIDE_OPTIONS_CN));
            }
            String copyDialogue = dialogue;
            String[] dialogueArr = copyDialogue.split("\\|");
            List<AvgChapterModel.DialogueOption> dialogueOptions = new ArrayList<>();
            List<String> nextIds = text.getNextIds();
            String unlockStr = text.getUnlock();
            String[] unlockList = new String[0];
            if (StringUtils.isNotBlank(unlockStr)) {
                unlockList = unlockStr.split("\\|");
            }
            String valueStr = text.getValue();
            String[] valueList = new String[0];
            if (StringUtils.isNotBlank(valueStr)) {
                valueList = valueStr.split("\\|");
            }
            String onetimeStr = text.getOnetime();
            String[] onetimeList = new String[0];
            if (StringUtils.isNotBlank(onetimeStr)) {
                onetimeList = onetimeStr.split("\\|");
            }
            for (int i = 0; i < dialogueArr.length; i++) {
                AvgChapterModel.DialogueOption dialogueOption = new AvgChapterModel.DialogueOption().setDialogue(dialogueArr[i])
                        .setNextId(nextIds.get(i))
                        .setOptionId(i)
                        //设置默认值，如果有设置再覆盖
                        .setUnlockCondition(AvgTextUnlockCondition.NOT_ONLINE.getCode())
                        .setValueCondition(AvgTextValueCondition.NO_CHANGE.getCode())
                        .setOnetime(false);
                //如果unlockList不为空，设置解锁条件
                if (unlockList.length > 0 && unlockList.length == dialogueArr.length) {
                    dialogueOption.setUnlockCondition(Integer.parseInt(unlockList[i]));
                }
                //如果valueList不为空，设置数值变化
                if (valueList.length > 0 && valueList.length == dialogueArr.length) {
                    dialogueOption.setValueCondition(Integer.parseInt(valueList[i]));
                }
                if (onetimeList.length > 0 && onetimeList.length == dialogueArr.length) {
                    dialogueOption.setOnetime("1".equals(onetimeList[i]));
                }
                dialogueOption.setHideOptions(HIDE_OPTIONS_CN.equals(dialogueArr[i]));
                dialogueOptions.add(dialogueOption);
            }
            if (CollectionUtils.isNotEmpty(dialogueOptions)) {
                for (AvgChapterModel.DialogueOption dialogueOption : dialogueOptions) {
                    String optionNextId = dialogueOption.getNextId();
                    dialogueOption.setNextId(parentTextId + "-" + optionNextId);
                }
            }
        }
    }

    private void adjustDialogueOptions(AvgChapterModel.TextModel insertChapterTextModel, String parentTextId) {
        List<AvgChapterModel.DialogueOption> dialogueOptions = insertChapterTextModel.getDialogueOptions();
        if (CollectionUtils.isNotEmpty(dialogueOptions)) {
            for (AvgChapterModel.DialogueOption dialogueOption : dialogueOptions) {
                String optionNextId = dialogueOption.getNextId();
                dialogueOption.setNextId(parentTextId + "-" + optionNextId);
            }
        }
    }

    private void handleInsertChapterBack(AvgChapterModel.TextModel insertChapterTextModel, List<String> parentNextIds) {
        if (insertChapterTextModel.isInsertChapterBack()) {
            insertChapterTextModel.setInsertChapterBackId(parentNextIds.get(0));
        }
    }

    private void handleLoopNextTextId(AvgChapterModel.TextModel insertChapterTextModel, String parentTextId) {
        String loopNextTextId = insertChapterTextModel.getLoopNextTextId();
        if (StringUtils.isNotBlank(loopNextTextId)) {
            insertChapterTextModel.setLoopNextTextId(parentTextId + "-" + loopNextTextId);
        }
    }

    private void insertChapterTextModelsIntoList(List<Pair<Integer, List<AvgChapterModel.TextModel>>> indexInsertChapterTextList,
                                                 List<AvgChapterModel.TextModel> textList) {
        for (Pair<Integer, List<AvgChapterModel.TextModel>> indexInsertChapterTexts : indexInsertChapterTextList) {
            Integer index = indexInsertChapterTexts.getLeft();
            List<AvgChapterModel.TextModel> insertChapterTextModels = indexInsertChapterTexts.getRight();
            textList.addAll(index + 1, insertChapterTextModels);
        }
    }

    private void mergeSpineFiles(AvgChapterModel avgChapterModel, List<AvgChapterModel.SpineFileModel> allInsertChapterSpineFileModels) {
        List<AvgChapterModel.SpineFileModel> spineFiles = avgChapterModel.getSpineFiles();
        if (CollectionUtils.isNotEmpty(spineFiles)) {
            spineFiles.addAll(allInsertChapterSpineFileModels);
        } else {
            avgChapterModel.setSpineFiles(allInsertChapterSpineFileModels);
        }
    }

    private Map<String, CommonVideo> getHighlightVideoMap(Map<String, AvgOriginFile> avgOriginFileMap) {
        List<String> videoIds = avgOriginFileMap.values()
                .stream()
                .filter(v -> v.getType() == AvgFileType.HIGHLIGHT_VIDEO.getCode() || v.getType() == AvgFileType.DYNAMIC_BACKGROUND.getCode())
                .map(AvgOriginFile::getConfig)
                .filter(Objects::nonNull)
                .map(AvgOriginFile.Config::getVideoId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return avgRepository.batchQueryAvgCommonVideo(videoIds).stream().collect(Collectors.toMap(CommonVideo::getVideoId, Function.identity()));
    }

    /**
     * 将陀螺仪里配置的视频添加到原始文件映射中
     */
    private void addGyroscopeVideosToOriginFileMap(Map<String, AvgGyroscope> avgGyroscopeMap, Map<String, AvgOriginFile> avgOriginFileMap) {
        if (MapUtils.isNotEmpty(avgGyroscopeMap)) {
            Set<String> videoNames = avgGyroscopeMap.values()
                    .stream()
                    .filter(ele -> ele.getConfig() != null && CollectionUtils.isNotEmpty(ele.getConfig().getGyroscopes()))
                    .flatMap(ele -> ele.getConfig().getGyroscopes().stream())
                    .map(AvgGyroscope.GyroscopeItem::getImmediateVideo)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            Map<String, AvgOriginFile> gyroscopeVideoMap = avgRepository.queryAvgOriginFileByNamesFromCache(videoNames);
            avgOriginFileMap.putAll(gyroscopeVideoMap);
        }
    }

    public Map<String, CommonAudio> getCommonAudioMap(Map<String, AvgOriginFile> avgOriginFileMap) {
        List<String> audioIds = avgOriginFileMap.values()
                .stream()
                .filter(value -> value.getType() == AvgFileType.BGM.getCode()
                        || value.getType() == AvgFileType.SOUND_EFFECT.getCode()
                        || value.getType() == AvgFileType.CV.getCode()
                        || value.getType() == AvgFileType.HIGHLIGHT_VIDEO_VOICE.getCode())
                .map(AvgOriginFile::getConfig)
                .filter(Objects::nonNull)
                .map(AvgOriginFile.Config::getAudioId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<CommonAudio> commonAudios = avgRepository.queryCommonAudioBatchByAudioIds(audioIds);
        return commonAudios.stream().collect(Collectors.toMap(CommonAudio::getAudioId, Function.identity()));
    }

    public AvgChapterRecordModel queryAvgRecord(int userId, int chapterId) {
        UserAvgChapterRecord userAvgChapterRecord = avgRepository.queryRecordByChapterId(userId, chapterId);
        return AvgChapterRecordModel.valueOf(userAvgChapterRecord);
    }

    public List<String> getPreTextIds(int chapterId, String textId) {
        Map<Integer, AvgChapterModel> avgChapterModelMap = batchQueryAvgChapter(Collections.singletonList(chapterId), false);
        if (MapUtils.isEmpty(avgChapterModelMap)) {
            return Collections.emptyList();
        }
        AvgChapterModel avgChapterModel = avgChapterModelMap.get(chapterId);
        if (avgChapterModel == null) {
            return Collections.emptyList();
        }
        return AvgChapterModel.findAllPredecessorsV2(chapterId, avgChapterModel.getTextList(), textId);
    }


    public boolean recordAvgChapter(AvgRecordParam param) {
        Message message = new Message();
        message.setTopic(MqConstant.TOPIC_AVG_RECORD_TOPIC);
        AvgRecordMessage avgRecordMessage = new AvgRecordMessage().setUserId(param.getUserId())
                .setChapterId(param.getChapterId())
                .setLastPlayTextId(param.getLastPlayTextId())
                .setSelectedOptions(param.getSelectedOptions())
                .setReadTime(new Date())
                .setCustomParam(param.getCustomParam());
        String body = com.kuaikan.common.tools.serialize.JsonUtils.toJson(avgRecordMessage);
        message.setBody(body.getBytes());
        try {
            SendResult send = avgRecordProducer.getDefaultMQProducer().send(message);
            log.debug("recordAvgChapter send result:{}", send.getMsgId());
            return true;
        } catch (Exception e) {
            log.error("recordAvgChapter error, param:{}", param, e);
        }
        return false;
    }

    // 专门的模型类，用于封装查询结果
    @Data
    @Accessors(chain = true)
    public static class ChapterQueryResults {

        private Map<String, AvgOriginFile> avgOriginFileMap;
        private Map<String, AvgHotZone> avgHotZoneMap;
        private Map<String, AvgGyroscope> avgGyroscopeMap;
        private Map<String, AvgDir> dynamicRoleName2DirIdMap;
        private Map<String, AvgDir> spineNameMap;
        private Map<String, AvgDir> qSpineNameMap;
        private Map<Integer, List<AvgOriginFile>> parentId2FileList;
        private Map<String, CommonVideo> commonVideoMap;
        private Map<String, CommonAudio> commonAudioMap;
        private List<AvgChapter> allChapters;
        private Map<String, AvgProject> projectIdToAvgProjectMap;

        public ChapterQueryResults() {
            this.avgOriginFileMap = new HashMap<>();
            this.avgHotZoneMap = new HashMap<>();
            this.avgGyroscopeMap = new HashMap<>();
            this.dynamicRoleName2DirIdMap = new HashMap<>();
            this.spineNameMap = new HashMap<>();
            this.qSpineNameMap = new HashMap<>();
            this.parentId2FileList = new HashMap<>();
            this.commonVideoMap = new HashMap<>();
            this.commonAudioMap = new HashMap<>();
            this.allChapters = new ArrayList<>();
            this.projectIdToAvgProjectMap = new HashMap<>();
        }

        public ChapterQueryResults(Map<String, AvgOriginFile> avgOriginFileMap, Map<String, AvgHotZone> avgHotZoneMap,
                                   Map<String, AvgGyroscope> avgGyroscopeMap, Map<String, AvgDir> dynamicRoleName2DirIdMap, Map<String, AvgDir> spineNameMap,
                                   Map<String, AvgDir> qSpineNameMap, Map<Integer, List<AvgOriginFile>> parentId2FileList,
                                   Map<String, CommonVideo> commonVideoMap, Map<String, CommonAudio> commonAudioMap, List<AvgChapter> allChapters,
            Map<String, AvgProject> projectIdToAvgProjectMap) {
            this.avgOriginFileMap = avgOriginFileMap;
            this.avgHotZoneMap = avgHotZoneMap;
            this.avgGyroscopeMap = avgGyroscopeMap;
            this.dynamicRoleName2DirIdMap = dynamicRoleName2DirIdMap;
            this.spineNameMap = spineNameMap;
            this.qSpineNameMap = qSpineNameMap;
            this.parentId2FileList = parentId2FileList;
            this.commonVideoMap = commonVideoMap;
            this.commonAudioMap = commonAudioMap;
            this.allChapters = allChapters;
            this.projectIdToAvgProjectMap = projectIdToAvgProjectMap;
        }

        public Map<String, AvgOriginFile> getAvgOriginFileMap() {
            return avgOriginFileMap;
        }

        public Map<String, AvgHotZone> getAvgHotZoneMap() {
            return avgHotZoneMap;
        }

        public Map<String, AvgGyroscope> getAvgGyroscopeMap() {
            return avgGyroscopeMap;
        }

        public Map<String, AvgDir> getDynamicRoleName2DirIdMap() {
            return dynamicRoleName2DirIdMap;
        }

        public Map<String, AvgDir> getSpineNameMap() {
            return spineNameMap;
        }

        public Map<String, AvgDir> getqSpineNameMap() {
            return qSpineNameMap;
        }

        public Map<Integer, List<AvgOriginFile>> getParentId2FileList() {
            return parentId2FileList;
        }

        public Map<String, CommonVideo> getCommonVideoMap() {
            return commonVideoMap;
        }

        public Map<String, CommonAudio> getCommonAudioMap() {
            return commonAudioMap;
        }

        public List<AvgChapter> getAllChapters() {
            return allChapters;
        }
    }
}


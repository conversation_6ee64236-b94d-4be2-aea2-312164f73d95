package com.kuaikan.role.game.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import com.google.common.collect.Lists;

import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity;
import com.kuaikan.role.game.api.bean.Furniture;
import com.kuaikan.role.game.api.bean.FurnitureActivityConfig;
import com.kuaikan.role.game.api.bean.FurnitureActivityFurnitureGroupRelation;
import com.kuaikan.role.game.api.bean.FurnitureGroup;
import com.kuaikan.role.game.api.bean.FurnitureGroupRelation;
import com.kuaikan.role.game.api.bean.FurnitureGroupRoleGroupRelation;
import com.kuaikan.role.game.api.bean.RewardOrder;
import com.kuaikan.role.game.api.bean.RewardOrderBlindBoxExtraInfo;
import com.kuaikan.role.game.api.bean.RewardOrderFurnitureActivityExtraInfo;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.UserCostume;
import com.kuaikan.role.game.api.bean.UserFurnitureActivityRewardRecord;
import com.kuaikan.role.game.api.bean.UserFurnitureBlindBoxLotteryRecord;
import com.kuaikan.role.game.api.bean.UserFurnitureCouponRecord;
import com.kuaikan.role.game.api.bo.FurnitureDrawConfig;
import com.kuaikan.role.game.api.bo.ImageInfo;
import com.kuaikan.role.game.api.enums.FurnitureActivityType;
import com.kuaikan.role.game.api.enums.FurnitureType;
import com.kuaikan.role.game.api.enums.RewardOrderStatus;
import com.kuaikan.role.game.api.enums.UserFurnitureCouponType;
import com.kuaikan.role.game.api.model.BlindBoxActivityModel;
import com.kuaikan.role.game.api.model.BlindBoxActivityRecordModel;
import com.kuaikan.role.game.api.model.FurnitureActivityDetailModel;
import com.kuaikan.role.game.api.model.FurnitureActivityModel;
import com.kuaikan.role.game.api.model.FurnitureActivityModel.PrizeInfoModel;
import com.kuaikan.role.game.api.model.FurnitureActivityPreviewModel;
import com.kuaikan.role.game.api.model.FurnitureBlindBoxLotteryRecordModel;
import com.kuaikan.role.game.api.model.FurnitureGroupActivityModel;
import com.kuaikan.role.game.api.model.FurnitureGroupModel;
import com.kuaikan.role.game.api.model.GuaranteeRemainDrawsResult;
import com.kuaikan.role.game.api.model.RoleAnimationModel;
import com.kuaikan.role.game.api.model.RoleGroupIconActivityModel;
import com.kuaikan.role.game.api.model.SimpleFurnitureActivityModel;
import com.kuaikan.role.game.api.model.UserBlindBoxActivityInfoModel;
import com.kuaikan.role.game.api.rpc.param.ActivityPrizeParam;
import com.kuaikan.role.game.api.rpc.param.BlindBoxActivityRecordParam;
import com.kuaikan.role.game.api.rpc.param.FurnitureActivityDrawListQueryParam;
import com.kuaikan.role.game.api.rpc.param.FurnitureActivityQueryParam;
import com.kuaikan.role.game.api.service.FurnitureService;
import com.kuaikan.role.game.component.RoleGroupComponent;
import com.kuaikan.role.game.component.SaComponent;
import com.kuaikan.role.game.component.UserFurnitureComponent;
import com.kuaikan.role.game.config.ApolloConfig;
import com.kuaikan.role.game.repository.FurnitureActivityConfigRepository;
import com.kuaikan.role.game.repository.FurnitureActivityFurnitureGroupRelationRepository;
import com.kuaikan.role.game.repository.FurnitureActivityRuleConfigRepository;
import com.kuaikan.role.game.repository.FurnitureGroupRelationRepository;
import com.kuaikan.role.game.repository.FurnitureGroupRepository;
import com.kuaikan.role.game.repository.FurnitureGroupRoleGroupRelationRepository;
import com.kuaikan.role.game.repository.FurnitureRepository;
import com.kuaikan.role.game.repository.RewardOrderRepository;
import com.kuaikan.role.game.repository.UserCostumeRepository;
import com.kuaikan.role.game.repository.UserFurnitureActivityRewardRecordRepository;
import com.kuaikan.role.game.repository.UserFurnitureBlindBoxLotteryRecordRepository;
import com.kuaikan.role.game.repository.UserFurnitureCouponRecordRepository;

/**
 * @author: zhangjianwu
 * @date: 2025/4/15 11:35
 */

@Slf4j
@DubboService(version = "1.0", group = "role-game")
public class FurnitureServiceImpl implements FurnitureService {

    @Resource
    private FurnitureActivityConfigRepository furnitureActivityConfigRepository;

    @Resource
    private RewardOrderRepository rewardOrderRepository;

    @Resource
    private UserFurnitureCouponRecordRepository userFurnitureCouponRecordRepository;

    @Resource
    private ApolloConfig apolloConfig;

    @Resource
    private UserFurnitureBlindBoxLotteryRecordRepository furnitureBlindBoxLotteryRecordRepository;

    @Resource
    private FurnitureRepository furnitureRepository;

    @Resource
    private FurnitureGroupRelationRepository furnitureGroupRelationRepository;
    @Resource
    private FurnitureGroupRepository furnitureGroupRepository;
    @Resource
    private UserFurnitureActivityRewardRecordRepository userFurnitureActivityRewardRecordRepository;
    @Resource
    private SaComponent saComponent;
    @Resource
    private FurnitureActivityFurnitureGroupRelationRepository furnitureActivityFurnitureGroupRelationRepository;
    @Resource
    private FurnitureGroupRoleGroupRelationRepository furnitureGroupRoleGroupRelationRepository;
    @Resource
    private RoleGroupComponent roleGroupComponent;
    @Resource
    private UserFurnitureComponent userFurnitureComponent;

    @Resource
    private FurnitureActivityRuleConfigRepository furnitureActivityRuleConfigRepository;
    @Resource
    private UserCostumeRepository userCostumeRepository;

    // 抽数档位
    private static final int DRAW_COUNT_LEVEL_1 = 1;
    private static final int DRAW_COUNT_LEVEL_10 = 10;

    @Override
    public RpcResult<FurnitureActivityModel> getActivityInfo(FurnitureActivityQueryParam queryParam) {
        int activityId = queryParam.getActivityId();
        if (activityId <= 0) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST);
        }
        FurnitureActivityConfig furnitureActivityConfig = furnitureActivityConfigRepository.selectActivityByIdFromCache(activityId);
        if (furnitureActivityConfig == null || furnitureActivityConfig.getEndAt() < System.currentTimeMillis()) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "活动不存在或者已过期");
        }
        // 当前家具活动订单
        List<RewardOrder> rewardOrders = rewardOrderRepository.queryFurnitureActivityOrder(queryParam.getUserId(), activityId);
        Optional<RewardOrder> hadFinishOrder = rewardOrders.stream().filter(rewardOrder -> {
            String extraInfo = rewardOrder.getExtraInfo();
            RewardOrderFurnitureActivityExtraInfo rewardOrderFurnitureActivityExtraInfo = JsonUtils.findObject(extraInfo,
                    RewardOrderFurnitureActivityExtraInfo.class);
            return rewardOrder.getStatus() == RewardOrderStatus.FINISHED.getCode() && rewardOrderFurnitureActivityExtraInfo.getDrawNum() == 1;
        }).findAny();
        boolean hadFreeFirst = !hadFinishOrder.isPresent();

        UserFurnitureCouponType couponType = Objects.equals(furnitureActivityConfig.getType(), FurnitureActivityType.FURNITURE_ACTIVITY_LIMIT.getCode())
                ? UserFurnitureCouponType.SPECIFIC
                : UserFurnitureCouponType.DAILY;

        List<UserFurnitureCouponRecord> userActivityCouponRecords = userFurnitureCouponRecordRepository.selectUseEffectiveCoupons(queryParam.getUserId(),
                Collections.singleton(couponType.getCode()));
        FurnitureActivityConfig.Config config = furnitureActivityConfig.getConfig();
        FurnitureActivityModel.PriceDetailModel leve1PriceDetailModel = getPriceDetailModel(DRAW_COUNT_LEVEL_1, (hadFreeFirst && config.isFirstFree()),
                userActivityCouponRecords);
        FurnitureActivityModel.PriceDetailModel leve10PriceDetailModel = getPriceDetailModel(DRAW_COUNT_LEVEL_10, (hadFreeFirst && config.isFirstFree()),
                userActivityCouponRecords);

        FurnitureActivityModel furnitureActivityModel = new FurnitureActivityModel();
        List<FurnitureActivityModel.PriceDetailModel> priceDetailModelList = new ArrayList<>();
        priceDetailModelList.add(leve1PriceDetailModel);
        priceDetailModelList.add(leve10PriceDetailModel);
        furnitureActivityModel.setPriceList(priceDetailModelList);
        furnitureActivityModel.setFurnitureActivityDetailModel(FurnitureActivityDetailModel.valueOf(furnitureActivityConfig));
        furnitureActivityModel.setNextReward(getNextReward(furnitureActivityConfig, rewardOrders));
        return RpcResult.success(furnitureActivityModel);
    }

    private PrizeInfoModel getNextReward(FurnitureActivityConfig furnitureActivityConfig, List<RewardOrder> rewardOrders) {
        if (Objects.equals(furnitureActivityConfig.getType(), FurnitureActivityType.FURNITURE_ACTIVITY_NORMAL.getCode())) {
            return null;
        }
        FurnitureActivityConfig.CumulativeDrawReward cumulativeDrawReward = furnitureActivityConfig.getConfig().getCumulativeDrawReward();
        if (cumulativeDrawReward == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(rewardOrders)) {
            return new PrizeInfoModel().setImage(cumulativeDrawReward.getRewardImage()).setDesc(cumulativeDrawReward.getRewardText());
        }
        int lotteryCount = rewardOrders.stream()
                .map(RewardOrder::getExtraInfo)
                .map(extraInfoStr -> JsonUtils.findObject(extraInfoStr, RewardOrderFurnitureActivityExtraInfo.class))
                .filter(Objects::nonNull)
                .mapToInt(extraInfo -> Optional.ofNullable(extraInfo.getLotteryRecordBids()).orElse(Lists.newArrayList()).size())
                .sum();
        if (0 == lotteryCount) {
            return new PrizeInfoModel().setImage(cumulativeDrawReward.getRewardImage()).setDesc(cumulativeDrawReward.getRewardText());
        }
        for (CostumeBlindBoxActivity.Reward reward : cumulativeDrawReward.getRewards()) {
            if (reward.getNeedNum() > lotteryCount) {
                return new PrizeInfoModel().setImage(reward.getRewardImage())
                        .setDesc(String.format("还差%s抽获得\n「%s」", reward.getNeedNum() - lotteryCount, reward.getName()));
            }
        }
        return new PrizeInfoModel().setImage(cumulativeDrawReward.getRewardImage()).setDesc(cumulativeDrawReward.getRewardText());
    }

    @Override
    public RpcResult<List<FurnitureBlindBoxLotteryRecordModel>> drawList(FurnitureActivityDrawListQueryParam queryParam) {
        List<UserFurnitureBlindBoxLotteryRecord> furnitureBlindBoxLotteryRecords = furnitureBlindBoxLotteryRecordRepository.selectByUserIdAndActivityId(
                queryParam.getUserId(), queryParam.getActivityId());

        Set<Integer> furnitureIds = furnitureBlindBoxLotteryRecords.stream()
                .map(UserFurnitureBlindBoxLotteryRecord::getFurnitureId)
                .collect(Collectors.toSet());
        Map<Integer, Furniture> furnitureMap = furnitureRepository.selectByIds(furnitureIds);
        Map<Integer, FurnitureGroupRelation> furniture2Group = furnitureGroupRelationRepository.queryByFurnitureIds(furnitureIds)
                .stream()
                .collect(Collectors.toMap(FurnitureGroupRelation::getFurnitureId, value -> value));
        Set<Integer> furnitureGroupIds = furniture2Group.values().stream().map(FurnitureGroupRelation::getFurnitureGroupId).collect(Collectors.toSet());
        List<FurnitureGroup> furnitureGroupList = furnitureGroupRepository.getByIds(furnitureGroupIds);
        Map<Integer, FurnitureGroup> furnitureGroupMap = furnitureGroupList.stream().collect(Collectors.toMap(FurnitureGroup::getId, Function.identity()));
        List<FurnitureBlindBoxLotteryRecordModel> furnitureBlindBoxLotteryRecordModels = new ArrayList<>();
        for (UserFurnitureBlindBoxLotteryRecord furnitureBlindBoxLotteryRecord : furnitureBlindBoxLotteryRecords) {
            FurnitureBlindBoxLotteryRecordModel furnitureBlindBoxLotteryRecordModel = new FurnitureBlindBoxLotteryRecordModel();
            String furnitureName = Optional.ofNullable(furnitureMap.get(furnitureBlindBoxLotteryRecord.getFurnitureId())).map(Furniture::getName).orElse("");
            furnitureBlindBoxLotteryRecordModel.setUserId(furnitureBlindBoxLotteryRecord.getUserId())
                    .setActivityId(furnitureBlindBoxLotteryRecord.getActivityId())
                    .setFurnitureId(furnitureBlindBoxLotteryRecord.getFurnitureId())
                    .setFurnitureName(furnitureName);
            FurnitureGroupRelation furnitureGroupRelation = furniture2Group.get(furnitureBlindBoxLotteryRecord.getFurnitureId());
            if (furnitureGroupRelation != null) {
                FurnitureGroup furnitureGroup = furnitureGroupMap.get(furnitureGroupRelation.getFurnitureGroupId());
                if (furnitureGroup != null) {
                    furnitureBlindBoxLotteryRecordModel.setFurnitureGroupId(furnitureGroupRelation.getFurnitureGroupId());
                    furnitureBlindBoxLotteryRecordModel.setFurnitureGroupName(furnitureGroup.getName());
                }
            }
            furnitureBlindBoxLotteryRecordModels.add(furnitureBlindBoxLotteryRecordModel);
        }
        return RpcResult.success(furnitureBlindBoxLotteryRecordModels);
    }

    public FurnitureActivityModel.PriceDetailModel getPriceDetailModel(int drawCount, boolean hadFreeFirst,
                                                                       List<UserFurnitureCouponRecord> userActivityCouponRecords) {
        FurnitureDrawConfig furnitureDrawConfig = apolloConfig.getFurnitureDrawConfig();
        if (drawCount == DRAW_COUNT_LEVEL_1) {
            if (hadFreeFirst) {
                // 一抽免费
                return new FurnitureActivityModel.PriceDetailModel().setLevel(DRAW_COUNT_LEVEL_1)
                        .setOriginPrice(furnitureDrawConfig.getOneDrawGiftNumber())
                        .setNeedKkbNum(0)
                        .setGiftId(furnitureDrawConfig.getFreeDrawGiftId())
                        .setGiftCount(1);
            } else {
                // 一抽付费
                if (CollectionUtils.size(userActivityCouponRecords) > 0) {
                    // 带折扣一抽
                    return new FurnitureActivityModel.PriceDetailModel().setLevel(DRAW_COUNT_LEVEL_1)
                            .setNeedKkbNum(0)
                            .setOriginPrice(furnitureDrawConfig.getOneDrawGiftNumber())
                            .setCouponBidList(Collections.singletonList(userActivityCouponRecords.get(0).getBid()))
                            .setGiftId(furnitureDrawConfig.getOneDrawGiftId())
                            .setGiftCount(0);
                } else {
                    // 正常一抽价格
                    return new FurnitureActivityModel.PriceDetailModel().setLevel(DRAW_COUNT_LEVEL_1)
                            .setNeedKkbNum(furnitureDrawConfig.getOneDrawGiftNumber())
                            .setOriginPrice(furnitureDrawConfig.getOneDrawGiftNumber())
                            .setGiftId(furnitureDrawConfig.getOneDrawGiftId())
                            .setGiftCount(furnitureDrawConfig.getOneDrawGiftNumber());
                }
            }

        } else if (drawCount == DRAW_COUNT_LEVEL_10) {
            int originPrice = DRAW_COUNT_LEVEL_10 * furnitureDrawConfig.getOneDrawGiftNumber();
            List<Long> couponBidList = new ArrayList<>();
            // 有折扣券抵扣
            if (CollectionUtils.size(userActivityCouponRecords) > 0) {
                // 最多抵扣十次
                if (CollectionUtils.size(userActivityCouponRecords) >= DRAW_COUNT_LEVEL_10) {
                    couponBidList = userActivityCouponRecords.subList(0, DRAW_COUNT_LEVEL_10)
                            .stream()
                            .map(UserFurnitureCouponRecord::getBid)
                            .collect(Collectors.toList());
                } else {
                    couponBidList = userActivityCouponRecords.subList(0, CollectionUtils.size(userActivityCouponRecords))
                            .stream()
                            .map(UserFurnitureCouponRecord::getBid)
                            .collect(Collectors.toList());
                }
            }
            int discountPrice = CollectionUtils.size(couponBidList) * furnitureDrawConfig.getOneDrawGiftNumber();
            int totalNeedPrice = Math.max(0, originPrice - discountPrice);
            return new FurnitureActivityModel.PriceDetailModel().setLevel(DRAW_COUNT_LEVEL_10)
                    .setOriginPrice(originPrice)
                    .setNeedKkbNum(totalNeedPrice)
                    .setCouponBidList(CollectionUtils.isNotEmpty(couponBidList) ? couponBidList : null)
                    .setGiftId(furnitureDrawConfig.getOneDrawGiftId())
                    .setGiftCount(totalNeedPrice);
        }
        return null;
    }

    @Override
    public RpcResult<FurnitureActivityPreviewModel> getOnlineActivityPreviewList() {
        FurnitureActivityPreviewModel furnitureActivityPreviewModel = new FurnitureActivityPreviewModel();
        List<FurnitureActivityConfig> onlineActivityList = furnitureActivityConfigRepository.queryOnlineActivityList();
        if (CollectionUtils.isEmpty(onlineActivityList)) {
            return RpcResult.success(furnitureActivityPreviewModel);
        }
        furnitureActivityPreviewModel.setSimpleFurnitureActivityModels(
                onlineActivityList.stream().map(SimpleFurnitureActivityModel::valueOf).collect(Collectors.toList()));
        return RpcResult.success(furnitureActivityPreviewModel);
    }

    @Override
    public RpcResult<BlindBoxActivityModel> queryFurnitureActivityById(ActivityPrizeParam param) {
        FurnitureActivityConfig activity = furnitureActivityConfigRepository.selectActivityByIdFromCache(param.getActivityId());
        if (activity == null) {
            log.warn("query furniture activity not found, activityId:{}", param.getActivityId());
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "家具盲盒活动不存在");
        }
        if (!activityInValidity(activity)) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "家具盲盒活动不在有效期");
        }
        final List<UserCostume> userCostumes = userCostumeRepository.queryByUserIdFromCache(param.getUserId());
        if (CollectionUtils.isEmpty(userCostumes)) {
            log.warn("No costumes found for user: {}", param.getUserId());
        }
        List<Integer> userCostumeIds = Optional.ofNullable(userCostumes)
                .orElse(Collections.emptyList())
                .stream()
                .map(UserCostume::getCostumeId)
                .collect(Collectors.toList());
        final List<Furniture> furnitureList = furnitureRepository.queryAllFromCache();
        if (CollectionUtils.isEmpty(furnitureList)) {
            log.warn("No furniture found");
        }
        List<Integer> interactiveFurnitureIds = Optional.ofNullable(furnitureList)
                .orElse(Collections.emptyList())
                .stream()
                .filter(furniture -> furniture.getType() == FurnitureType.INTERACTIVE.getCode())
                .map(Furniture::getId)
                .collect(Collectors.toList());
        BlindBoxActivityModel blindBoxActivityModel = BlindBoxActivityModel.valueOf(activity);
        blindBoxActivityModel.setInteractiveFurnitureIds(interactiveFurnitureIds);
        blindBoxActivityModel.setUserCostumeIds(userCostumeIds);
        return RpcResult.success(blindBoxActivityModel);
    }

    @Override
    public RpcResult<UserBlindBoxActivityInfoModel> queryFurnitureActivityRewardRecord(ActivityPrizeParam param) {
        FurnitureActivityConfig activity = furnitureActivityConfigRepository.selectActivityByIdFromCache(param.getActivityId());
        if (activity == null) {
            log.warn("query furniture activity not found, activityId:{}", param.getActivityId());
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "家具盲盒活动不存在");
        }
        if (!activityInValidity(activity)) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "家具盲盒活动不在有效期");
        }
        UserBlindBoxActivityInfoModel userBlindBoxActivityInfoModel = new UserBlindBoxActivityInfoModel();
        userBlindBoxActivityInfoModel.setBlindBoxActivityModel(BlindBoxActivityModel.valueOf(activity));
        List<UserFurnitureActivityRewardRecord> rewardRecords = userFurnitureActivityRewardRecordRepository.queryByUserIdAndActivityId(param.getUserId(),
                param.getActivityId());
        if (CollectionUtils.isEmpty(rewardRecords)) {
            log.warn("query furniture activity reward record not found, activityId:{}, userId:{}", param.getActivityId(), param.getUserId());
        }
        List<BlindBoxActivityRecordModel> rewardRecordModels = Lists.newArrayListWithExpectedSize(rewardRecords.size());
        for (UserFurnitureActivityRewardRecord record : rewardRecords) {
            rewardRecordModels.add(BlindBoxActivityRecordModel.valueOf(record));
        }
        userBlindBoxActivityInfoModel.setBlindBoxActivityRecordModels(rewardRecordModels);
        return RpcResult.success(userBlindBoxActivityInfoModel);
    }

    @Override
    public RpcResult<Void> insertUserFurnitureActivityRecord(BlindBoxActivityRecordParam activityRecordParam) {
        UserFurnitureActivityRewardRecord rewardRecord = buildRewardRecord(activityRecordParam);
        int result = userFurnitureActivityRewardRecordRepository.insert(rewardRecord);
        if (result <= 0) {
            log.error("insert user furniture activity record failed, result:{}, activityRecordParam:{}", result, activityRecordParam);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "写入用户家具盲盒活动记录失败");
        }
        List<RewardOrder> rewardOrders = rewardOrderRepository.queryFurnitureActivityOrder(activityRecordParam.getUserId(),
                activityRecordParam.getActivityId());
        if (CollectionUtils.isEmpty(rewardOrders)) {
            log.error("trackActivityPrizeData error, rewardOrders is empty, trackActivityPrizeParam={}", activityRecordParam);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "当前活动内领奖时的抽数为空");
        }
        int lotteryCountSum = rewardOrders.stream()
                .map(RewardOrder::getExtraInfo)
                .map(extraInfoStr -> JsonUtils.findObject(extraInfoStr, RewardOrderBlindBoxExtraInfo.class))
                .filter(Objects::nonNull)
                .mapToInt(extraInfo -> extraInfo.getLotteryRecordBids().size())
                .sum();
        Map<String, Object> properties = new HashMap<>(8);
        properties.put("Action", "领奖");
        properties.put("PrizeJialiaoCount", lotteryCountSum);
        properties.put("PrizeName", activityRecordParam.getPrizeName());
        properties.put("ActivityID", activityRecordParam.getActivityId());
        properties.put("ActivityName", activityRecordParam.getActivityName());
        String selectedPrize = activityRecordParam.getSelectedPrize();
        properties.put("SelectedPrize", StringUtils.isNotBlank(selectedPrize) ? selectedPrize : "");
        saComponent.uploadEventData(activityRecordParam.getUserId(), SaComponent.FURNITURE_ACTIVITY_PRIZE, properties);
        return RpcResult.success();
    }

    @Override
    public RpcResult<GuaranteeRemainDrawsResult> getUserGuaranteeRemainDraws(int userId, int activityId) {
        FurnitureActivityConfig furnitureActivityConfig = furnitureActivityConfigRepository.selectActivityByIdFromCache(activityId);
        if (furnitureActivityConfig == null || (furnitureActivityConfig.getType() == 2 && furnitureActivityConfig.getEndAt() < System.currentTimeMillis())) {
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "活动不存在或者已过期");
        }
        boolean showGuarantee = false;
        if (furnitureActivityConfig.getConfig() != null) {
            showGuarantee = furnitureActivityConfig.getConfig().isShowGuarantee();
        }
        int countByRuleId = furnitureActivityRuleConfigRepository.countByRuleId(furnitureActivityConfig.getConfig().getRuleId());
        UserFurnitureBlindBoxLotteryRecord recentGuaranteedLotteryRecord = furnitureBlindBoxLotteryRecordRepository.queryRecentGuaranteedLotteryRecord(userId,
                activityId);
        int lastGuaranteedLotteryRecordId = 0;
        if (recentGuaranteedLotteryRecord != null) {
            lastGuaranteedLotteryRecordId = recentGuaranteedLotteryRecord.getId();
        }
        int count = furnitureBlindBoxLotteryRecordRepository.countByUserIdAndActivityIdAfterId(userId, activityId, lastGuaranteedLotteryRecordId);
        int remainDraws = countByRuleId - count;
        log.info("getUserGuaranteeRemainDraws User [{}] Activity [{}] remainDraws [{}] from {}", userId, activityId, remainDraws, count);
        return RpcResult.success(GuaranteeRemainDrawsResult.valueOf(remainDraws, showGuarantee));
    }

    @Override
    public RpcResult<Map<Integer, List<RoleGroupIconActivityModel>>> getRoleGroupDetails(ActivityPrizeParam param) {
        int activityId = param.getActivityId();

        // 获取活动对应的家具套组ID
        List<Integer> groupIds = furnitureActivityFurnitureGroupRelationRepository.queryByActivityId(activityId)
                .stream()
                .map(FurnitureActivityFurnitureGroupRelation::getFurnitureGroupId)
                .collect(Collectors.toList());
        if (groupIds.isEmpty()) {
            log.warn("getRoleGroupDetails no furniture group found for activityId: {}", activityId);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "活动没有对应的家具套组");
        }

        // 获取并过滤上线状态的家具套组
        // 只查询非【全部】的家具组
        Map<Integer, FurnitureGroup> groupMap = furnitureGroupRepository.getByIds(groupIds)
                .stream()
                .filter(g -> g.getStatus() == 1)
                .filter(e -> e.getConfig() != null && !e.getConfig().isAssociateAllRoleGroup())
                .collect(Collectors.toMap(FurnitureGroup::getId, Function.identity()));

        // 根据家具套组ID批量查询关系并按组分组
        Map<Integer, List<FurnitureGroupRoleGroupRelation>> relationsMap = furnitureGroupRoleGroupRelationRepository.getByFurnitureGroupIds(
                new ArrayList<>(groupMap.keySet())).stream().collect(Collectors.groupingBy(FurnitureGroupRoleGroupRelation::getFurnitureGroupId));

        // 收集所有角色组ID
        List<Integer> allRoleGroupIds = relationsMap.values()
                .stream()
                .flatMap(List::stream)
                .map(FurnitureGroupRoleGroupRelation::getRoleGroupId)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, List<Role>> roleMap = new HashMap<>();
        for (Integer groupId : allRoleGroupIds) {
            List<Role> roles = roleGroupComponent.queryByGroupId(groupId);
            roleMap.put(groupId, roles != null ? roles : Collections.emptyList());
        }

        Map<Integer, List<RoleGroupIconActivityModel>> roleGroupHashMap = relationsMap.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream().map(rel -> {
                    int roleGroupId = rel.getRoleGroupId();
                    List<Role> roles = roleMap.getOrDefault(roleGroupId, Collections.emptyList());
                    RoleGroupIconActivityModel model = new RoleGroupIconActivityModel();
                    model.setRoleGroupId(roleGroupId);
                    model.setRoleNames(roles.stream().map(Role::getName).collect(Collectors.toList()));
                    model.setImages(roles.stream().map(Role::getImage).collect(Collectors.toList()));
                    return model;
                }).collect(Collectors.toList())));
        return RpcResult.success(roleGroupHashMap);
    }

    @Override
    public RpcResult<List<FurnitureGroupActivityModel>> getFurnitureGroupDetails(ActivityPrizeParam param) {
        int activityId = param.getActivityId();
        int userId = param.getUserId();

        // 获取活动对应的家具套组ID
        List<Integer> groupIds = furnitureActivityFurnitureGroupRelationRepository.queryByActivityId(activityId)
                .stream()
                .map(FurnitureActivityFurnitureGroupRelation::getFurnitureGroupId)
                .collect(Collectors.toList());

        if (groupIds.isEmpty()) {
            log.warn("getFurnitureGroupDetails no furniture group found for activityId: {}", activityId);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "活动没有对应的家具套组");
        }
        // 获取并过滤上线状态的家具套组
        Map<Integer, FurnitureGroup> groupMap = furnitureGroupRepository.getByIds(groupIds)
                .stream()
                .filter(g -> g.getStatus() == 1)
                .collect(Collectors.toMap(FurnitureGroup::getId, Function.identity()));

        if (groupMap.isEmpty()) {
            log.warn("getFurnitureGroupDetails no valid furniture group found for activityId: {}", activityId);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "活动没有有效的家具套组");
        }

        // 获取每个套组对应的家具关系
        Map<Integer, List<FurnitureGroupRelation>> relationsMap = furnitureGroupRelationRepository.queryByGroupIds(new ArrayList<>(groupMap.keySet()));

        // 收集所有家具ID
        List<Integer> allFurnitureIds = relationsMap.values()
                .stream()
                .flatMap(List::stream)
                .map(FurnitureGroupRelation::getFurnitureId)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, Furniture> furnitureMap = furnitureRepository.queryByIds(allFurnitureIds)
                .stream()
                .collect(Collectors.toMap(Furniture::getId, Function.identity()));

        // 根据关系组装每个套组的家具
        Map<Integer, List<Furniture>> furnituresByGroup = relationsMap.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue()
                        .stream()
                        .map(rel -> furnitureMap.get(rel.getFurnitureId()))
                        .filter(Objects::nonNull)
                        .sorted(Comparator.comparing(Furniture::getOrderNum))
                        .collect(Collectors.toList())));

        List<FurnitureGroupActivityModel> result = furnituresByGroup.entrySet().stream().map(entry -> {
            Integer gid = entry.getKey();
            FurnitureGroupActivityModel model = new FurnitureGroupActivityModel();
            model.setFurnitureGroupId(gid);
            FurnitureGroup furnitureGroup = groupMap.get(gid);
            model.setFurnitureGroupName(furnitureGroup.getName());
            model.setOrderNum(furnitureGroup.getOrderNum());
            model.setCreateTime(furnitureGroup.getCreatedAt());
            if (ObjectUtil.isNotEmpty(furnitureGroup.getConfig().getThumbnail())) {
                model.setThumbnail(furnitureGroup.getConfig().getThumbnail());
            }
            List<FurnitureGroupActivityModel.FurnitureModel> items = entry.getValue().stream().map(f -> {
                FurnitureGroupActivityModel.FurnitureModel fm = new FurnitureGroupActivityModel.FurnitureModel();
                fm.setId(f.getId());
                fm.setImage(f.getConfig().getIcon());
                fm.setIsInteractive(f.getType());
                fm.setHasOwned(userFurnitureComponent.hasUserFurniture(userId, f.getId()));
                return fm;
            }).collect(Collectors.toList());
            model.setFurnitures(items);
            return model;
        }).collect(Collectors.toList());

        return RpcResult.success(result);
    }

    @Override
    public RpcResult<String> getActivityRuleByRuleId(int activityId) {
        FurnitureActivityConfig furnitureActivityConfig = furnitureActivityConfigRepository.selectActivityByIdFromDB(activityId);
        if (furnitureActivityConfig == null) {
            log.warn("getActivityRuleByRuleId not found, activity:{}", activityId);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "家具活动规则不存在");
        }
        return RpcResult.success(furnitureActivityConfig.getConfig().getRuleDescription());
    }

    @Override
    public RpcResult<List<FurnitureGroupModel>> getFurnitureGroup(ActivityPrizeParam param) {
        int activityId = param.getActivityId();
        // 根据活动id查询家具套组id
        Set<Integer> furnitureGroupIds = furnitureActivityFurnitureGroupRelationRepository.queryByActivityId(activityId)
                .stream()
                .map(FurnitureActivityFurnitureGroupRelation::getFurnitureGroupId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(furnitureGroupIds)) {
            log.warn("getFurnitureGroup no furniture group found for activityId: {}", activityId);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "活动没有对应的家具套组");
        }
        // 过滤掉未上线的家具套组
        List<Integer> furnitureGroupIdList = furnitureGroupRepository.getByIds(furnitureGroupIds)
                .stream()
                .filter(furnitureGroup -> furnitureGroup.getStatus() == 1)
                .map(FurnitureGroup::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(furnitureGroupIdList)) {
            log.warn("getFurnitureGroup no valid furniture group found for activityId: {}", activityId);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "活动没有有效的家具套组");
        }
        // 根据家具套组id查询家具套组信息
        List<FurnitureGroupModel> furnitureGroupModels = furnitureGroupRepository.getByIds(furnitureGroupIdList)
                .stream()
                .filter(furnitureGroup -> furnitureGroup != null && furnitureGroup.getConfig() != null)
                .map(furnitureGroup -> new FurnitureGroupModel().setThumbnail(furnitureGroup.getConfig().getThumbnail()).setId(furnitureGroup.getId()))
                .collect(Collectors.toList());
        return RpcResult.success(furnitureGroupModels);
    }

    @Override
    public RpcResult<RoleAnimationModel> getFurnitureActivityById(ActivityPrizeParam param) {
        int activityId = param.getActivityId();
        FurnitureActivityConfig furnitureActivityConfig = furnitureActivityConfigRepository.selectActivityByIdFromDB(activityId);

        if (furnitureActivityConfig == null) {
            log.warn("getFurnitureActivityById not found, activityId:{}", activityId);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "家具活动不存在");
        }
        ImageInfo backgroundImage = furnitureActivityConfig.getConfig().getBackgroundImage();
        if (backgroundImage == null) {
            log.warn("getBackgroundPage not found, activityId:{}", param.getActivityId());
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "家具活动背景图不存在");
        }
        FurnitureActivityConfig.RoleAnimation roleAnimation = furnitureActivityConfig.getConfig().getRoleAnimation();
        if (roleAnimation == null || CollectionUtils.isEmpty(roleAnimation.getRoleConfigs())) {
            log.info("getFurnitureActivityById role animation not found, activityId:{}", activityId);
            return RpcResult.success(new RoleAnimationModel().setBackgroundImage(backgroundImage));
        }
        // 获取角色组id
        List<Integer> roleIdList = roleAnimation.getRoleConfigs().stream().map(CostumeBlindBoxActivity.RoleConfig::getId).collect(Collectors.toList());
        Integer roleGroupIdByRoleId = roleGroupComponent.findRoleGroupIdByRoleId(roleIdList);
        List<RoleAnimationModel.RoleConfigModel> roleConfigs = roleAnimation.getRoleConfigs().stream().filter(Objects::nonNull).map(roleConfig -> {
            RoleAnimationModel.CoordinateModel coordinate = new RoleAnimationModel.CoordinateModel().setX(roleConfig.getCoordinate().getX())
                    .setY(roleConfig.getCoordinate().getY());
            return new RoleAnimationModel.RoleConfigModel().setCoordinate(coordinate)
                    .setRoleId(roleConfig.getId())
                    .setRoleName(roleConfig.getName())
                    .setAnimation(roleConfig.getAnimation())
                    .setCostumeId(roleConfig.getCostumeId());
        }).collect(Collectors.toList());
        RoleAnimationModel roleAnimationModel = new RoleAnimationModel();
        roleAnimationModel.setRoleGroupId(roleGroupIdByRoleId);
        roleAnimationModel.setFurnitureGroupId(roleAnimation.getFurnitureGroupId());
        roleAnimationModel.setZoomRatio(roleAnimation.getZoomRatio());
        roleAnimationModel.setRoleConfigs(roleConfigs);
        roleAnimationModel.setBackgroundImage(backgroundImage);
        return RpcResult.success(roleAnimationModel);
    }

    private boolean activityInValidity(FurnitureActivityConfig activity) {
        long now = System.currentTimeMillis();
        return activity.getStartAt() <= now && activity.getEndAt() >= now;
    }

    private static UserFurnitureActivityRewardRecord buildRewardRecord(BlindBoxActivityRecordParam activityRecordParam) {
        return new UserFurnitureActivityRewardRecord().setUserId(activityRecordParam.getUserId())
                .setActivityId(activityRecordParam.getActivityId())
                .setLevelIndex(activityRecordParam.getLevelIndex())
                .setBid(activityRecordParam.getBid())
                .setPrizeId(activityRecordParam.getPrizeId())
                .setNum(activityRecordParam.getNum());
    }

}

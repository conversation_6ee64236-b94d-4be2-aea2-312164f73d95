package com.kuaikan.role.game.service.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboService;

import com.kuaikan.comic.avg.model.bo.UserTopicRoleAttributeBO;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.game.gamecard.activity.def.dto.UserCoinDto;
import com.kuaikan.game.gamecard.dubbo.dto.ClientInfoDTO;
import com.kuaikan.game.gamecard.questcontainer.def.dto.QuestContainerDto;
import com.kuaikan.game.gamecard.questcontainer.def.dto.QuestDto;
import com.kuaikan.game.gamecard.questcontainer.def.service.QuestContainerService;
import com.kuaikan.role.game.api.bean.MapStory;
import com.kuaikan.role.game.api.bean.MapStoryAiTaskConfig;
import com.kuaikan.role.game.api.bean.MapStoryRelation;
import com.kuaikan.role.game.api.bean.MapStoryVoiceConfig;
import com.kuaikan.role.game.api.bean.UserMapStory;
import com.kuaikan.role.game.api.bean.UserThirdBizProgressRecord;
import com.kuaikan.role.game.api.bo.MapStoryBO;
import com.kuaikan.role.game.api.common.RoleGameResponse;
import com.kuaikan.role.game.api.enums.MapStoryType;
import com.kuaikan.role.game.api.enums.MapStoryUnLockType;
import com.kuaikan.role.game.api.model.CommonScheduleStoryLibModel;
import com.kuaikan.role.game.api.model.map.MapCollectionModel;
import com.kuaikan.role.game.api.model.map.MapStoryModel;
import com.kuaikan.role.game.api.model.map.MapStorySimpleModel;
import com.kuaikan.role.game.api.model.map.MapStoryTabModel;
import com.kuaikan.role.game.api.model.map.UnlockMapStoryModel;
import com.kuaikan.role.game.api.rpc.result.TaskModuleConfigModel;
import com.kuaikan.role.game.api.service.CommonMapService;
import com.kuaikan.role.game.api.service.RedDotService;
import com.kuaikan.role.game.common.bean.MapCity;
import com.kuaikan.role.game.common.enums.RedDotEventType;
import com.kuaikan.role.game.component.GameCardActivityComponent;
import com.kuaikan.role.game.component.LockComponent;
import com.kuaikan.role.game.component.MapStoryComponent;
import com.kuaikan.role.game.component.RedDotComponent;
import com.kuaikan.role.game.component.TopicRoleComponent;
import com.kuaikan.role.game.component.UserMapStoryComponent;
import com.kuaikan.role.game.component.UserThirdBizProgressComponent;
import com.kuaikan.role.game.model.bo.GetMapStoryTypeBO;
import com.kuaikan.role.game.repository.AvgRepository;
import com.kuaikan.role.game.repository.MapCityRepository;
import com.kuaikan.role.game.repository.MapStoryRelationRepository;
import com.kuaikan.role.game.repository.MapStoryRepository;
import com.kuaikan.role.game.repository.UserMapStoryRepository;
import com.kuaikan.role.game.uitl.QuestUtil;

/**
 *<AUTHOR>
 *@date 2025/6/4
 */
@DubboService(version = "1.0", group = "role-game")
@Slf4j
public class CommonMapServiceImpl implements CommonMapService {

    @Resource
    private QuestContainerService questContainerService;
    @Resource
    private MapCityRepository mapCityRepository;
    @Resource
    private MapStoryRepository mapStoryRepository;
    @Resource
    private UserMapStoryRepository userMapStoryRepository;
    @Resource
    private RedDotService redDotService;
    @Resource
    private AvgRepository avgRepository;
    @Resource
    private UserMapStoryComponent userMapStoryComponent;
    @Resource
    private RedDotComponent redDotComponent;
    @Resource
    private MapStoryRelationRepository mapStoryRelationRepository;
    @Resource
    private MapStoryComponent mapStoryComponent;
    @Resource
    private TopicRoleComponent topicRoleComponent;
    @Resource
    private GameCardActivityComponent gameCardActivityComponent;
    @Resource
    private LockComponent lockComponent;
    @Resource
    private UserThirdBizProgressComponent userThirdBizProgressComponent;

    @Override
    public RpcResult<Void> checkUserMapStory(int userId, int mapId, int chapterId) {
        return RpcResult.success();
    }

    @Override
    public RpcResult<TaskModuleConfigModel.TaskConfigModel> getMapTaskList(ClientInfoDTO clientInfo, int mapId) {
        MapCity mapCity = mapCityRepository.getById(mapId);
        if (mapCity == null || mapCity.getConfig() == null || mapCity.getConfig().getActivityJobId() == null) {
            log.warn("getMapTaskList mapCity taskId is null, mapId: {}, mapCity: {}", mapId, mapCity);
            return RpcResult.success(null);
        }
        long questContainerId = mapCity.getConfig().getActivityJobId();
        QuestContainerDto userQuestContainerDto = questContainerService.getUserQuestContainerDto(clientInfo, questContainerId);
        if (userQuestContainerDto == null) {
            log.error("getMapTaskList userQuestContainerDto is null, questContainerId:{}, clientInfo: {}", questContainerId, clientInfo);
            return RpcResult.success(null);
        }
        // 检查任务时间是否有效
        if (userQuestContainerDto.getStartTime() > System.currentTimeMillis() || userQuestContainerDto.getEndTime() < System.currentTimeMillis()) {
            log.error("getMapTaskList userQuestContainerDto time is invalid, userId: {}, questContainerId: {}, startTime: {}, endTime: {}",
                    clientInfo.getUserId(), questContainerId, userQuestContainerDto.getStartTime(), userQuestContainerDto.getEndTime());
            return RpcResult.success(null);
        }
        List<QuestDto> questDtos = userQuestContainerDto.getQuestDtos();
        List<TaskModuleConfigModel.QuestDtoInfo> questDtoInfos = questDtos.stream()
                .map(item -> new TaskModuleConfigModel.QuestDtoInfo().setQuestDto(item))
                .collect(Collectors.toList());
        boolean hadRedDot = QuestUtil.ifHasFinishTask(questDtos);
        TaskModuleConfigModel.TaskConfigModel taskConfigModel = new TaskModuleConfigModel.TaskConfigModel().setTaskId(questContainerId)
                .setModuleName("任务")
                .setShowRedDot(hadRedDot)
                .setQuestInfos(questDtoInfos);

        return RpcResult.success(taskConfigModel);
    }

    @Override
    public RpcResult<MapCollectionModel> getMapCollection(int userId, int mapId) {
        // 查询地图topic
        MapCity mapCity = mapCityRepository.getById(mapId);
        if (mapCity == null) {
            log.warn("getMapCollection mapCity not found for mapId: {}", mapId);
            return RpcResult.success(new MapCollectionModel().setModuleName("地图收集簿"));
        }
        Integer topicId = mapCity.getConfig().getTopicId();
        // 查询地图剧情
        List<MapStoryBO> mapStories = mapStoryRepository.queryBoListByMapId(mapId);
        if (CollectionUtils.isEmpty(mapStories)) {
            log.warn("getMapCollection no map stories found for mapId: {}", mapId);
            return RpcResult.success(new MapCollectionModel().setModuleName("地图收集簿"));
        }
        // 获取用户已获得剧情信息
        List<UserMapStory> userMapStories = userMapStoryRepository.queryListByUserIdAndMapId(userId, mapId);
        Map<Integer, UserMapStory> userMapStoryMap = userMapStories.stream().collect(Collectors.toMap(UserMapStory::getMapStoryId, Function.identity()));
        // 未解锁的故事剧情
        List<MapStoryBO> unLockStories = mapStories.stream().filter(story -> !userMapStoryMap.containsKey(story.getMapStoryId())).collect(Collectors.toList());
        // 配置的解锁条件
        Map<Integer, List<MapStory.UnlockCondition>> unlockConditionsListMap = unLockStories.stream()
                .collect(Collectors.toMap(MapStoryBO::getMapStoryId, MapStoryBO::getUnlockConditions, (a, b) -> b));
        // 用户需要解锁的剧情金币数量
        Map<Long, UserCoinDto> userCoinMap = mapStoryComponent.getUserCoinMapByUnLockStories(userId, unLockStories);
        // 有关联剧情的日程信息
        List<CommonScheduleStoryLibModel> commonScheduleStoryLibList = userMapStoryComponent.getCommonScheduleStoryLibList(mapId);
        Map<Integer, List<CommonScheduleStoryLibModel>> unlockScheduleListMap = commonScheduleStoryLibList.stream()
                .collect(Collectors.groupingBy(CommonScheduleStoryLibModel::getStoryLibId, Collectors.toList()));
        // 角色的属性
        UserTopicRoleAttributeBO userCurrentAttribute = topicRoleComponent.getTopicRoleAttribute(userId, topicId);
        Map<Integer, UserThirdBizProgressRecord> userTopicAttributeProgressRecords = userThirdBizProgressComponent.getUserTopicAttributeProgressRecords(userId,
                topicId, unLockStories, userCurrentAttribute);
        Map<Integer, UserThirdBizProgressRecord> userCoinHistoryProgressMap = userThirdBizProgressComponent.getUserCoinHistoryProgressRecords(userId,
                 unLockStories, userCoinMap);
        GetMapStoryTypeBO getMapStoryTypeBO = new GetMapStoryTypeBO();
        getMapStoryTypeBO.setUserMapStoryMap(userMapStoryMap)
                .setUnlockScheduleListMap(unlockScheduleListMap)
                .setUserTopicAttributeProgressMap(userTopicAttributeProgressRecords)
                .setUserCoinHistoryProgressMap(userCoinHistoryProgressMap)
                .setUserTopicRoleAttributeBO(userCurrentAttribute)
                .setUnlockConditionsListMap(unlockConditionsListMap)
                .setUserId(userId)
                .setMapId(mapId)
                .setTopicId(topicId)
                .setUserCoinMap(userCoinMap);
        // 填充地图收集簿
        List<MapStoryTabModel> tabModelList = new ArrayList<>();
        // 根据标签分组
        Map<String, List<MapStoryBO>> storiesByTag = mapStories.stream()
                .sorted(Comparator.comparing(MapStoryBO::getTagNum, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.groupingBy(MapStoryBO::getTag, LinkedHashMap::new, Collectors.toList()));
        // 使用标签分组进行展示
        storiesByTag.forEach((tag, stories) -> {
            MapStoryTabModel mapStoryTabModel = new MapStoryTabModel().setTabName(tag);
            List<MapStoryModel> storyInfos = stories.stream()
                    .sorted(Comparator.comparing(MapStoryBO::getLibNum, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(MapStoryBO::getOrderNum, Comparator.nullsLast(Comparator.naturalOrder())))
                    .map(story -> processStory(getMapStoryTypeBO, story))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            boolean tabNewRedDot = redDotComponent.hasAddMapStoryTagEvent(userId, mapId, tag);
            boolean tabHasRedDot = redDotComponent.hasUserAcceptStoryPoint(RedDotEventType.MAP_USER_ACCEPT_STORY_TAG, userId, mapId, tag);
            mapStoryTabModel.setNewRedDot(tabNewRedDot);
            mapStoryTabModel.setShowRedDot(tabHasRedDot);
            mapStoryTabModel.setStoryList(storyInfos);
            mapStoryTabModel.setUserStoryCnt((int) storyInfos.stream().filter(MapStoryModel::isUnlock).count());
            mapStoryTabModel.setTotalStoryCnt(storyInfos.size());
            // 将分组添加到收集簿中
            tabModelList.add(mapStoryTabModel);
        });

        // 地图获得新剧情红点
        boolean newRedDot = redDotComponent.hasUserBroadcastEvent(userId, RedDotEventType.MAP_HOME_ADD_STORY, mapId);
        // 地图上新红点信息
        boolean hasRedDot = redDotComponent.hasUserAcceptStoryPoint(RedDotEventType.MAP_HOME_USER_ACCEPT_STORY, userId, mapId, null);
        return RpcResult.success(
                new MapCollectionModel().setModuleName("地图收集簿").setTabList(tabModelList).setNewRedDot(newRedDot).setShowRedDot(hasRedDot));
    }

    /**
     * 处理单个剧情
     */
    private MapStoryModel processStory(GetMapStoryTypeBO getMapStoryTypeBO, MapStoryBO story) {
        MapStoryModel mapStoryModel;
        switch (Objects.requireNonNull(MapStoryType.getByCode(story.getType()))) {
            case AVG:
                mapStoryModel = mapStoryComponent.getAvgMapStory(getMapStoryTypeBO, story);
                break;
            case IMAGE:
                mapStoryModel = mapStoryComponent.getPhotoMapStory(getMapStoryTypeBO, story);
                break;
            case AUDIO:
                mapStoryModel = mapStoryComponent.getVoiceMapStory(getMapStoryTypeBO, story);
                break;
            case AI_TASK:
                mapStoryModel = mapStoryComponent.getAICardMapStory(getMapStoryTypeBO, story);
                break;
            default:
                log.error("Unknown story type: {}", story.getType());
                return null;
        }
        return mapStoryModel;
    }

    @Override
    public void storyCollectionRedDotClear(int userId, int mapId, List<Integer> storyIds) {
        for (Integer mapStoryId : storyIds) {
            redDotService.clearAddMapStoryEvent(userId, mapId, mapStoryId);
        }
        userMapStoryComponent.clearRedDotByAvgIds(userId, mapId, storyIds);
    }

    @Override
    public void clearMapRedDot(int userId, int mapId) {
        redDotService.clearMapHomeAddStoryAndAcceptStoryEvent(userId, mapId);
    }

    @Override
    public void clearMapTagRedDot(int userId, int mapId, String tag) {
        redDotService.clearMapTagAddStoryAndAcceptStoryEvent(userId, mapId, tag);
    }

    @Override
    public UnlockMapStoryModel unlockMapStoryModelByLibId(int userId, int mapId, int libId) {
        return userMapStoryComponent.unlockMapStoryModelByLibId(userId, mapId, libId);
    }

    @Override
    public RpcResult<Void> unlockStory(int userId, int mapId, int mapStoryId) {
        // 参数校验
        if (mapStoryId <= 0 || mapId <= 0 || userId <= 0) {
            log.warn("unlockStory invalid parameters, userId: {}, mapId: {}, mapStoryId: {}", userId, mapId, mapStoryId);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST);
        }
        // 校验地图剧情关系配置
        MapStoryRelation mapStoryRelation = mapStoryRelationRepository.queryByMapIdAndStoryId(mapId, mapStoryId);
        if (mapStoryRelation == null) {
            log.warn("unlockStory mapStoryRelation not found for mapId: {}, mapStoryId: {}", mapId, mapStoryId);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST);
        }
        MapStoryBO mapStory = mapStoryRepository.queryBOByMapIdAndStoryId(mapId, mapStoryRelation.getMapStoryId());
        if (mapStory == null) {
            log.warn("unlockStory mapStory not found for mapStoryId: {}", mapStoryRelation.getMapStoryId());
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST);
        }
        // 1. 已解锁直接返回
        List<UserMapStory> userMapStory = userMapStoryRepository.queryListByUserIdAndMapId(userId, mapId);
        Map<Integer, UserMapStory> userMapStoryMap = userMapStory.stream().collect(Collectors.toMap(UserMapStory::getMapStoryId, Function.identity()));
        UserMapStory userMapStoryStory = userMapStoryMap.get(mapStoryId);
        if (userMapStoryStory != null) {
            return RpcResult.result(RoleGameResponse.MAP_STORY_ALREADY_OBTAINED);
        }
        // 2. 无解锁条件，返回配置错误信息，不能直接解锁
        List<MapStory.UnlockCondition> unlockConditions = mapStory.getUnlockConditions();
        if (CollectionUtils.isEmpty(unlockConditions)) {
            log.error("unlockStory no unlock conditions for mapStoryId: {}", mapStoryId);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST);
        }
        // 过滤解锁条件
        unlockConditions = unlockConditions.stream()
                .filter(cond -> cond.getConditionType() != MapStoryUnLockType.SCHEDULE.getCode())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unlockConditions)) {
            log.error("unlockStory no needed unlock conditions for mapStoryId: {}", mapStoryId);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST);
        }

        // 4. 校验属性、金币达成条件，未达成直接返回
        List<MapStory.UnlockCondition> attrConditions = unlockConditions.stream()
                .filter(cond -> cond.getConditionType() == MapStoryUnLockType.ATTRIBUTE_REQUIREMENT.getCode()
                        || cond.getConditionType() == MapStoryUnLockType.CONSUME_REQUIREMENT.getCode())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(attrConditions)) {
            // 查询地图topicId
            MapCity mapCity = mapCityRepository.getById(mapId);
            if (mapCity == null) {
                log.warn("unlockStory mapCity not found for mapId: {}", mapId);
                return RpcResult.result(ResponseCodeMsg.BAD_REQUEST);
            }
            // 手动解锁时，仅查询记录
            Integer topicId = mapCity.getConfig().getTopicId();
            for (MapStory.UnlockCondition cond : attrConditions) {
                if (cond.getConditionType() == MapStoryUnLockType.ATTRIBUTE_REQUIREMENT.getCode()) {
                    Integer condAttrKey = NumberUtils.toInt(cond.getAttributeKey(), 0);
                    UserThirdBizProgressRecord progressRecord = userThirdBizProgressComponent.getUserTopicAttributeProgressRecord(userId, topicId, condAttrKey);
                    if (progressRecord == null || progressRecord.getProgress() < cond.getAttributeValue()) {
                        return RpcResult.result(RoleGameResponse.MAP_STORY_UNLOCK_ATTR_NOT_ENOUGH);
                    }
                } else if (cond.getConditionType() == MapStoryUnLockType.CONSUME_REQUIREMENT.getCode()) {
                    UserThirdBizProgressRecord progressRecord = userThirdBizProgressComponent.getUserCoinProgressRecord(userId, cond.getCoinId());
                    if (progressRecord == null || progressRecord.getProgress() < cond.getCoinCount()) {
                        return RpcResult.result(RoleGameResponse.MAP_STORY_UNLOCK_COIN_NOT_REACHED);
                    }
                }
            }
        }
        ClientInfoDTO clientInfoDTO = new ClientInfoDTO();
        clientInfoDTO.setUserId(userId);
        // 5. 校验金币条件，属性和日程都通过后才校验
        List<MapStory.UnlockCondition> coinConditions = unlockConditions.stream()
                .filter(cond -> cond.getConditionType() == MapStoryUnLockType.CONSUME_ITEM.getCode())
                .collect(Collectors.toList());
        // 如果金币没配置
        if (CollectionUtils.isEmpty(coinConditions)) {
            return RpcResult.result(RoleGameResponse.BAD_REQUEST);
        }
        if (CollectionUtils.isNotEmpty(coinConditions)) {
            // 先获取需要的金币ID列表
            List<Long> coinIds = coinConditions.stream().map(cond -> Long.valueOf(cond.getCoinId())).distinct().collect(Collectors.toList());
            List<UserCoinDto> userCoins = gameCardActivityComponent.getUserCoins(clientInfoDTO, coinIds);
            Map<Long, UserCoinDto> userCoinDtoMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(userCoins)) {
                userCoinDtoMap = userCoins.stream().collect(Collectors.toMap(UserCoinDto::getCoinId, Function.identity(), (a, b) -> b));
            }
            // 收集所有不满足条件的金币名称（去重）
            Set<String> insufficientCoinNames = new LinkedHashSet<>();
            for (MapStory.UnlockCondition cond : coinConditions) {
                int coinId = cond.getCoinId();
                UserCoinDto userCoinDto = userCoinDtoMap.get((long) coinId);
                if (userCoinDto == null || userCoinDto.getOwnerCoins() < cond.getCoinCount()) {
                    if (userCoinDto != null) {
                        insufficientCoinNames.add(userCoinDto.getName());
                    } else {
                        // 若金币未找到，添加默认提示
                        insufficientCoinNames.add("未知道具");
                    }
                }
            }
            // 如果有不足的金币，返回拼接后的错误信息
            if (!insufficientCoinNames.isEmpty()) {
                List<String> names = new ArrayList<>(insufficientCoinNames);
                String message;
                if (names.size() == 1) {
                    message = names.get(0) + "消耗品不足，请获取";
                } else {
                    message = String.join("和", names) + "消耗品不足，请获取";
                }
                return RpcResult.result(RoleGameResponse.MAP_STORY_UNLOCK_COIN_NOT_ENOUGH.getCode(), message);
            }
            // 全部校验通过后再逐个扣除金币
            for (MapStory.UnlockCondition cond : coinConditions) {
                try {
                    // 为金币上锁
                    boolean locked = lockComponent.lockForCoinUnlock(userId, cond.getCoinId());
                    if (!locked) {
                        return RpcResult.result(RoleGameResponse.OPERATION_TOO_FAST);
                    }
                    Boolean isPay = gameCardActivityComponent.changeUserCoin(clientInfoDTO, Long.valueOf(cond.getCoinId()), -cond.getCoinCount());
                    if (isPay == null || !isPay) {
                        log.warn("扣除金币失败，userId: {}, coinId: {}, count: {}", userId, cond.getCoinId(), cond.getCoinCount());
                        return RpcResult.result(RoleGameResponse.MAP_STORY_UNLOCK_COIN_NOT_ENOUGH);
                    }
                } finally {
                    lockComponent.unlockForCoinUnlock(userId, cond.getCoinId());
                }
            }
        }
        // 6. 执行解锁剧情
        userMapStoryComponent.unlockMapStoryModel(userId, mapId, mapStory);
        return RpcResult.success();
    }

    @Override
    public RpcResult<List<MapStorySimpleModel>> getUserUnlockMapStorySimpleList(int userId, int mapId, List<Integer> topicRoleIds) {
        // 查询地图剧情配置
        List<MapStoryBO> mapStories = mapStoryRepository.queryBoListByMapId(mapId);
        if (CollectionUtils.isEmpty(mapStories)) {
            log.warn("getUserUnlockMapStorySimpleList no map stories found for mapId: {}", mapId);
            return RpcResult.success(new ArrayList<>());
        }
        Set<Integer> storyIdSet = mapStories.stream().filter(story -> {
            // 没有限制角色的剧情
            if (CollectionUtils.isEmpty(topicRoleIds)) {
                return true;
            } else {
                // 有角色限制，检查是否包含在topicRoleIds中
                return story.getTopicRoleIds().stream().anyMatch(topicRoleIds::contains);
            }
        }).map(MapStoryBO::getMapStoryId).collect(Collectors.toSet());
        // 查询用户解锁剧情
        List<UserMapStory> userMapStories = userMapStoryRepository.queryListByUserIdAndMapId(userId, mapId);
        if (CollectionUtils.isEmpty(userMapStories)) {
            log.debug("getUserUnlockMapStorySimpleList no user map stories found for userId: {}, mapId: {}", userId, mapId);
            return RpcResult.success(new ArrayList<>());
        }
        Map<Integer, MapStoryBO> mapStoryBOMap = mapStories.stream().collect(Collectors.toMap(MapStoryBO::getMapStoryId, Function.identity(), (a, b) -> a));
        List<MapStorySimpleModel> list = userMapStories.stream()
                .filter(userMapStory -> storyIdSet.contains(userMapStory.getMapStoryId()))
                .sorted(Comparator.comparing(UserMapStory::getCreatedAt).reversed())
                .map(userMapStory -> {
                    MapStoryBO mapStoryBO = mapStoryBOMap.get(userMapStory.getMapStoryId());
                    if (mapStoryBO == null) {
                        // 用户获取的剧情在配置中未找到，可能是因为配置变更或数据不一致
                        log.warn("getUserUnlockMapStorySimpleList mapStoryBO not found for mapStoryId: {}", userMapStory.getMapStoryId());
                        return null;
                    }
                    MapStorySimpleModel mapStorySimpleModel = new MapStorySimpleModel().setMapStoryId(mapStoryBO.getMapStoryId())
                            .setTitle(mapStoryBO.getName())
                            .setType(mapStoryBO.getType())
                            .setAvgChapterId(mapStoryBO.getAvgChapterId())
                            .setCoverImageUrl(mapStoryBO.getCoverImage())
                            .setTopicRoleIds(mapStoryBO.getTopicRoleIds());
                    if (mapStoryBO.getType() == MapStoryType.AI_TASK.getCode()) {
                        MapStoryAiTaskConfig config =  JsonUtils.fromJson(mapStoryBO.getConfig(), MapStoryAiTaskConfig.class);
                        mapStorySimpleModel.setAiTaskCardId(config.getAiTaskCardId());
                    } else if (mapStoryBO.getType() == MapStoryType.AUDIO.getCode()) {
                        MapStoryVoiceConfig config = JsonUtils.fromJson(mapStoryBO.getConfig(), MapStoryVoiceConfig.class);
                        mapStorySimpleModel.setMp3Url(config.getConfigFileKey());
                    }
                    return mapStorySimpleModel;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return RpcResult.success(list);
    }
}

package com.kuaikan.role.game.api.apis;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import javax.validation.Valid;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.rpc.param.AnchorUpdateRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgChapterCreateRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgChapterDeleteRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgChapterUpdateRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgChapterUploadRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgDirSaveOrUpdateRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgFileQueryRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgGyroscopeSaveRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgHotZoneSaveOrUpdateRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgOriginFileSaveOrUpdateRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgResourceUpdateRpcParam;
import com.kuaikan.role.game.api.rpc.param.AvgUIStyleZipFileCreateRpcParam;
import com.kuaikan.role.game.api.rpc.param.BatchQueryAvgParam;
import com.kuaikan.role.game.api.rpc.param.CheckTopicAvgRpcParam;
import com.kuaikan.role.game.api.rpc.param.PushDigitalAssetParam;
import com.kuaikan.role.game.api.rpc.result.AvgChapterModel;
import com.kuaikan.role.game.api.rpc.result.AvgChapterSimpleModel;

@RestController
@RequestMapping("/api/roleGame/avg")
public interface AvgApi {

    /**
     * 批量查询avg章节信息
     *
     * @param batchQueryAvgParam 批量查询参数
     * @return avg章节信息
     */
    @PostMapping("queryByIds")
    RpcResult<Map<Integer, AvgChapterModel>> queryByIds(@RequestBody BatchQueryAvgParam batchQueryAvgParam);

    /**
     * 根据专题id，查专题id关联项目的段落
     *
     * @param topicId，专题id
     * @return 专题id关联的段落列表
     */
    @GetMapping("queryTopicRelatedChapter")
    RpcResult<List<AvgChapterSimpleModel>> queryTopicRelatedChapter(@RequestParam("topicId") int topicId);

    /**
     * 推送数字资产
     * @param pushDigitalAssetParam 推送数字资产参数
     * @return 推送结果
     */
    @PostMapping("pushDigitalAsset")
    RpcResult<Object> pushDigitalAsset(PushDigitalAssetParam pushDigitalAssetParam);

    @GetMapping("chapter/list")
    RpcResult<Map<String, Object>> chapterList(
            @RequestParam(name = "orderType", defaultValue = "1") Integer orderTypeId,
            @RequestParam(name = "segmentName", defaultValue = "") String segmentName,
            @RequestParam(name = "segmentId", defaultValue = "0") Integer segmentId,
            @RequestParam(name = "type", defaultValue = "1") Integer type, @RequestParam("topicId") Integer topicId,
            @RequestParam("page") int page, @RequestParam("pageSize") int pageSize);

    /**
     * 上传avg剧情段落
     *
     * @param
     * @param
     * @return
     */
    @PostMapping("chapter/upload")
    RpcResult<Map<String, Object>> chapterUpload(@RequestBody AvgChapterUploadRpcParam rpcParam);

    /**
     * 修改段落
     *
     * @return
     */
    @PostMapping("chapter/update")
    RpcResult<Map<String, Object>> updateChapter(@RequestBody AvgChapterUpdateRpcParam rpcParam);

    /**
     * 删除段落
     *
     * @param
     * @return
     */
    @PostMapping("chapter/delete")
    RpcResult<Map<String, Object>> deleteChapter(@RequestBody AvgChapterDeleteRpcParam rpcParam);

    /**
     * 新建段落
     *
     * @param
     * @param
     * @return
     */
    @PostMapping("chapter/create")
    RpcResult<Map<String, Object>> createChapter(@RequestBody AvgChapterCreateRpcParam  rpcParam);

    /**
     * 创建avg资源文件
     *
     * @param param
     * @return
     */
    @PostMapping("/file/create")
    RpcResult<Map<String, Object>> createFile(@Valid @RequestBody AvgOriginFileSaveOrUpdateRpcParam param);

    /**
     * 资源文件启用接口
     *
     * @param id
     * @param type
     * @return
     */
    @PostMapping("/online")
    RpcResult<Map<String, Object>> online(@RequestParam(name = "id") int id, @RequestParam(name = "type") int type,
                                          @RequestParam("topicId") int topicId);

    /**
     * 资源文件停用接口
     *
     * @param id
     * @param type
     * @return
     */
    @PostMapping("/offline")
    RpcResult<Map<String, Object>> offline(@RequestParam(name = "id") int id, @RequestParam(name = "type") int type,
                                           @RequestParam("topicId") int topicId);

    /**
     * avg压缩文件下载
     *
     * @param id
     * @param
     * @return
     */
    @PostMapping("file/zip/download")
    RpcResult<Map<String, Object>> avgZipFileDownload(@RequestParam("id") int id, @RequestParam(name = "topicId") int topicId);

    /**
     * 校验关联的topic
     *
     * @param
     * @param
     * @return
     */
    @PostMapping("file/check/topic")
    RpcResult<Map<String, Object>> checkTopic(@RequestBody CheckTopicAvgRpcParam rpcParam);

    /**
     * zip包内文件批量替换
     */
    @PostMapping("/anchor/update")
    RpcResult<Map<String, Object>> updateAnchor(@RequestBody AnchorUpdateRpcParam rpcParam);

    /**
     * zip包内文件批量替换
     */
    @PostMapping("/file/zip/create")
    RpcResult<Map<String, Object>> createZipFile(AvgUIStyleZipFileCreateRpcParam rpcParam);

    /**
     * 所有目录列表(不携带文件不分页)
     */
    @GetMapping("/dir/listAll")
    RpcResult<Map<String, Object>> dirListAllByType(@RequestParam("type") int type, @RequestParam("orderByAndSort") String orderByAndSort);

    @PostMapping("/file/listByTypes")
    RpcResult<Map<String, Object>> fileListByType(@RequestBody AvgFileQueryRpcParam param);

    /**
     * 判断文件是否存在
     *
     * @param fileName
     * @param dirId
     * @return
     */
    @GetMapping("file/check/exit")
    RpcResult<Map<String, Object>> checkExit(@RequestParam("fileName") String fileName,
                                             @RequestParam(name = "dirId", required = false, defaultValue = "0") Integer dirId);

    /**
     * 删除文件
     */
    @PostMapping("/file/delete")
    RpcResult<Map<String, Object>> deleteFile(@RequestParam("id") int id, @RequestParam("topicId") int topicId);

    /**
     * 上传avg资源文件
     *
     * @return
     */
    @PostMapping(value = "file/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    RpcResult<Map<String, Object>> avgFileUpload(@RequestPart("file") MultipartFile file, @RequestParam("key") String key,
                                                 @RequestParam(name = "type", required = false) Integer type,
                                                 @RequestParam(name = "dirId", required = false) Integer dirId);

    /**
     * 更新文件
     */
    @PostMapping("/file/update")
    RpcResult<Map<String, Object>> updateFile(@Valid @RequestBody AvgOriginFileSaveOrUpdateRpcParam rpcParam);

    /**
     * 上传zip文件
     */
    @PostMapping(value = "file/zip/upload",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    RpcResult<Map<String, Object>> avgZipFileUpload(@RequestPart("file") MultipartFile file,
                                                    @RequestParam(name = "type") Integer type, @RequestParam(name = "dirId") Integer dirId,
                                                    @RequestParam("topicId") Integer topicId);

    /**
     * 更新目录
     *
     * @param rpcParam
     * @return
     */
    @PostMapping("/dir/update")
    RpcResult<Map<String, Object>> updateDir(@RequestBody AvgDirSaveOrUpdateRpcParam rpcParam);

    @PostMapping("/dir/listWithFiles")
    RpcResult<Map<String, Object>> dirListWithFilesByType(@RequestBody AvgFileQueryRpcParam rpcParam);

    /**
     * 目录创建
     *
     * @param rpcParam
     * @return
     */
    @PostMapping("/dir/create")
    RpcResult<Map<String, Object>> createDir(@RequestBody AvgDirSaveOrUpdateRpcParam rpcParam);

    /**
     * 目录列表(不携带文件)
     */
    @GetMapping("/dir/list")
    RpcResult<Map<String, Object>> dirListByType(@RequestParam("type") int type, @RequestParam("orderByAndSort") String orderByAndSort,
                                                 @RequestParam("page") int page, @RequestParam("pageSize") int pageSize);

    /**
     * 删除目录
     */
    @PostMapping("/dir/delete")
    RpcResult<Map<String, Object>> deleteDir(@RequestParam("id") int id, @RequestParam("topicId") int topicId);

    /**
     * 点触热区列表
     *
     * @param rpcParam
     * @return
     */
    @PostMapping("hotZone/list")
    RpcResult<Map<String, Object>> avgHotZoneList(@RequestBody AvgFileQueryRpcParam rpcParam);

    /**
     * 点触热区创建
     */
    @PostMapping("hotZone/create")
    RpcResult<Map<String, Object>> avgHotZoneCreate(@Valid @RequestBody AvgHotZoneSaveOrUpdateRpcParam rpcParam);

    /**
     * 点触热区更新
     */
    @PostMapping("hotZone/update")
    RpcResult<Map<String, Object>> avgHotZoneUpdate(@Valid @RequestBody AvgHotZoneSaveOrUpdateRpcParam rpcParam);

    /**
     * 点触热区底图上传
     */
    @PostMapping("hotZone/delete")
    RpcResult<Map<String, Object>> avgHotZoneDelete(@RequestParam("id") Integer id, @RequestParam("topicId") Integer topicId);

    @PostMapping(value = "hotZone/baseImg/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    RpcResult<Map<String, Object>> avgHotZoneUpload(@RequestPart("file") MultipartFile file, @RequestParam("key") String key,
                                                    @RequestParam("hotZoneId") Integer hotZoneId, @RequestParam("topicId") Integer topicId);

    @GetMapping("avgResourceTag/tagName/list")
    RpcResult<Map<String, Object>> avgTagNameList();

    @PostMapping("/update/resource")
    RpcResult<Map<String, Object>> updateResource(@RequestBody AvgResourceUpdateRpcParam rpcParam);

    @PostMapping("gyroscope/list")
    RpcResult<Map<String, Object>> gyroscopeList(@RequestBody AvgFileQueryRpcParam rpcParam);

    @PostMapping("gyroscope/save")
    RpcResult<Map<String, Object>> avgGyroscopeCreate(@RequestBody AvgGyroscopeSaveRpcParam rpcParam);

    @PostMapping("gyroscope/file/upload")
    RpcResult<Map<String, Object>> avgGyroscopeUpload(@RequestPart("file") MultipartFile file,
                                                      @RequestParam("gyroscopeId") int gyroscopeId, @RequestParam("type") Integer type,
                                                      @RequestParam("topicId") Integer topicId);
    @PostMapping("gyroscope/delete")
    RpcResult<Map<String, Object>> avgGyroscopeDelete(@RequestParam("id") Integer id, @RequestParam("topicId") Integer topicId);

    @GetMapping("/topic/relatedProjectId")
    RpcResult<Map<Integer, String>> getTopicRelatedProject(@RequestParam("topicIds") Collection<Integer> topicIds);
}

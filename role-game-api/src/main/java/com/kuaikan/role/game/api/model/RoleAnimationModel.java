package com.kuaikan.role.game.api.model;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.Coordinate;
import com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity;
import com.kuaikan.role.game.api.bean.FurnitureActivityConfig.RoleAnimation;
import com.kuaikan.role.game.api.bo.ImageInfo;

@Data
@Accessors(chain = true)
public class RoleAnimationModel implements Serializable {

    private static final long serialVersionUID = 1837493923923L;

    /** 背景图 */
    private ImageInfo backgroundImage;

    /** 角色组ID */
    private Integer roleGroupId;

    /** 关联单个角色组的家具套组 */
    private Integer furnitureGroupId;

    /** 缩放比例 */
    private Double zoomRatio;

    /** 角色配置 */
    private List<RoleConfigModel> roleConfigs;

    @Data
    @Accessors(chain = true)
    public static class RoleConfigModel implements Serializable {

        private static final long serialVersionUID = -6157859122478L;

        private int roleId;

        private String roleName;

        private int costumeId;

        private String animation;

        private CoordinateModel coordinate;

        public static RoleConfigModel valueOf(CostumeBlindBoxActivity.RoleConfig roleConfig) {
            if (roleConfig == null) {
                return null;
            }
            RoleConfigModel roleConfigModel = new RoleConfigModel();
            roleConfigModel.setRoleId(roleConfig.getId());
            roleConfigModel.setRoleName(roleConfig.getName());
            roleConfigModel.setCostumeId(roleConfig.getCostumeId());
            roleConfigModel.setAnimation(roleConfig.getAnimation());
            roleConfigModel.setCoordinate(CoordinateModel.valueOf(roleConfig.getCoordinate()));
            return roleConfigModel;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class CoordinateModel implements Serializable {

        private static final long serialVersionUID = 1721683732123L;

        private int x;
        private int y;

        public static CoordinateModel valueOf(Coordinate coordinate) {
            if (coordinate == null) {
                return null;
            }
            CoordinateModel coordinateModel = new CoordinateModel();
            coordinateModel.setX(coordinate.getX());
            coordinateModel.setY(coordinate.getY());
            return coordinateModel;
        }
    }

    public static RoleAnimationModel valueOf(RoleAnimation roleAnimation) {
        if (roleAnimation == null) {
            return null;
        }
        RoleAnimationModel roleAnimationModel = new RoleAnimationModel();
        roleAnimationModel.setFurnitureGroupId(roleAnimation.getFurnitureGroupId());
        roleAnimationModel.setZoomRatio(roleAnimation.getZoomRatio());
        roleAnimationModel.setRoleConfigs(roleAnimation.getRoleConfigs().stream().map(RoleConfigModel::valueOf).collect(Collectors.toList()));
        return roleAnimationModel;
    }
}

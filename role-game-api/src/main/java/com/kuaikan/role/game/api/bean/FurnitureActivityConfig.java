package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bo.ImageInfo;

/**
 * 家具活动配置表
 * <AUTHOR>
 * @TableName furniture_activity_config
 */
@Data
public class FurnitureActivityConfig implements Serializable {

    private static final long serialVersionUID = 6403568119359200634L;

    private Integer id;

    /**
     * 排序
     */
    private Integer orderId;

    /**
     * 名称
     */
    private String name;

    /**
     * 开始时间
     */
    private Long startAt;

    /**
     * 结束时间
     */
    private Long endAt;

    /**
     * 活动配置
     */
    private Config config;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 上下架状态
     * @see com.kuaikan.role.game.api.enums.CommonStatus
     */
    private Integer status;

    /**
     * 类型,1:限时,2:常驻
     * @see com.kuaikan.role.game.api.enums.FurnitureActivityType
     */
    private Integer type;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    @Data
    @Accessors(chain = true)
    public static class Config implements Serializable {

        private static final long serialVersionUID = 8023013067275884558L;
        // 导航图片
        private ImageInfo navigationImage;
        // 背景图片
        private ImageInfo backgroundImage;
        // 是否展示保底
        private boolean showGuarantee;
        // 规则ID
        private Integer ruleId;
        private String ruleDescription;
        // 兑换商城,活动链接url
        private List<String> exchangeMall;
        // 气泡
        private String reminderBubble;
        // 角色动画
        private RoleAnimation roleAnimation;
        // 首抽免费
        private boolean firstFree;
        // 首抽付费
        private FirstPrice firstPrice;
        // 累抽奖励
        private CumulativeDrawReward cumulativeDrawReward;
    }

    // FirstPrice
    @Accessors(chain = true)
    @Data
    public static class FirstPrice implements Serializable {

        private static final long serialVersionUID = 1L;
        private Integer kkbPrice;
        private String cornerText;
    }

    // RoleAnimation
    @Data
    @Accessors(chain = true)
    public static class RoleAnimation implements Serializable {

        private static final long serialVersionUID = -4252373242339262530L;

        /** 关联单个角色组的家具套组 */
        private Integer furnitureGroupId;

        /** 缩放比例 */
        private Double zoomRatio;

        /** 角色配置 */
        private List<CostumeBlindBoxActivity.RoleConfig> roleConfigs;
    }

    // CumulativeDrawReward
    @Data
    @Accessors(chain = true)
    public static class CumulativeDrawReward implements Serializable {

        private static final long serialVersionUID = 141702042327790658L;

        /** 奖励图片 */
        private ImageInfo rewardImage;
        /** 奖励文案 */
        private String rewardText;
        /** 奖励列表 */
        private List<CostumeBlindBoxActivity.Reward> rewards;
    }
}
package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bo.ImageInfo;

/**
 * 盲盒活动
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CostumeBlindBoxActivity implements Serializable {

    private static final long serialVersionUID = 1955405000647369571L;

    private int id;

    /**
     * 顺序
     */
    private int orderNum;

    /**
     * 名称
     */
    private String name;

    /**
     * 角色组 id
     */
    private int roleGroupId;

    /**
     * 开始时间
     */
    private long startAt;

    /**
     * 结束时间
     */
    private long endAt;

    /**
     * 状态
     * @see com.kuaikan.role.game.api.enums.BlindBoxActivityStatus
     */
    private int status;

    /**
     * 活动配置
     */
    private Config config;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    @Data
    @Accessors(chain = true)
    public static class Config implements Serializable {

        private static final long serialVersionUID = 2831804949376895732L;
        /**
         * 活动图片
         */
        private ImageInfo activityImage;

        private String spineKey;

        /**
         * 背景 spine
         */
        private SpineMaterial spine;

        /**
         * 抽取奖励按钮
         */
        private ImageInfo drawRewardBtn;

        private String drawCopywriting;

        private ImageInfo entryImage;

        private List<RoleConfig> roleConfigs;

        private Integer scaleRatio;

        private Integer freeCount;

        private List<Reward> rewards;

        /** 概率up装扮 */
        private UpCostume upCostume;

        /** 概率规则id
         * */
        private Integer defaultRuleId;

        /** 概率实验
         *  */
        private ExptRule exptRules;
    }

    @Data
    @Accessors(chain = true)
    public static class RoleConfig implements Serializable {

        private static final long serialVersionUID = -6157129372859122478L;

        private int id;

        private String name;

        private int costumeId;

        private String animation;

        private com.kuaikan.role.game.api.bean.Coordinate coordinate;
    }

    @Data
    @Accessors(chain = true)
    public static class Coordinate implements Serializable {

        private static final long serialVersionUID = -2101787984215788938L;
        private int x;
        private int y;
    }

    @Data
    @Accessors(chain = true)
    public static class Reward implements Serializable {

        private static final long serialVersionUID = 6442720873588190234L;

        private int needNum;

        private String name;

        /**
         * 奖励类型
         * @see com.kuaikan.role.game.api.enums.RewardType
         */
        private int type;

        private Integer rewardId;

        private Integer rewardNum;

        private ImageInfo rewardImage;

        /**
         * 付费后台奖励名称
         */
        private String awardName;

        /**
         * 付费后台奖池名称
         */
        private String activityName;

        /**
         * 卡片后台自选礼包ID
         */
        private Long prizeBagId;
    }

    @Data
    @Accessors(chain = true)
    public static class UpCostume implements Serializable {

        private static final long serialVersionUID = 4259433097146175320L;
        private List<Integer> upCostumeIds;
        private List<Content> contents;
    }

    @Data
    @Accessors(chain = true)
    public static class Content implements Serializable {

        private static final long serialVersionUID = -3971314813138860743L;
        private Integer type;
        private String content;
    }

    @Data
    @Accessors(chain = true)
    public static class ExptRule implements Serializable {

        private static final long serialVersionUID = -7079243275966346060L;
        private boolean exptOpen;
        private String exptFlag;
        private List<ExptGroup> exptGroups;
    }

    @Data
    @Accessors(chain = true)
    public static class ExptGroup implements Serializable {

        private static final long serialVersionUID = -4993860667429187871L;
        private String name;
        private Integer ruleId;
    }
}

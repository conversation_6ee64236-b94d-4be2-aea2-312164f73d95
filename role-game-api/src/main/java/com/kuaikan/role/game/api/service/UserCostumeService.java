package com.kuaikan.role.game.api.service;

import java.util.List;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity;
import com.kuaikan.role.game.api.model.ActivityCostumePreviewModel;
import com.kuaikan.role.game.api.model.ActivityDetailModel;
import com.kuaikan.role.game.api.model.BlindBoxActivityModel;
import com.kuaikan.role.game.api.model.BlindBoxActivityRecordModel;
import com.kuaikan.role.game.api.model.BlindBoxDrawResultModel;
import com.kuaikan.role.game.api.model.BlindBoxModel;
import com.kuaikan.role.game.api.model.GroupCostumeModel;
import com.kuaikan.role.game.api.model.RoleCostumeListModel;
import com.kuaikan.role.game.api.model.UserBlindBoxActivityInfoModel;
import com.kuaikan.role.game.api.model.UserRoleCostumeModel;
import com.kuaikan.role.game.api.rpc.param.ActivityPrizeParam;
import com.kuaikan.role.game.api.rpc.param.BlindBoxActivityParam;
import com.kuaikan.role.game.api.rpc.param.BlindBoxActivityRecordParam;
import com.kuaikan.role.game.api.rpc.param.BlindBoxParam;
import com.kuaikan.role.game.api.rpc.param.CostumeExposureParam;
import com.kuaikan.role.game.api.rpc.param.CostumePreviewParam;
import com.kuaikan.role.game.api.rpc.param.CurrentRolesCostumeParam;
import com.kuaikan.role.game.api.rpc.param.ListGroupCostumeInfoParam;
import com.kuaikan.role.game.api.rpc.param.MockLotteryParam;
import com.kuaikan.role.game.api.rpc.param.RolesCostumeParam;
import com.kuaikan.role.game.api.rpc.param.ListUserRoleCostumeParam;
import com.kuaikan.role.game.api.rpc.param.SwitchCostumeParam;
import com.kuaikan.role.game.api.rpc.param.UserCostumeParam;

/**
 * <AUTHOR>
 * @version 2024-03-01
 */
public interface UserCostumeService {

    RpcResult<List<UserRoleCostumeModel>> listUserRoleCostume(ListUserRoleCostumeParam param);

    RpcResult<List<GroupCostumeModel>> listGroupCostume(ListGroupCostumeInfoParam param);

    RpcResult<Void> preview(CostumePreviewParam param);

    RpcResult<Void> switchCostume(SwitchCostumeParam param);

    RpcResult<BlindBoxModel> getBlindBox(BlindBoxParam param);

    RpcResult<BlindBoxModel> getBlindBoxV2(BlindBoxParam param);

    RpcResult<BlindBoxDrawResultModel> getBlindBoxLotteryMock(MockLotteryParam param);

    @Deprecated
    RpcResult<RoleCostumeListModel> queryCurrentRolesCostume(CurrentRolesCostumeParam param);

    RpcResult<RoleCostumeListModel> queryCurrentRolesCostume(RolesCostumeParam param);

    RpcResult<RoleCostumeListModel> queryDefaultRolesCostume(RolesCostumeParam param);

    RpcResult<RoleCostumeListModel> queryRolesCostumeByConfig(RolesCostumeParam param);

    RpcResult<List<CostumeBlindBoxActivity>> queryBlindBoxActivityByGroupId(BlindBoxActivityParam param);

    RpcResult<BlindBoxActivityModel> queryBlindBoxActivityByActivityId(ActivityPrizeParam param);

    RpcResult<UserBlindBoxActivityInfoModel> queryBlindBoxActivityRewardRecordV2(ActivityPrizeParam param);

    @Deprecated
    RpcResult<List<BlindBoxActivityRecordModel>> queryBlindBoxActivityRewardRecord(ActivityPrizeParam param);

    RpcResult<List<BlindBoxActivityRecordModel>> queryBlindBoxActivityRewardRecordByUserId(ActivityPrizeParam param);

    RpcResult<ActivityCostumePreviewModel> getActivityCostumePreview(BlindBoxActivityParam param);

    RpcResult<ActivityCostumePreviewModel> getActivityCostumePreviewV2(BlindBoxActivityParam param);

    RpcResult<ActivityDetailModel> getBlindBoxActivityDetail(BlindBoxActivityParam param);

    RpcResult<ActivityDetailModel> getBlindBoxActivityDetailV2(BlindBoxActivityParam param);

    RpcResult<Void> insertUserBlindBoxActivityRecord(BlindBoxActivityRecordParam blindBoxActivityRecordParam);

    RpcResult<Void> costumeExposure(CostumeExposureParam costumeExposureParam);

    RpcResult<List<Integer>> queryUserCostumeIdList(UserCostumeParam param);
}

package com.kuaikan.role.game.api.apis;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.rpc.param.avg.AvgTextBatchUpdateParam;
import com.kuaikan.role.game.api.rpc.param.avg.AvgTextUpdateParam;
import com.kuaikan.role.game.api.rpc.result.avg.AvgChapterTextPage;
import com.kuaikan.role.game.api.rpc.result.avg.AvgTextVO;

@RestController
@RequestMapping("/api/roleGame/avgText")
public interface AvgTextApi {

    /**
     * 段落单条文本保存
     *
     * @param param 要更新的文本内容
     * @return 段落id
     */
    @PostMapping("/chapterText/chapterTextSave")
    RpcResult<Integer> chapterTextSave(@RequestBody AvgTextUpdateParam param);

    /**
     * 根据文本id查询文本配置
     *
     * @param chapterId 段落id
     * @param textId    文本id
     * @return 文本行配置
     */
    @GetMapping("/chapterText/findTextByTextId")
    RpcResult<AvgTextVO> findTextByTextId(@RequestParam("topicId") Integer topicId, @RequestParam("chapterId") Integer chapterId,
                                          @RequestParam("textId") String textId);

    /**
     * 根据段落id和文本id,新增空白文本行
     *
     * @param param 段落id+文本id
     * @return 文本id
     */
    @PostMapping("/chapterText/addBlankText")
    RpcResult<String> addBlankText(@RequestBody AvgTextUpdateParam param);

    /**
     * 根据段落id分页查询对应文本配置
     *
     * @param chapterId 段落id
     * @param roleName  角色名称,非必传
     * @param pageNum   页数,默认1
     * @param pageSize  条数,默认20
     * @param startNum  截取开始行数,不分页时才处理
     * @param endNum    截取终止行数,不分页时才处理
     */
    @GetMapping("/chapterText/queryPageByChapterId")
    RpcResult<AvgChapterTextPage> queryPageByChapterId(@RequestParam("topicId") Integer topicId, @RequestParam("chapterId") Integer chapterId,
                                                       @RequestParam(value = "textId", defaultValue = "") String textId,
                                                       @RequestParam(value="pageNum", defaultValue = "1") Integer pageNum,
                                                       @RequestParam(value="pageSize", defaultValue = "20") Integer pageSize,
                                                       @RequestParam(value = "roleName", defaultValue = "") String roleName,
                                                       @RequestParam(value = "startNum", required = false) Integer startNum,
                                                       @RequestParam(value = "endNum", required = false) Integer endNum);

    /**
     * 根据段落id和文本id,批量替换文本配置
     *
     * @param param 文本批量替换字段传参
     * @return 替换后的段落id
     */
    @PostMapping("/chapterText/updateBatchByTextIds")
    RpcResult<Integer> updateBatchByTextIds(@RequestBody AvgTextBatchUpdateParam param);

    /**
     * 根据段落id返回所有角色名称结果集
     *
     * @param chapterId 段落id
     * @return 角色名称结果集
     */
    @GetMapping("/chapterText/queryAllRoleNameByChapterId")
    RpcResult<List<String>> queryAllRoleNameByChapterId(@RequestParam("topicId") Integer topicId, @RequestParam("chapterId") Integer chapterId);

    /**
     * 单条text预览
     * @param param 单条text信息
     * @return 预览文本内容
     */
    @PostMapping("/chapterText/chapterTextPreview")
    RpcResult<String> chapterTextPreview(@RequestBody AvgTextUpdateParam param);
    /**
     * 根据段落id查询所有文本id
     *
     * @param chapterId 段落id
     * @return 所有文本id
     */
    @GetMapping("/chapterText/queryAllTextIdByChapterId")
    RpcResult<List<String>> queryAllTextIdByChapterId(@RequestParam("topicId") Integer topicId, @RequestParam("chapterId") Integer chapterId);

    /**
     * 刷新段落文件下载地址
     * @param chapterId 段落Id
     * @return 文件地址
     */
    @GetMapping("/file/avgFileDownloadUrl")
    RpcResult<String> avgFileDownloadUrl(@RequestParam("topicId") Integer topicId, @RequestParam("chapterId") Integer chapterId);
}

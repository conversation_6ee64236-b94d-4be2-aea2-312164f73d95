package com.kuaikan.role.game.backend.model;

import java.io.Serializable;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.Costume;
import com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity;
import com.kuaikan.role.game.api.model.ActivityDetailModel;
import com.kuaikan.role.game.api.model.BlindBoxModel;

/**
 * 盲盒活动详情
 */
@Data
@Accessors(chain = true)
public class ActivityDetailVO implements Serializable {

    private static final long serialVersionUID = -3887634833702005289L;

    private int id;

    private String name;

    private ActivityConfigVO config;

    private List<SimpleRoleCostumeVO> costumePreview;

    private PrizeInfoVO nextReward;

    private int usedFreeCount;

    private int totalFreeCount;

    private boolean showRedDot;

    private List<BlindBoxVO.ButtonVO> buttons;

    public static ActivityDetailVO valueOf(ActivityDetailModel activityDetailModel) {
        if (activityDetailModel == null) {
            return null;
        }

        ActivityDetailVO activityDetailVO = new ActivityDetailVO();

        CostumeBlindBoxActivity activity = activityDetailModel.getActivity();
        List<ActivityDetailModel.SimpleRoleCostume> costumePreview = activityDetailModel.getCostumePreview();

        ActivityDetailModel.PrizeInfo nextReward = activityDetailModel.getNextReward();
        List<BlindBoxModel.Button> buttons = activityDetailModel.getButtons();
        Map<Integer, Costume> costumeMap = activityDetailModel.getCostumeMap();

        activityDetailVO.setId(activity.getId());
        activityDetailVO.setName(activity.getName());
        activityDetailVO.setTotalFreeCount(activity.getConfig().getFreeCount());
        activityDetailVO.setConfig(ActivityConfigVO.valueOf(activity.getConfig(), costumeMap));
        activityDetailVO.setCostumePreview(costumePreview.stream().map(SimpleRoleCostumeVO::valueOf).collect(Collectors.toList()));
        activityDetailVO.setNextReward(PrizeInfoVO.valueOf(nextReward));
        activityDetailVO.setUsedFreeCount(activityDetailModel.getUsedFreeCount());
        activityDetailVO.setShowRedDot(activityDetailModel.isShowRedDot());
        activityDetailVO.setButtons(Optional.ofNullable(buttons)
                .orElse(Collections.emptyList())
                .stream()
                .sorted(Comparator.comparingInt(BlindBoxModel.Button::getAmount))
                .map(BlindBoxVO.ButtonVO::valueOf)
                .collect(Collectors.toList()));
        return activityDetailVO;
    }

    @Data
    @Accessors(chain = true)
    public static class ActivityConfigVO implements Serializable {

        private static final long serialVersionUID = -2403741181080107586L;

        private ImageInfoVO entryImage;

        private SpineMaterialVO spine;

        private ImageInfoVO drawRewardBtn;

        private List<ActivityRoleVO> roleList;

        private int scaleRatio;

        public static ActivityConfigVO valueOf(CostumeBlindBoxActivity.Config config, Map<Integer, Costume> costumeMap) {
            if (config == null) {
                return null;
            }
            ActivityConfigVO activityConfigVO = new ActivityConfigVO();
            activityConfigVO.setEntryImage(ImageInfoVO.valueOf(config.getActivityImage()));
            activityConfigVO.setSpine(SpineMaterialVO.valueOf(config.getSpine()));
            activityConfigVO.setDrawRewardBtn(ImageInfoVO.valueOf(config.getDrawRewardBtn()));
            activityConfigVO.setScaleRatio(config.getScaleRatio());

            List<CostumeBlindBoxActivity.RoleConfig> roleConfigs = config.getRoleConfigs();
            if (roleConfigs != null) {
                activityConfigVO.setRoleList(roleConfigs.stream().map(roleConfig -> {
                    int costumeId = roleConfig.getCostumeId();
                    Costume costume = costumeMap.get(costumeId);
                    if (costume == null) {
                        return null;
                    }

                    CostumeVO costumeVO = new CostumeVO();
                    costumeVO.setCostumeId(costumeId);
                    costumeVO.setName(costume.getName());
                    costumeVO.setThumbnail(ImageInfoVO.valueOf(costume.getConfig().getThumbnail()));

                    CostumeVO.AnimationConfig animationConfig = new CostumeVO.AnimationConfig();
                    animationConfig.setAnimation(roleConfig.getAnimation());
                    animationConfig.setSpine(SpineMaterialVO.valueOf(costume.getConfig().getActionSpineMaterial()));

                    costumeVO.setAnimationConfig(animationConfig);

                    return new ActivityRoleVO().setId(roleConfig.getId())
                            .setName(roleConfig.getName())
                            .setCostume(costumeVO)
                            .setCoordinate(new CoordinateVO().setX(roleConfig.getCoordinate().getX()).setY(roleConfig.getCoordinate().getY()));
                }).collect(Collectors.toList()));
            }
            return activityConfigVO;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class ActivityRoleVO implements Serializable {

        private static final long serialVersionUID = -4637319441244415074L;

        private int id;

        private String name;

        private CostumeVO costume;

        private CoordinateVO coordinate;

    }

    @Data
    @Accessors(chain = true)
    public static class CoordinateVO implements Serializable {

        private static final long serialVersionUID = -7202908907923738445L;
        private int x;
        private int y;
    }

    @Data
    @Accessors(chain = true)
    public static class CostumeVO implements Serializable {

        private static final long serialVersionUID = -563236845725891661L;

        private int costumeId;

        private String name;

        private ImageInfoVO thumbnail;

        private AnimationConfig animationConfig;

        @Data
        @Accessors(chain = true)
        private static class AnimationConfig {

            private String animation;

            private SpineMaterialVO spine;
        }
    }
}

package com.kuaikan.role.game.backend.service;

import static com.kuaikan.role.game.backend.util.ThreadPoolConfig.ROLE_GAME_SERVICE_POOL;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import com.kuaikan.comic.common.concurrent.AsyncWork;
import com.kuaikan.comic.common.utils.ThreadPoolFactory;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.model.BlindBoxModel;
import com.kuaikan.role.game.api.model.GroupCostumeModel;
import com.kuaikan.role.game.api.model.UserBlindBoxActivityInfoModel;
import com.kuaikan.role.game.api.model.UserRoleCostumeModel;
import com.kuaikan.role.game.api.rpc.param.ActivityPrizeParam;
import com.kuaikan.role.game.api.rpc.param.BlindBoxActivityRecordParam;
import com.kuaikan.role.game.api.rpc.param.BlindBoxParam;
import com.kuaikan.role.game.api.rpc.param.CostumePreviewParam;
import com.kuaikan.role.game.api.rpc.param.ListGroupCostumeInfoParam;
import com.kuaikan.role.game.api.rpc.param.ListUserRoleCostumeParam;
import com.kuaikan.role.game.api.service.UserCostumeService;

/**
 * <AUTHOR>
 * @version 2024-03-06
 */
@Service
@Slf4j
public class AsyncUserCostumeService {

    @Resource
    private UserCostumeService userCostumeService;

    public void listUserRoleCostume(int userId, int roleId, Consumer<List<UserRoleCostumeModel>> callback) {
        ListUserRoleCostumeParam param = new ListUserRoleCostumeParam().setUserId(userId).setRoleId(roleId);
        AsyncWork.supplyAsync(() -> {
            RpcResult<List<UserRoleCostumeModel>> rpcResult = userCostumeService.listUserRoleCostume(param);
            if (!rpcResult.isSuccess()) {
                log.warn("costume listUserRoleCostume failed, param:{}, result:{}", param, rpcResult);
                return new ArrayList<>();
            }
            return rpcResult.getData();

        }, ThreadPoolFactory.acquire(ROLE_GAME_SERVICE_POOL.getPoolName()), callback);
    }

    public void getGroupCostumeModel(int userId, int groupId, Consumer<List<GroupCostumeModel>> callback) {
        ListGroupCostumeInfoParam param = new ListGroupCostumeInfoParam().setUserId(userId).setGroupId(groupId);
        AsyncWork.supplyAsync(() -> {
            RpcResult<List<GroupCostumeModel>> rpcResult = userCostumeService.listGroupCostume(param);
            if (!rpcResult.isSuccess()) {
                log.warn("costume getGroupCostumeModel failed, param:{}, result:{}", param, rpcResult);
                return new ArrayList<>();
            }
            return rpcResult.getData();

        }, ThreadPoolFactory.acquire(ROLE_GAME_SERVICE_POOL.getPoolName()), callback);
    }

    public void preview(CostumePreviewParam param) {
        AsyncWork.supplyAsync(() -> {
            RpcResult<Void> rpcResult = userCostumeService.preview(param);
            if (!rpcResult.isSuccess()) {
                log.warn("costume preview failed, param:{}, result:{}", param, rpcResult);
                return null;
            }
            return rpcResult.getData();

        }, ThreadPoolFactory.acquire(ROLE_GAME_SERVICE_POOL.getPoolName()), null);
    }

    public void getBlindBox(BlindBoxParam param, Consumer<BlindBoxModel> callback) {
        AsyncWork.supplyAsync(() -> {
            RpcResult<BlindBoxModel> rpcResult = userCostumeService.getBlindBox(param);
            if (!rpcResult.isSuccess()) {
                log.warn("getBlindBox failed, result:{}", rpcResult);
                return null;
            }
            return rpcResult.getData();

        }, ThreadPoolFactory.acquire(ROLE_GAME_SERVICE_POOL.getPoolName()), callback);
    }

    public void getBlindBoxV2(BlindBoxParam param, Consumer<BlindBoxModel> callback) {
        AsyncWork.supplyAsync(() -> {
            RpcResult<BlindBoxModel> rpcResult = userCostumeService.getBlindBoxV2(param);
            if (!rpcResult.isSuccess()) {
                log.warn("getBlindBoxV2 failed, result:{}", rpcResult);
                return null;
            }
            return rpcResult.getData();

        }, ThreadPoolFactory.acquire(ROLE_GAME_SERVICE_POOL.getPoolName()), callback);
    }

    // 查询当前用户在当前活动中的抽奖记录,以及当前活动信息
    public void listUserBlindBoxActivityRecord(ActivityPrizeParam param, Consumer<UserBlindBoxActivityInfoModel> callback) {
        AsyncWork.supplyAsync(() -> {
            RpcResult<UserBlindBoxActivityInfoModel> rpcResult = userCostumeService.queryBlindBoxActivityRewardRecordV2(param);
            if (!rpcResult.isSuccess()) {
                log.warn("listUserBlindBoxActivityRecord failed, result:{}", rpcResult);
                return null;
            }
            return rpcResult.getData();

        }, ThreadPoolFactory.acquire(ROLE_GAME_SERVICE_POOL.getPoolName()), callback);
    }

    public void insertUserBlindBoxActivityRecord(BlindBoxActivityRecordParam blindBoxActivityRecordParam) {
        AsyncWork.supplyAsync(() -> {
            RpcResult<Void> rpcResult = userCostumeService.insertUserBlindBoxActivityRecord(blindBoxActivityRecordParam);
            if (!rpcResult.isSuccess()) {
                log.warn("insertUserBlindBoxActivityRecord failed, result:{}", rpcResult);
                return null;
            }
            return rpcResult.getData();

        }, ThreadPoolFactory.acquire(ROLE_GAME_SERVICE_POOL.getPoolName()), null);
    }
}

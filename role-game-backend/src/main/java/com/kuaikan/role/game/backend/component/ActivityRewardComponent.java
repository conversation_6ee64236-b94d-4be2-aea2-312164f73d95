package com.kuaikan.role.game.backend.component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import com.kuaikan.account.api.model.PassportContext;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.common.concurrent.Work;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.common.util.IpLocationHelper;
import com.kuaikan.game.gamecard.base.dto.PrizeBagDto;
import com.kuaikan.game.gamecard.base.dto.PrizeDto;
import com.kuaikan.game.gamecard.dubbo.dto.ClientInfoDTO;
import com.kuaikan.game.gamecard.prize.def.service.GameCardPrizeService;
import com.kuaikan.idgenerator.sdk.BizIdGenerator;
import com.kuaikan.role.game.api.bo.ImageInfo;
import com.kuaikan.role.game.api.enums.BlindBoxActivityType;
import com.kuaikan.role.game.api.enums.ClaimStatus;
import com.kuaikan.role.game.api.enums.PotionSourceType;
import com.kuaikan.role.game.api.enums.RewardType;
import com.kuaikan.role.game.api.enums.SpiritStoneEnum;
import com.kuaikan.role.game.api.model.BlindBoxActivityModel;
import com.kuaikan.role.game.api.model.BlindBoxActivityRecordModel;
import com.kuaikan.role.game.api.model.FurnitureGroupActivityModel;
import com.kuaikan.role.game.api.model.FurnitureGroupModel;
import com.kuaikan.role.game.api.model.RoleAnimationModel;
import com.kuaikan.role.game.api.model.RoleGroupIconActivityModel;
import com.kuaikan.role.game.api.model.UserBlindBoxActivityInfoModel;
import com.kuaikan.role.game.api.rpc.param.ActivityPrizeParam;
import com.kuaikan.role.game.api.rpc.param.AssignRewardParam;
import com.kuaikan.role.game.api.rpc.param.BlindBoxActivityRecordParam;
import com.kuaikan.role.game.api.service.AssignRewardService;
import com.kuaikan.role.game.api.service.FurnitureService;
import com.kuaikan.role.game.api.service.UserCostumeService;
import com.kuaikan.role.game.backend.bo.ActivityBackgroundDetails;
import com.kuaikan.role.game.backend.bo.ActivityPrizeBO;
import com.kuaikan.role.game.backend.model.ActivityPrizeVO;
import com.kuaikan.role.game.backend.model.BackGroundPageVo;
import com.kuaikan.role.game.backend.model.FurnitureGroupActivityVO;
import com.kuaikan.role.game.backend.model.ImageInfoVO;
import com.kuaikan.role.game.backend.model.PrizeBagVO;
import com.kuaikan.role.game.backend.model.PrizeInfoVO;
import com.kuaikan.role.game.backend.service.AsyncFurnitureService;
import com.kuaikan.role.game.backend.service.AsyncRewardOrderService;
import com.kuaikan.role.game.backend.service.AsyncUserCostumeService;

/**
 * <AUTHOR>
 * @date 2025/5/6 12:40
 * @description: 盲盒活动公共接口
 */

@Slf4j
@Component
public class ActivityRewardComponent {

    @Resource
    private FurnitureService furnitureService;
    @Resource
    private UserCostumeService userCostumeService;
    @Resource
    private AssignRewardService assignRewardService;
    @Resource
    private GameCardPrizeService gameCardPrizeService;
    @Resource
    private AsyncFurnitureService asyncFurnitureService;
    @Resource
    private AsyncUserCostumeService asyncUserCostumeService;
    @Resource
    private AsyncRewardOrderService asyncRewardOrderService;

    /** 获取盲盒活动奖励列表 */
    public BizResult<ActivityPrizeVO> getActivityPrizeList(int activityId, BlindBoxActivityType blindBoxActivityType) {
        final int userId = PassportContext.getUserId();
        ActivityPrizeParam param = new ActivityPrizeParam().setUserId(userId).setActivityId(activityId);
        ActivityPrizeBO prizeBO = new ActivityPrizeBO();
        fetchActivityRecords(param, blindBoxActivityType, prizeBO);
        return BizResult.success(ActivityPrizeVO.valueOf(prizeBO));
    }

    /** 获取背包奖励子列表 */
    public BizResult<PrizeBagVO> getPrizeBagPrizeList(int activityId, int lotteryCount, BlindBoxActivityType blindBoxActivityType) {
        final int userId = PassportContext.getUserId();
        ActivityPrizeParam param = new ActivityPrizeParam().setUserId(userId).setActivityId(activityId);

        RpcResult<BlindBoxActivityModel> activityResult = getActivityModel(param, blindBoxActivityType);
        if (!activityResult.isSuccess() || activityResult.getData() == null) {
            log.warn("getActivityPrizeList fail, userId:{}, activityId:{}", userId, activityId);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "获取盲盒活动奖励失败");
        }

        BlindBoxActivityModel.PrizeInfoModel currentPrizeInfo = findPrizeBagInfo(activityResult.getData(), lotteryCount);
        if (currentPrizeInfo == null || currentPrizeInfo.getPrizeBagId() == null) {
            log.warn("Prize bag not found, userId:{}, activityId:{}, lotteryCount:{}", userId, activityId, lotteryCount);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "查询奖励包配置不存在");
        }

        PrizeBagDto prizeBagDto = gameCardPrizeService.fetchPrizeBagInfo(userId, currentPrizeInfo.getPrizeBagId());
        if (prizeBagDto == null) {
            log.warn("Prize bag info not found, userId:{}, prizeBagId:{}", userId, currentPrizeInfo.getPrizeBagId());
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "查询奖励包信息不存在");
        }

        log.info("Successfully fetched prize bag info, userId:{}, prizeBagId:{}", userId, currentPrizeInfo.getPrizeBagId());
        return BizResult.success(
                PrizeBagVO.from(prizeBagDto, activityResult.getData().getUserCostumeIds(), activityResult.getData().getInteractiveFurnitureIds()));
    }

    /** 领取活动奖励,子奖励 */
    public BizResult<PrizeInfoVO> receiveActivityPrize(int activityId, int lotteryCount, Long subPrizeId, BlindBoxActivityType blindBoxActivityType) {
        final int userId = PassportContext.getUserId();
        ActivityPrizeParam activityPrizeParam = new ActivityPrizeParam().setUserId(userId).setActivityId(activityId);
        RpcResult<UserBlindBoxActivityInfoModel> blindBoxActivityInfoResult = getUserBlindBoxActivityInfoMode(activityPrizeParam, blindBoxActivityType);
        if (!blindBoxActivityInfoResult.isSuccess() || blindBoxActivityInfoResult.getData() == null) {
            log.warn("obtain  activity lottery count reward fail, userId:{}, activityId:{}, lotteryCount:{}", userId, activityId, lotteryCount);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "获取用户盲盒活动信息失败");
        }

        UserBlindBoxActivityInfoModel boxActivityInfoResultData = blindBoxActivityInfoResult.getData();
        BlindBoxActivityModel activityModel = boxActivityInfoResultData.getBlindBoxActivityModel();
        BlindBoxActivityModel.PrizeInfoModel currentPrizeInfo = findCurrentPrizeInfo(activityModel, lotteryCount);
        if (currentPrizeInfo == null) {
            log.warn("obtain blind box activity lottery count reward fail, userId:{}, activityId:{}, lotteryCount:{}", userId, activityId, lotteryCount);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "获取盲盒活动档位奖励失败");
        }

        List<BlindBoxActivityRecordModel> recordModels = boxActivityInfoResultData.getBlindBoxActivityRecordModels();
        if (CollectionUtils.isNotEmpty(recordModels) && recordModels.stream().anyMatch(recordModel -> recordModel.getLevelIndex() == lotteryCount)) {
            log.warn("obtain blind box activity lottery count reward fail, userId:{}, activityId:{}, lotteryCount:{}", userId, activityId, lotteryCount);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "用户当前档位奖励已经领取");
        }

        // Assign reward
        long blindBoxActivityBid = BizIdGenerator.getId();
        AssignRewardParam assignRewardParam = buildAssignRewardParam(userId, activityId, currentPrizeInfo, subPrizeId, blindBoxActivityType,
                blindBoxActivityBid);
        RpcResult<Void> rpcResult = assignRewardService.assignRewardAndRecord(assignRewardParam);
        if (!rpcResult.isSuccess()) {
            log.warn("claim blind box activity lottery count reward fail, userId:{}, activityId:{}, lotteryCount:{}, rpcResult:{}", userId, activityId,
                    lotteryCount, rpcResult);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "领取盲盒活动档位奖励失败");
        }

        // save record
        Pair<BlindBoxActivityRecordParam, BlindBoxActivityModel.PrizeInfoModel> activityRecordParamPair = buildActivityRecordParam(userId, activityId,
                activityModel.getActivityName(), blindBoxActivityBid, lotteryCount, currentPrizeInfo, subPrizeId);
        if (null == activityRecordParamPair) {
            log.error("获取礼包信息失败，userId={}, prizeBagId={}", userId, currentPrizeInfo.getPrizeBagId());
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "查询奖励包信息不存在");
        }

        if (BlindBoxActivityType.COSTUME_BLIND_BOX == blindBoxActivityType) {
            asyncUserCostumeService.insertUserBlindBoxActivityRecord(activityRecordParamPair.getLeft());
        } else {
            asyncFurnitureService.insertUserFurnitureActivityRecord(activityRecordParamPair.getLeft());
        }
        Work.complete();

        PrizeInfoVO prizeInfoVO = PrizeInfoVO.modelToVO(activityRecordParamPair.getRight());
        return BizResult.success(prizeInfoVO);
    }

    /**
     * 获取家具盲盒家具详情
     */
    public BizResult<FurnitureGroupActivityVO> getFurnitureGroupDetails(int activityId, BlindBoxActivityType blindBoxActivityType) {
        int userId = PassportContext.getUserId();
        ActivityPrizeParam param = new ActivityPrizeParam().setUserId(userId).setActivityId(activityId);
        // 查询盲盒活动中包含的家具组信息
        RpcResult<List<FurnitureGroupActivityModel>> furnitureGroupMapByActivityId = furnitureService.getFurnitureGroupDetails(param);
        if (!furnitureGroupMapByActivityId.isSuccess()) {
            log.warn("getFurnitureGroupDetails fail, userId:{}, activityId:{}", userId, activityId);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "获取家具组信息失败");
        }
        // 查询家具组对应的角色组(一件家具组对应多个角色组)
        RpcResult<Map<Integer, List<RoleGroupIconActivityModel>>> roleGroupMapByFurnitureId = furnitureService.getRoleGroupDetails(param);
        if (!roleGroupMapByFurnitureId.isSuccess()) {
            log.warn("getRoleGroupDetails fail, userId:{}, activityId:{}", userId, activityId);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "获取角色组信息失败");
        }
        // 获取活动规则
        RpcResult<String> activityRuleByRuleId = furnitureService.getActivityRuleByRuleId(activityId);
        if (!activityRuleByRuleId.isSuccess()) {
            log.warn("getActivityRuleByRuleId fail, userId:{}, activityId:{}", userId, activityId);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "获取活动规则失败");
        }
        List<FurnitureGroupActivityVO.FurnitureGroupVO> furnitureGroupVOS = coverToFurnitureGroupVO(furnitureGroupMapByActivityId.getData(), roleGroupMapByFurnitureId.getData());
        FurnitureGroupActivityVO furnitureGroupActivityVO = new FurnitureGroupActivityVO();
        furnitureGroupActivityVO.setFurnitureGroups(furnitureGroupVOS);
        furnitureGroupActivityVO.setRuleDescription(activityRuleByRuleId.getData());
        return BizResult.success(furnitureGroupActivityVO);
    }

    /**
     * 获取背景页信息
     */
    public BizResult<BackGroundPageVo> getBackgroundPage(int activityId, BlindBoxActivityType blindBoxActivityType) {
        int userId = PassportContext.getUserId();
        ActivityPrizeParam param = new ActivityPrizeParam().setUserId(userId).setActivityId(activityId);
        ActivityBackgroundDetails activityBackgroundDetails = new ActivityBackgroundDetails();
        // 异步调用
        getActivityBackgroundDetails(param, activityBackgroundDetails);
        List<FurnitureGroupModel> furnitureGroupModels = activityBackgroundDetails.getFurnitureGroupModels();
        RoleAnimationModel roleAnimationModel = activityBackgroundDetails.getRoleAnimationModel();
        if (furnitureGroupModels == null || roleAnimationModel == null){
            log.warn("getBackgroundPage fail, userId:{}, activityId:{}", userId, activityId);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "获取背景页信息失败");
        }
        BackGroundPageVo backGroundPageVo = buildBackGroundPageVo(furnitureGroupModels, roleAnimationModel);
        return BizResult.success(backGroundPageVo);
    }

    private BackGroundPageVo buildBackGroundPageVo(List<FurnitureGroupModel> furnitureGroups, RoleAnimationModel roleAnimationModel) {

        List<BackGroundPageVo.FurnitureActivityBackGroundVO> furnitureActivityBackGroundVOS = furnitureGroups.stream()
                .map(furnitureGroupModel -> {
                    BackGroundPageVo.FurnitureActivityBackGroundVO furnitureActivityBackGroundVO =
                            new BackGroundPageVo.FurnitureActivityBackGroundVO();
                    furnitureActivityBackGroundVO.setFurnitureGroupId(furnitureGroupModel.getId());
                    furnitureActivityBackGroundVO.setFurnitureGroupImage(ImageInfoVO.valueOf(furnitureGroupModel.getThumbnail()));
                    return furnitureActivityBackGroundVO;
                })
                .collect(Collectors.toList());
        // 背景图片
        ImageInfo backgroundImage = roleAnimationModel.getBackgroundImage();
        // 家具常驻池
        if (roleAnimationModel.getRoleConfigs() == null || roleAnimationModel.getRoleConfigs().isEmpty()) {
            // 返回只有背景和家具组，不带任何角色动画
            return new BackGroundPageVo()
                    .setBackgroundImage(ImageInfoVO.valueOf(backgroundImage))
                    .setFurnitureGroups(furnitureActivityBackGroundVOS);
        }

        BackGroundPageVo.RoleAnimationVO roleAnimationVO = new BackGroundPageVo.RoleAnimationVO();
        roleAnimationVO.setRoleGroupId(roleAnimationModel.getRoleGroupId());
        roleAnimationVO.setRoleGroupFurnituresId(roleAnimationModel.getFurnitureGroupId());
        roleAnimationVO.setZoomRatio(roleAnimationModel.getZoomRatio());
        roleAnimationVO.setRoleConfigs(roleAnimationModel.getRoleConfigs().stream()
                .map(roleConfigModel -> new BackGroundPageVo.RoleConfigVO().setId(roleConfigModel.getRoleId())
                        .setName(roleConfigModel.getRoleName())
                        .setCostumeId(roleConfigModel.getCostumeId())
                        .setAnimation(roleConfigModel.getAnimation())
                        .setCoordinate(new BackGroundPageVo.CoordinateVO()
                                .setX(roleConfigModel.getCoordinate().getX())
                                .setY(roleConfigModel.getCoordinate().getY()))).collect(Collectors.toList()));
        // 家具限定池
        return new BackGroundPageVo()
                .setBackgroundImage(ImageInfoVO.valueOf(backgroundImage))
                .setFurnitureGroups(furnitureActivityBackGroundVOS)
                .setRoleAnimation(roleAnimationVO);
    }

    private List<FurnitureGroupActivityVO.FurnitureGroupVO> coverToFurnitureGroupVO(List<FurnitureGroupActivityModel> furnitureGroup,
                                                                                    Map<Integer, List<RoleGroupIconActivityModel>> roleGroupMap) {
        if (CollectionUtils.isEmpty(furnitureGroup)) {
            return new ArrayList<>();
        }
        // 按照排序号和创建时间排序
        List<FurnitureGroupActivityModel> sortFurnitureGroup = furnitureGroup.stream()
                .sorted(Comparator.comparingInt(FurnitureGroupActivityModel::getOrderNum)
                        .thenComparing(FurnitureGroupActivityModel::getCreateTime, Comparator.reverseOrder()))
                .collect(Collectors.toList());
        List<FurnitureGroupActivityVO.FurnitureGroupVO> furnitureGroupVOS = new ArrayList<>();

        for (FurnitureGroupActivityModel furnitureGroupActivityModel : sortFurnitureGroup) {
            Integer furnitureGroupId = furnitureGroupActivityModel.getFurnitureGroupId();
            List<RoleGroupIconActivityModel> roleGroupModels = roleGroupMap.get(furnitureGroupId);
            FurnitureGroupActivityVO.FurnitureGroupVO furnitureGroupVO = new FurnitureGroupActivityVO.FurnitureGroupVO()
                    .setFurnitureGroupId(furnitureGroupId)
                    .setThumbnail(ImageInfoVO.valueOf(furnitureGroupActivityModel.getThumbnail()))
                    .setFurnitureGroupName(furnitureGroupActivityModel.getFurnitureGroupName());
            if (CollectionUtils.isNotEmpty(roleGroupModels)) {
                List<FurnitureGroupActivityVO.RoleGroupVO> roleGroupVos = roleGroupModels.stream().map(roleGroupIconActivityModel -> {
                    FurnitureGroupActivityVO.RoleGroupVO roleGroupVO = new FurnitureGroupActivityVO.RoleGroupVO();
                    roleGroupVO.setRoleGroupId(roleGroupIconActivityModel.getRoleGroupId());
                    roleGroupVO.setRoleName(roleGroupIconActivityModel.getRoleNames());
                    roleGroupVO.setRoleImage(roleGroupIconActivityModel.getImages().stream().map(ImageInfoVO::valueOf).collect(Collectors.toList()));
                    return roleGroupVO;
                }).collect(Collectors.toList());
                furnitureGroupVO.setRoleGroup(roleGroupVos);
            }

            List<FurnitureGroupActivityModel.FurnitureModel> furnitures = furnitureGroupActivityModel.getFurnitures();
            List<FurnitureGroupActivityVO.FurnitureVO> furnitureVOS = furnitures.stream().map(furnitureModel -> {
                FurnitureGroupActivityVO.FurnitureVO furnitureVO = new FurnitureGroupActivityVO.FurnitureVO();
                furnitureVO.setId(furnitureModel.getId());
                furnitureVO.setIcon(ImageInfoVO.valueOf(furnitureModel.getImage()));
                furnitureVO.setIsInteractive(furnitureModel.getIsInteractive());
                furnitureVO.setHasOwned(furnitureModel.getHasOwned());
                return furnitureVO;
            }).collect(Collectors.toList());

            furnitureGroupVO.setFurnitures(furnitureVOS);
            furnitureGroupVOS.add(furnitureGroupVO);
        }

        return furnitureGroupVOS;
    }

    private RpcResult<BlindBoxActivityModel> getActivityModel(ActivityPrizeParam param, BlindBoxActivityType type) {
        switch (type) {
            case COSTUME_BLIND_BOX:
                return userCostumeService.queryBlindBoxActivityByActivityId(param);
            case FURNITURE_BLIND_BOX:
                return furnitureService.queryFurnitureActivityById(param);
            default:
                log.warn("getActivityModel activity type error, userId:{}, blindBoxActivityType:{}, activityId:{}", param.getUserId(), type,
                        param.getActivityId());
                return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "盲盒活动类型错误");
        }
    }

    private RpcResult<UserBlindBoxActivityInfoModel> getUserBlindBoxActivityInfoMode(ActivityPrizeParam param, BlindBoxActivityType type) {
        switch (type) {
            case COSTUME_BLIND_BOX:
                return userCostumeService.queryBlindBoxActivityRewardRecordV2(param);
            case FURNITURE_BLIND_BOX:
                return furnitureService.queryFurnitureActivityRewardRecord(param);
            default:
                log.warn("getActivityRecordModelList activity type error, userId:{}, blindBoxActivityType:{}, activityId:{}", param.getUserId(), type,
                        param.getActivityId());
                return RpcResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "盲盒活动类型错误");
        }
    }

    private void fetchActivityRecords(ActivityPrizeParam param, BlindBoxActivityType type, ActivityPrizeBO prizeBO) {
        if (type == BlindBoxActivityType.COSTUME_BLIND_BOX) {
            asyncUserCostumeService.listUserBlindBoxActivityRecord(param, prizeBO::setUserBlindBoxActivityInfoModel);
            asyncRewardOrderService.listUserBlindBoxActivityOrder(param, prizeBO::setBlindBoxActivityOrderModels);
        } else {
            asyncFurnitureService.listUserFurnitureActivityRecord(param, prizeBO::setUserBlindBoxActivityInfoModel);
            asyncRewardOrderService.listUserFurnitureActivityOrder(param, prizeBO::setBlindBoxActivityOrderModels);
        }
        Work.complete();
    }

    private void getActivityBackgroundDetails(ActivityPrizeParam param, ActivityBackgroundDetails activityBackgroundDetails) {
        asyncFurnitureService.listFurnitureGroup(param, activityBackgroundDetails::setFurnitureGroupModels);
        asyncFurnitureService.listRoleAnimation(param, activityBackgroundDetails::setRoleAnimationModel);
        Work.complete();
    }

    private static AssignRewardParam buildAssignRewardParam(int userId, int activityId, BlindBoxActivityModel.PrizeInfoModel prizeInfo, Long subPrizeId,
                                                            BlindBoxActivityType blindBoxActivityType, long bid) {
        AssignRewardParam param = new AssignRewardParam().setUserId(userId)
                .setRelatedId(Optional.ofNullable(prizeInfo.getRewardId()).orElse(0))
                .setNum(Optional.ofNullable(prizeInfo.getRewardNum()).orElse(0))
                .setAwardSource(blindBoxActivityType == BlindBoxActivityType.COSTUME_BLIND_BOX
                        ? SpiritStoneEnum.BLIND_BOX.getType()
                        : SpiritStoneEnum.FURNITURE_ACTIVITY.getType())
                .setType(prizeInfo.getType())
                .setActivityName(Optional.ofNullable(prizeInfo.getActivityName()).orElse(""))
                .setAwardName(Optional.ofNullable(prizeInfo.getAwardName()).orElse(""))
                .setPrizeBagId(prizeInfo.getPrizeBagId())
                .setSubPrizeId(subPrizeId)
                .setClientInfoDTO(getClientInfoDTO())
                .setBid(bid);

        if (prizeInfo.getType() == RewardType.EMOTION_BOND_BOTTLE.getCode()) {
            param.setSourceType(blindBoxActivityType == BlindBoxActivityType.COSTUME_BLIND_BOX
                    ? PotionSourceType.BLIND_BOX.getCode()
                    : PotionSourceType.FURNITURE_ACTIVITY.getCode());
            param.setSourceId(String.valueOf(activityId));
        }

        return param;
    }

    // buildActivityRecordParam
    private Pair<BlindBoxActivityRecordParam, BlindBoxActivityModel.PrizeInfoModel> buildActivityRecordParam(int userId, int activityId, String activityName,
                                                                                                             long bid, int lotteryCount,
                                                                                                             BlindBoxActivityModel.PrizeInfoModel prizeInfo,
                                                                                                             Long subPrizeId) {
        BlindBoxActivityRecordParam param = new BlindBoxActivityRecordParam().setUserId(userId)
                .setActivityId(activityId)
                .setActivityName(activityName)
                .setBid(bid)
                .setLevelIndex(lotteryCount)
                .setPrizeId(Optional.ofNullable(prizeInfo.getRewardId()).orElse(0))
                .setPrizeName(prizeInfo.getName())
                .setNum(Optional.ofNullable(prizeInfo.getRewardNum()).orElse(0))
                .setStatus(ClaimStatus.CLAIMED.getCode());
        int rewardType = Optional.ofNullable(prizeInfo.getType()).orElse(0);
        if (RewardType.PRIZE_BAG.getCode() == rewardType) {
            PrizeBagDto prizeBagDto = gameCardPrizeService.fetchPrizeBagInfo(userId, prizeInfo.getPrizeBagId());
            if (prizeBagDto == null) {
                log.error("Prize bag not found, userId:{}, prizeBagId:{}", userId, prizeInfo.getPrizeBagId());
                return null;
            }
            PrizeDto subPrize = prizeBagDto.getPrizeList().stream().filter(p -> p.getPrizeId() == subPrizeId).findFirst().orElse(null);
            param.setSelectedPrize(subPrize != null ? subPrize.getName() : null);
            prizeInfo = BlindBoxActivityModel.PrizeInfoModel.valueOf(subPrize);
        }

        return Pair.of(param, prizeInfo);
    }

    private static BlindBoxActivityModel.PrizeInfoModel findPrizeBagInfo(BlindBoxActivityModel activityModel, int lotteryCount) {
        return activityModel.getExtraReward()
                .stream()
                .filter(extraReward -> RewardType.PRIZE_BAG.getCode() == extraReward.getPrizeInfo().getType() && extraReward.getLotteryCount() == lotteryCount)
                .map(BlindBoxActivityModel.ExtraRewardModel::getPrizeInfo)
                .findFirst()
                .orElse(null);
    }

    private static BlindBoxActivityModel.PrizeInfoModel findCurrentPrizeInfo(BlindBoxActivityModel activityModel, int lotteryCount) {
        return activityModel.getExtraReward()
                .stream()
                .filter(extraRewardModel -> extraRewardModel.getLotteryCount() == lotteryCount)
                .map(BlindBoxActivityModel.ExtraRewardModel::getPrizeInfo)
                .findFirst()
                .orElse(null);
    }

    @NotNull
    private static ClientInfoDTO getClientInfoDTO() {
        ClientInfoDTO clientInfoDTO = new ClientInfoDTO();
        clientInfoDTO.setUserId(PassportContext.getUserId());
        clientInfoDTO.setXDeviceHeader(PassportContext.getXDevice());
        clientInfoDTO.setUserAgent(PassportContext.getUserAgent());
        clientInfoDTO.setDeviceRegisterTime(PassportContext.getDeviceRegisterTime());
        clientInfoDTO.setLocationInfo(IpLocationHelper.getIpLocation(PassportContext.getIp()));
        clientInfoDTO.setVisitor(PassportContext.isVisitor());
        return clientInfoDTO;
    }
}
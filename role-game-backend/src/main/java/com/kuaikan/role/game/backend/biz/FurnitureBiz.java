package com.kuaikan.role.game.backend.biz;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.kuaikan.account.api.model.PassportContext;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.enums.BlindBoxActivityType;
import com.kuaikan.role.game.api.model.FurnitureActivityModel;
import com.kuaikan.role.game.api.model.FurnitureActivityPreviewModel;
import com.kuaikan.role.game.api.model.FurnitureBlindBoxLotteryRecordModel;
import com.kuaikan.role.game.api.model.GuaranteeRemainDrawsResult;
import com.kuaikan.role.game.api.model.SimpleFurnitureActivityModel;
import com.kuaikan.role.game.api.rpc.param.FurnitureActivityDrawListQueryParam;
import com.kuaikan.role.game.api.rpc.param.FurnitureActivityQueryParam;
import com.kuaikan.role.game.api.service.FurnitureService;
import com.kuaikan.role.game.backend.component.ActivityRewardComponent;
import com.kuaikan.role.game.backend.model.ActivityPrizeVO;
import com.kuaikan.role.game.backend.model.BackGroundPageVo;
import com.kuaikan.role.game.backend.model.FurnitureActivityPreviewVO;
import com.kuaikan.role.game.backend.model.FurnitureActivityVO;
import com.kuaikan.role.game.backend.model.FurnitureBlindBoxLotteryRecordVO;
import com.kuaikan.role.game.backend.model.FurnitureGroupActivityVO;
import com.kuaikan.role.game.backend.model.GuaranteeRemainDrawsVO;
import com.kuaikan.role.game.backend.model.PrizeBagVO;
import com.kuaikan.role.game.backend.model.PrizeInfoVO;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class FurnitureBiz {

    @Resource
    private FurnitureService furnitureService;
    @Resource
    private ActivityRewardComponent activityRewardComponent;

    public BizResult<FurnitureActivityVO> activityInfo(int activityId) {
        int userId = PassportContext.getUserId();
        FurnitureActivityQueryParam queryParam = new FurnitureActivityQueryParam().setUserId(userId).setActivityId(activityId);
        RpcResult<FurnitureActivityModel> rpcResult = furnitureService.getActivityInfo(queryParam);
        if (!rpcResult.isSuccess()) {
            return BizResult.result(rpcResult.getCode(), rpcResult.getMessage());
        }
        return BizResult.success(FurnitureActivityVO.valueOf(rpcResult.getData()));
    }

    public BizResult<List<FurnitureBlindBoxLotteryRecordVO>> drawList(int activityId) {
        int userId = PassportContext.getUserId();
        FurnitureActivityDrawListQueryParam queryParam = new FurnitureActivityDrawListQueryParam().setUserId(userId).setActivityId(activityId);
        RpcResult<List<FurnitureBlindBoxLotteryRecordModel>> rpcResult = furnitureService.drawList(queryParam);
        if (!rpcResult.isSuccess()) {
            return BizResult.result(rpcResult.getCode(), rpcResult.getMessage());
        }
        List<FurnitureBlindBoxLotteryRecordVO> vos = rpcResult.getData().stream().map(FurnitureBlindBoxLotteryRecordVO::valueOf).collect(Collectors.toList());
        return BizResult.success(vos);
    }

    public BizResult<FurnitureActivityPreviewVO> getOnlineActivityPreviewList() {
        RpcResult<FurnitureActivityPreviewModel> activityPreviewModelRpcResult = furnitureService.getOnlineActivityPreviewList();
        if (!activityPreviewModelRpcResult.isSuccess()) {
            return BizResult.result(activityPreviewModelRpcResult.getCode(), "获取家具活动信息失败");
        }
        if (null == activityPreviewModelRpcResult.getData() || CollectionUtils.isEmpty(
                activityPreviewModelRpcResult.getData().getSimpleFurnitureActivityModels())) {
            return BizResult.success();
        }
        FurnitureActivityPreviewVO furnitureActivityPreviewVO = new FurnitureActivityPreviewVO();
        List<SimpleFurnitureActivityModel> simpleActivityModels = Optional.ofNullable(activityPreviewModelRpcResult.getData()).getData().getSimpleFurnitureActivityModels();
        furnitureActivityPreviewVO.setActivityList(
                simpleActivityModels.stream().map(FurnitureActivityPreviewVO.SimpleFurnitureActivityVO::valueOf).collect(Collectors.toList()));
        return BizResult.success(furnitureActivityPreviewVO);
    }

    public BizResult<ActivityPrizeVO> getActivityPrizeList(int activityId) {
        return activityRewardComponent.getActivityPrizeList(activityId, BlindBoxActivityType.FURNITURE_BLIND_BOX);
    }

    public BizResult<PrizeBagVO> getPrizeBagPrizeList(int activityId, int lotteryCount) {
        return activityRewardComponent.getPrizeBagPrizeList(activityId, lotteryCount, BlindBoxActivityType.FURNITURE_BLIND_BOX);
    }

    public BizResult<PrizeInfoVO> receiveActivityPrize(int activityId, int lotteryCount, Long subPrizeId) {
        return activityRewardComponent.receiveActivityPrize(activityId, lotteryCount, subPrizeId, BlindBoxActivityType.FURNITURE_BLIND_BOX);
    }

    public BizResult<GuaranteeRemainDrawsVO> getRemainingDraws(Integer activityId) {
        int userId = PassportContext.getUserId();
        // 默认常驻活动
        if (activityId == null) {
            activityId = 0;
        }
        RpcResult<GuaranteeRemainDrawsResult> rpcResult = furnitureService.getUserGuaranteeRemainDraws(userId, activityId);
        if (!rpcResult.isSuccess()){
            return BizResult.result(rpcResult.getCode(), "获取保底信息失败");
        }
        GuaranteeRemainDrawsResult result = rpcResult.getData();
        return BizResult.success(GuaranteeRemainDrawsVO.valueOf(result.getRemainDraws(), result.isShowGuarantee()));
    }

    public BizResult<FurnitureGroupActivityVO> getFurnitureGroupDetails(int activityId) {
        return activityRewardComponent.getFurnitureGroupDetails(activityId, BlindBoxActivityType.FURNITURE_BLIND_BOX);
    }

    public BizResult<BackGroundPageVo> getBackgroundPage(int activityId) {
        return activityRewardComponent.getBackgroundPage(activityId, BlindBoxActivityType.FURNITURE_BLIND_BOX);
    }
}